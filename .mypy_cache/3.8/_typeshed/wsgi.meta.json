{"data_mtime": 1748744171, "dep_lines": [9, 7, 8, 10, 11, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "sys", "_typeshed", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "types"], "hash": "3431499acf002effa9ff444f09c4b66b801b27c2", "id": "_typeshed.wsgi", "ignore_all": true, "interface_hash": "db2e58f645ed1f2f47e50de799d86fa5d7313469", "mtime": 1748042741, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/mypy/typeshed/stdlib/_typeshed/wsgi.pyi", "plugin_data": null, "size": 1637, "suppressed": [], "version_id": "1.15.0"}