{".class": "MypyFile", "_fullname": "ast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.AST", "name": "AST", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.AST", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ast.AST._attributes", "name": "_attributes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ast.AST._fields", "name": "_fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Add", "name": "Add", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Add", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Add", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "And": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.boolop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.And", "name": "And", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.And", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.And", "ast.boolop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnnAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON>.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 4], "arg_names": ["self", "target", "annotation", "value", "simple", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Ann<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AnnAssign.annotation", "name": "annotation", "type": "ast.expr"}}, "simple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AnnAssign.simple", "name": "simple", "type": "builtins.int"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AnnAssign.target", "name": "target", "type": {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AnnAssign.value", "name": "value", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Assert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON>", "name": "Assert", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON>", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "test", "msg", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Assert.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "test", "msg", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON>", "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Assert", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Assert.msg", "name": "msg", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Assert.test", "name": "test", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Assign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Assign", "name": "Assign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Assign", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Assign", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "targets", "value", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Assign.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "targets", "value", "type_comment", "kwargs"], "arg_types": ["ast.Assign", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "ast.expr", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Assign", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Assign.targets", "name": "targets", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Assign.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Assign.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.AsyncFor", "name": "AsyncFor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.AsyncFor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.AsyncFor", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.AsyncFor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "arg_types": ["ast.AsyncFor", "ast.expr", "ast.expr", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFor.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFor.iter", "name": "iter", "type": "ast.expr"}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFor.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFor.target", "name": "target", "type": "ast.expr"}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFor.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFunctionDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.AsyncFunctionDef", "name": "AsyncFunctionDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.AsyncFunctionDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.AsyncFunctionDef", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.AsyncFunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "kwargs"], "arg_types": ["ast.AsyncFunctionDef", "builtins.str", "ast.arguments", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFunctionDef.args", "name": "args", "type": "ast.arguments"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFunctionDef.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decorator_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFunctionDef.decorator_list", "name": "decorator_list", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFunctionDef.name", "name": "name", "type": "builtins.str"}}, "returns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFunctionDef.returns", "name": "returns", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncFunctionDef.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncWith": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.AsyncWith", "name": "AsyncWith", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.AsyncWith", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.AsyncWith", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.AsyncWith.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "arg_types": ["ast.AsyncWith", {".class": "Instance", "args": ["ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncWith", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncWith.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncWith.items", "name": "items", "type": {".class": "Instance", "args": ["ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AsyncWith.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Attribute", "name": "Attribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Attribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Attribute", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "attr", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Attribute.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "attr", "ctx", "kwargs"], "arg_types": ["ast.Attribute", "ast.expr", "builtins.str", "ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Attribute", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Attribute.attr", "name": "attr", "type": "builtins.str"}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Attribute.ctx", "name": "ctx", "type": "ast.expr_context"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Attribute.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AugAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Aug<PERSON><PERSON>", "name": "AugAssign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Aug<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Aug<PERSON><PERSON>", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "target", "op", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.AugAssign.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "target", "op", "value", "kwargs"], "arg_types": ["ast.Aug<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}, "ast.operator", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AugAssign", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AugAssign.op", "name": "op", "type": "ast.operator"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AugAssign.target", "name": "target", "type": {".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.AugAssign.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AugLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.AugLoad", "name": "AugLoad", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.AugLoad is deprecated: Deprecated since Python 3.9. Unused in Python 3.", "flags": [], "fullname": "ast.AugLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.AugLoad", "ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AugStore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.AugStore", "name": "AugStore", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.AugStore is deprecated: Deprecated since Python 3.9. Unused in Python 3.", "flags": [], "fullname": "ast.AugStore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.AugStore", "ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Await": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>t", "name": "Await", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>t", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>t", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Await.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>t", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Await", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Await.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BinOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.BinOp", "name": "BinOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.BinOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.BinOp", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "op", "right", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.BinOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "op", "right", "kwargs"], "arg_types": ["ast.BinOp", "ast.expr", "ast.operator", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BinOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.BinOp.left", "name": "left", "type": "ast.expr"}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.BinOp.op", "name": "op", "type": "ast.operator"}}, "right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.BinOp.right", "name": "right", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitAnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.BitAnd", "name": "BitAnd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.BitAnd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.BitAnd", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitOr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.BitOr", "name": "BitOr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.BitOr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.BitOr", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitXor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.BitXor", "name": "BitXor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.BitXor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.BitXor", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoolOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Bool<PERSON>p", "name": "BoolOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Bool<PERSON>p", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Bool<PERSON>p", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "values", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.BoolOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "values", "kwargs"], "arg_types": ["ast.Bool<PERSON>p", "ast.boolop", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoolOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.BoolOp.op", "name": "op", "type": "ast.boolop"}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.BoolOp.values", "name": "values", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Break": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Break", "name": "Break", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Break", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Break", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.<PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": "ast._ABC", "defn": {".class": "ClassDef", "fullname": "ast.By<PERSON>", "name": "Bytes", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Bytes is deprecated: Replaced by ast.Constant; removed in Python 3.14", "flags": [], "fullname": "ast.By<PERSON>", "has_param_spec_type": false, "metaclass_type": "ast._ABC", "metadata": {}, "module_name": "ast", "mro": ["ast.By<PERSON>", "ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "s": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Bytes.s", "name": "s", "type": "builtins.bytes"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Bytes.value", "name": "value", "type": "builtins.bytes"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Call", "name": "Call", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Call", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Call", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "func", "args", "keywords", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Call.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "func", "args", "keywords", "kwargs"], "arg_types": ["ast.Call", "ast.expr", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Call", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Call.args", "name": "args", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Call.func", "name": "func", "type": "ast.expr"}}, "keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Call.keywords", "name": "keywords", "type": {".class": "Instance", "args": ["ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.ClassDef", "name": "ClassDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.ClassDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.ClassDef", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "bases", "keywords", "body", "decorator_list", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.ClassDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "bases", "keywords", "body", "decorator_list", "kwargs"], "arg_types": ["ast.ClassDef", "builtins.str", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClassDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "bases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ClassDef.bases", "name": "bases", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ClassDef.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decorator_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ClassDef.decorator_list", "name": "decorator_list", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ClassDef.keywords", "name": "keywords", "type": {".class": "Instance", "args": ["ast.keyword"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ClassDef.name", "name": "name", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Comp<PERSON>", "name": "Compare", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Comp<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Comp<PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "ops", "comparators", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Compare.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "left", "ops", "comparators", "kwargs"], "arg_types": ["ast.Comp<PERSON>", "ast.expr", {".class": "Instance", "args": ["ast.cmpop"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Compare", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "comparators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Compare.comparators", "name": "comparators", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Compare.left", "name": "left", "type": "ast.expr"}}, "ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Compare.ops", "name": "ops", "type": {".class": "Instance", "args": ["ast.cmpop"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Constant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "Constant", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "kind", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Constant.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "kind", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Constant", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Constant.kind", "name": "kind", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "n": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Constant.n", "name": "n", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": true}}}, "s": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Constant.s", "name": "s", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Constant.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Continue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Continue", "name": "Continue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Continue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Continue", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Del": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON>", "name": "Del", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON>", "ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Delete", "name": "Delete", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Delete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Delete", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "targets", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Delete.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "targets", "kwargs"], "arg_types": ["ast.Delete", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Delete", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Delete.targets", "name": "targets", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Dict", "name": "Dict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Dict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Dict", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "keys", "values", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Dict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "keys", "values", "kwargs"], "arg_types": ["ast.Dict", {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Dict.keys", "name": "keys", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Dict.values", "name": "values", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DictComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.DictComp", "name": "DictComp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.DictComp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.DictComp", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "key", "value", "generators", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.DictComp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "key", "value", "generators", "kwargs"], "arg_types": ["ast.DictComp", "ast.expr", "ast.expr", {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DictComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.DictComp.generators", "name": "generators", "type": {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.DictComp.key", "name": "key", "type": "ast.expr"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.DictComp.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Div": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Div", "name": "Div", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Div", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Div", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Ellipsis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.<PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": "ast._ABC", "defn": {".class": "ClassDef", "fullname": "ast.<PERSON>", "name": "El<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Ellipsis is deprecated: Replaced by ast.Constant; removed in Python 3.14", "flags": [], "fullname": "ast.<PERSON>", "has_param_spec_type": false, "metaclass_type": "ast._ABC", "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON>", "ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Eq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Eq", "name": "Eq", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Eq", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Eq", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.except<PERSON>ler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON><PERSON>", "ast.except<PERSON>ler", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 4], "arg_names": ["self", "type", "name", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ExceptHandler.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ExceptHandler.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ExceptHandler.type", "name": "type", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Expr", "name": "Expr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Expr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Expr", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Expr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["ast.Expr", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Expr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Expr.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Expression", "name": "Expression", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Expression", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Expression", "ast.mod", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Expression.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["ast.Expression", "ast.expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Expression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Expression.body", "name": "body", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtSlice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.slice"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.ExtSlice", "name": "ExtSlice", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.ExtSlice is deprecated: Deprecated since Python 3.9. Use ast.Tuple instead.", "flags": [], "fullname": "ast.ExtSlice", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.ExtSlice", "ast.slice", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "dims", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.ExtSlice.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "dims", "kwargs"], "arg_types": ["ast.ExtSlice", {".class": "Instance", "args": ["ast.slice"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": "ast._SliceAttributes", "items": [], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExtSlice", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ExtSlice.dims", "name": "dims", "type": {".class": "Instance", "args": ["ast.slice"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FloorDiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.FloorDiv", "name": "FloorDiv", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.FloorDiv", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.FloorDiv", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "For": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.For", "name": "For", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.For", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.For", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.For.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "target", "iter", "body", "orelse", "type_comment", "kwargs"], "arg_types": ["ast.For", "ast.expr", "ast.expr", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of For", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.For.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.For.iter", "name": "iter", "type": "ast.expr"}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.<PERSON>.ore<PERSON>e", "name": "orelse", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.For.target", "name": "target", "type": "ast.expr"}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.For.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormattedValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.FormattedValue", "name": "FormattedValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.FormattedValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.FormattedValue", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "conversion", "format_spec", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.FormattedValue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "conversion", "format_spec", "kwargs"], "arg_types": ["ast.FormattedValue", "ast.expr", "builtins.int", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormattedValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "conversion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FormattedValue.conversion", "name": "conversion", "type": "builtins.int"}}, "format_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FormattedValue.format_spec", "name": "format_spec", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FormattedValue.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.FunctionDef", "name": "FunctionDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.FunctionDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.FunctionDef", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.FunctionDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "name", "args", "body", "decorator_list", "returns", "type_comment", "kwargs"], "arg_types": ["ast.FunctionDef", "builtins.str", "ast.arguments", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionDef.args", "name": "args", "type": "ast.arguments"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionDef.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decorator_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionDef.decorator_list", "name": "decorator_list", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionDef.name", "name": "name", "type": "builtins.str"}}, "returns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionDef.returns", "name": "returns", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionDef.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.FunctionType", "name": "FunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.FunctionType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.FunctionType", "ast.mod", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "argtypes", "returns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.FunctionType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "argtypes", "returns"], "arg_types": ["ast.FunctionType", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "ast.expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FunctionType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "argtypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionType.argtypes", "name": "argtypes", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "returns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.FunctionType.returns", "name": "returns", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GeneratorExp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.GeneratorExp", "name": "GeneratorExp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.GeneratorExp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.GeneratorExp", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.GeneratorExp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "arg_types": ["ast.GeneratorExp", "ast.expr", {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GeneratorExp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "elt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.GeneratorExp.elt", "name": "elt", "type": "ast.expr"}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.GeneratorExp.generators", "name": "generators", "type": {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Global": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Global", "name": "Global", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Global", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Global", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Global.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "arg_types": ["ast.Global", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Global", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Global.names", "name": "names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Gt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Gt", "name": "Gt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Gt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Gt", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GtE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.GtE", "name": "GtE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.GtE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.GtE", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "If": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.If", "name": "If", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.If", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.If", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.If.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "arg_types": ["ast.If", "ast.expr", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of If", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.If.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.<PERSON>.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.If.test", "name": "test", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IfExp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.IfExp", "name": "IfExp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.IfExp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.IfExp", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.IfExp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "arg_types": ["ast.IfExp", "ast.expr", "ast.expr", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IfExp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.IfExp.body", "name": "body", "type": "ast.expr"}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.IfExp.orelse", "name": "orelse", "type": "ast.expr"}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.IfExp.test", "name": "test", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Import": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.I<PERSON>rt", "name": "Import", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.I<PERSON>rt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.I<PERSON>rt", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Import.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "arg_types": ["ast.I<PERSON>rt", {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Import", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Import.names", "name": "names", "type": {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImportFrom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.ImportFrom", "name": "ImportFrom", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.ImportFrom", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.ImportFrom", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ast.ImportFrom.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.ImportFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 4], "arg_names": ["self", "module", "names", "level", "kwargs"], "arg_types": ["ast.ImportFrom", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ImportFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ImportFrom.level", "name": "level", "type": "builtins.int"}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ImportFrom.module", "name": "module", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ImportFrom.names", "name": "names", "type": {".class": "Instance", "args": ["ast.alias"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "In": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.In", "name": "In", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.In", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.In", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.slice"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Index", "name": "Index", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Index is deprecated: Deprecated since Python 3.9. Use the index value directly instead.", "flags": [], "fullname": "ast.Index", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Index", "ast.slice", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Index.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["ast.Index", "ast.expr", {".class": "TypedDictType", "fallback": "ast._SliceAttributes", "items": [], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Index", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Index.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Interactive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Interactive", "name": "Interactive", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Interactive", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Interactive", "ast.mod", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Interactive.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["ast.Interactive", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Interactive", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Interactive.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Invert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Invert", "name": "Invert", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Invert", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Invert", "ast.unaryop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Is": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Is", "name": "Is", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Is", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Is", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IsNot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.IsNot", "name": "IsNot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.IsNot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.IsNot", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JoinedStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.JoinedStr", "name": "JoinedStr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.JoinedStr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.JoinedStr", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "values", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.JoinedStr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "values", "kwargs"], "arg_types": ["ast.JoinedStr", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JoinedStr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.JoinedStr.values", "name": "values", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LShift": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.LShift", "name": "LShift", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.LShift", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.LShift", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "Lambda", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "args", "body", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Lambda.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "args", "body", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", "ast.arguments", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Lambda", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Lambda.args", "name": "args", "type": "ast.arguments"}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Lambda.body", "name": "body", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.List", "name": "List", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.List", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.List", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.List.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "arg_types": ["ast.List", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of List", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.List.ctx", "name": "ctx", "type": "ast.expr_context"}}, "elts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.List.elts", "name": "elts", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.ListComp", "name": "ListComp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.ListComp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.ListComp", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.ListComp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "arg_types": ["ast.ListComp", "ast.expr", {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "elt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ListComp.elt", "name": "elt", "type": "ast.expr"}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.ListComp.generators", "name": "generators", "type": {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Load": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Load", "name": "Load", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Load", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Load", "ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Lt", "name": "Lt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Lt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Lt", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LtE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.LtE", "name": "LtE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.LtE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.LtE", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatMult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON>", "name": "MatMult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON>", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON>d", "name": "Mod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON>d", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON>d", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON>", "ast.mod", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "type_ignores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Module.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "type_ignores"], "arg_types": ["ast.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.TypeIgnore"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Module", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Module.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_ignores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Module.type_ignores", "name": "type_ignores", "type": {".class": "Instance", "args": ["ast.TypeIgnore"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "<PERSON>lt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Name", "name": "Name", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Name", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Name", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "id", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Name.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "id", "ctx", "kwargs"], "arg_types": ["ast.Name", "builtins.str", "ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Name", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Name.ctx", "name": "ctx", "type": "ast.expr_context"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Name.id", "name": "id", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NameConstant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.<PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": "ast._ABC", "defn": {".class": "ClassDef", "fullname": "ast.NameConstant", "name": "NameConstant", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.NameConstant is deprecated: Replaced by ast.Constant; removed in Python 3.14", "flags": [], "fullname": "ast.NameConstant", "has_param_spec_type": false, "metaclass_type": "ast._ABC", "metadata": {}, "module_name": "ast", "mro": ["ast.NameConstant", "ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedExpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.NamedExpr", "name": "NamedExpr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.NamedExpr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.NamedExpr", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NamedExpr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "value", "kwargs"], "arg_types": ["ast.NamedExpr", "ast.Name", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NamedExpr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.NamedExpr.target", "name": "target", "type": "ast.Name"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.NamedExpr.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodeTransformer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.NodeVisitor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.NodeTransformer", "name": "NodeTransformer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.NodeTransformer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.NodeTransformer", "ast.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "generic_visit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeTransformer.generic_visit", "name": "generic_visit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeTransformer", "ast.AST"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generic_visit of NodeTransformer", "ret_type": "ast.AST", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodeVisitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.NodeVisitor", "name": "NodeVisitor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "generic_visit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.generic_visit", "name": "generic_visit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AST"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generic_visit of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit", "name": "visit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AST"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Add", "name": "visit_Add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Add"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Add of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_And": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_And", "name": "visit_And", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.And"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_And of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AnnAssign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AnnAssign", "name": "visit_Ann<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AnnAssign of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Assert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Assert", "name": "visit_Assert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Assert of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Assign", "name": "visit_Assign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Assign"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Assign of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AsyncFor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AsyncFor", "name": "visit_AsyncFor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AsyncFor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AsyncFor of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AsyncFunctionDef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AsyncFunctionDef", "name": "visit_AsyncFunctionDef", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AsyncFunctionDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AsyncFunctionDef of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AsyncWith": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AsyncWith", "name": "visit_AsyncWith", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AsyncWith"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AsyncWith of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Attribute", "name": "visit_Attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Attribute"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Attribute of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AugAssign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AugAssign", "name": "visit_AugAssign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Aug<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AugAssign of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AugLoad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AugLoad", "name": "visit_AugLoad", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AugLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AugLoad of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AugStore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_AugStore", "name": "visit_AugStore", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.AugStore"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AugStore of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Await": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Await", "name": "visit_Await", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>t"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Await of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_BinOp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_BinOp", "name": "visit_BinOp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.BinOp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_BinOp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_BitAnd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_BitAnd", "name": "visit_BitAnd", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.BitAnd"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_BitAnd of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_BitOr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_BitOr", "name": "visit_BitOr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.BitOr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_BitOr of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_BitXor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_BitXor", "name": "visit_BitXor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.BitXor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_BitXor of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_BoolOp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_BoolOp", "name": "visit_BoolOp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Bool<PERSON>p"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_BoolOp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Break": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Break", "name": "visit_Break", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Break"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Break of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": "function ast.NodeVisitor.visit_Bytes is deprecated: Replaced by visit_Constant; removed in Python 3.14", "flags": ["is_decorated"], "fullname": "ast.NodeVisitor.visit_Bytes", "name": "visit_Bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.By<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Bytes of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "ast.NodeVisitor.visit_Bytes", "name": "visit_Bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.By<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Bytes of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Call", "name": "visit_Call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Call"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Call of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ClassDef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_ClassDef", "name": "visit_ClassDef", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.ClassDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ClassDef of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Compare", "name": "visit_Compare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Comp<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Compare of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Constant", "name": "visit_Constant", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Constant of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Continue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Continue", "name": "visit_Continue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Continue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Continue of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Del": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Del", "name": "visit_Del", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Del of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Delete", "name": "visit_Delete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Delete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Delete of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Dict", "name": "visit_Dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Dict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Dict of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_DictComp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_DictComp", "name": "visit_DictComp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.DictComp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_DictComp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Div": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Div", "name": "visit_Div", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Div"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Div of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Ellipsis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": "function ast.NodeVisitor.visit_Ellipsis is deprecated: Replaced by visit_Constant; removed in Python 3.14", "flags": ["is_decorated"], "fullname": "ast.NodeVisitor.visit_Ellipsis", "name": "visit_Ellipsis", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Ellipsis of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "ast.NodeVisitor.visit_Ellipsis", "name": "visit_Ellipsis", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Ellipsis of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Eq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Eq", "name": "visit_Eq", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Eq"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Eq of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ExceptHandler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Except<PERSON><PERSON><PERSON>", "name": "visit_<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_<PERSON><PERSON><PERSON><PERSON> of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Expr", "name": "visit_Expr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Expr of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Expression", "name": "visit_Expression", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Expression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Expression of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ExtSlice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_ExtSlice", "name": "visit_ExtSlice", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.ExtSlice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ExtSlice of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FloorDiv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_FloorDiv", "name": "visit_FloorDiv", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.FloorDiv"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FloorDiv of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_For": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_For", "name": "visit_For", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.For"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_For of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FormattedValue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_FormattedValue", "name": "visit_FormattedValue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.FormattedValue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FormattedValue of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FunctionDef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_FunctionDef", "name": "visit_FunctionDef", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.FunctionDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FunctionDef of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_GeneratorExp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_GeneratorExp", "name": "visit_GeneratorExp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.GeneratorExp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_GeneratorExp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Global": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Global", "name": "visit_Global", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Global"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Global of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Gt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Gt", "name": "visit_Gt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Gt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Gt of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_GtE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_GtE", "name": "visit_GtE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.GtE"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_GtE of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_If": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_If", "name": "visit_If", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.If"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_If of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_IfExp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_IfExp", "name": "visit_IfExp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.IfExp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_IfExp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Import": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Import", "name": "visit_Import", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.I<PERSON>rt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Import of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ImportFrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_ImportFrom", "name": "visit_ImportFrom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.ImportFrom"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ImportFrom of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_In": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_In", "name": "visit_In", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.In"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_In of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Index", "name": "visit_Index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Index"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Index of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Interactive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Interactive", "name": "visit_Interactive", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Interactive"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Interactive of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Invert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Invert", "name": "visit_Invert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Invert"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Invert of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Is": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Is", "name": "visit_Is", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Is"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Is of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_IsNot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_IsNot", "name": "visit_IsNot", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.IsNot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_IsNot of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_JoinedStr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_JoinedStr", "name": "visit_JoinedStr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.JoinedStr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_JoinedStr of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_LShift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_LShift", "name": "visit_LShift", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.LShift"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_LShift of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Lambda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Lambda", "name": "visit_Lambda", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Lambda of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_List": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_List", "name": "visit_List", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.List"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_List of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ListComp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_ListComp", "name": "visit_ListComp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.ListComp"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ListComp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Load", "name": "visit_Load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Load"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Load of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Lt", "name": "visit_Lt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Lt"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Lt of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_LtE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_LtE", "name": "visit_LtE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.LtE"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_LtE of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_MatMult": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_MatMult", "name": "visit_Mat<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_MatMult of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Mod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Mod", "name": "visit_Mod", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON>d"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Mod of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Module", "name": "visit_Module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Module of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Mult": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Mult", "name": "visit_Mult", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Mult of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Name", "name": "visit_Name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Name"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Name of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_NameConstant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": "function ast.NodeVisitor.visit_NameConstant is deprecated: Replaced by visit_Constant; removed in Python 3.14", "flags": ["is_decorated"], "fullname": "ast.NodeVisitor.visit_NameConstant", "name": "visit_NameConstant", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.NameConstant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NameConstant of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "ast.NodeVisitor.visit_NameConstant", "name": "visit_NameConstant", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.NameConstant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NameConstant of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_NamedExpr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_NamedExpr", "name": "visit_NamedExpr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.NamedExpr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NamedExpr of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Nonlocal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Nonlocal", "name": "visit_Nonlocal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Nonlocal of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Not": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Not", "name": "visit_Not", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Not"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Not of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_NotEq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_NotEq", "name": "visit_NotEq", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.NotEq"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NotEq of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_NotIn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_NotIn", "name": "visit_NotIn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.NotIn"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NotIn of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": "function ast.NodeVisitor.visit_Num is deprecated: Replaced by visit_Constant; removed in Python 3.14", "flags": ["is_decorated"], "fullname": "ast.NodeVisitor.visit_Num", "name": "visit_Num", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Num of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "ast.NodeVisitor.visit_Num", "name": "visit_Num", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Num of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Or", "name": "visit_Or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Or"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Or of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Param", "name": "visit_Param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Param of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Pass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Pass", "name": "visit_Pass", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Pass"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Pass of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Pow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Pow", "name": "visit_Pow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Pow of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_RShift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_RShift", "name": "visit_RShift", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.RShift"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_RShift of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Raise", "name": "visit_Raise", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Raise of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Return": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Return", "name": "visit_Return", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Return"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Return of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Set", "name": "visit_Set", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Set"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Set of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_SetComp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_SetComp", "name": "visit_SetComp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_SetComp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Slice", "name": "visit_Slice", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Slice of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Starred": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Starred", "name": "visit_Starred", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON>ed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Starred of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Store", "name": "visit_Store", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Store"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Store of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": "function ast.NodeVisitor.visit_Str is deprecated: Replaced by visit_Constant; removed in Python 3.14", "flags": ["is_decorated"], "fullname": "ast.NodeVisitor.visit_Str", "name": "visit_Str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Str of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "ast.NodeVisitor.visit_Str", "name": "visit_Str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Str of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Sub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Sub", "name": "visit_Sub", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Sub"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Sub of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Subscript": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Subscript", "name": "visit_Subscript", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Subscript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Subscript of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Suite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Suite", "name": "visit_Suite", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Suite"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Suite of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Try": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Try", "name": "visit_Try", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.Try"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Try of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Tuple", "name": "visit_<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_<PERSON><PERSON> of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_TypeIgnore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_TypeIgnore", "name": "visit_TypeIgnore", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.TypeIgnore"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_TypeIgnore of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_UAdd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_UAdd", "name": "visit_UAdd", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.U<PERSON>dd"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_UAdd of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_USub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_USub", "name": "visit_USub", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_USub of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_UnaryOp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_UnaryOp", "name": "visit_UnaryOp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_UnaryOp of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_While": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_While", "name": "visit_While", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.While"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_While of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_With": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_With", "name": "visit_With", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.With"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_With of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Yield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_Yield", "name": "visit_Yield", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Yield of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_YieldFrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_YieldFrom", "name": "visit_YieldFrom", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_YieldFrom of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_alias", "name": "visit_alias", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.alias"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_alias of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_arg", "name": "visit_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.arg"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_arg of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_arguments", "name": "visit_arguments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.arguments"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_arguments of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_comprehension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_comprehension", "name": "visit_comprehension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.comprehension"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_comprehension of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_keyword": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_keyword", "name": "visit_keyword", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.keyword"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_keyword of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_withitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.NodeVisitor.visit_withitem", "name": "visit_withitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["ast.NodeVisitor", "ast.withitem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_withitem of NodeVisitor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Nonlocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON>", "name": "Nonlocal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON>", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Nonlocal.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "names", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON>", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Nonlocal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Nonlocal.names", "name": "names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Not": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Not", "name": "Not", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Not", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Not", "ast.unaryop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotEq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.NotEq", "name": "NotEq", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.NotEq", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.NotEq", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotIn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.cmpop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.NotIn", "name": "NotIn", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.NotIn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.NotIn", "ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Num": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.<PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": "ast._ABC", "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Num is deprecated: Replaced by ast.Constant; removed in Python 3.14", "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": "ast._ABC", "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Num.value", "name": "value", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Or": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.boolop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Or", "name": "Or", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Or", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Or", "ast.boolop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "Param", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Param is deprecated: Deprecated since Python 3.9. Unused in Python 3.", "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Pass", "name": "Pass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Pass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Pass", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "<PERSON>w", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyCF_ALLOW_TOP_LEVEL_AWAIT": {".class": "SymbolTableNode", "cross_ref": "_ast.PyCF_ALLOW_TOP_LEVEL_AWAIT", "kind": "Gdef"}, "PyCF_ONLY_AST": {".class": "SymbolTableNode", "cross_ref": "_ast.PyCF_ONLY_AST", "kind": "Gdef"}, "PyCF_TYPE_COMMENTS": {".class": "SymbolTableNode", "cross_ref": "_ast.PyCF_TYPE_COMMENTS", "kind": "Gdef"}, "RShift": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.RShift", "name": "RShift", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.RShift", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.RShift", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Raise": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "Rai<PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "exc", "cause", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "exc", "cause", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Raise", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "cause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Raise.cause", "name": "cause", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "exc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Raise.exc", "name": "exc", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Return": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Return", "name": "Return", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Return", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Return", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Return.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["ast.Return", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Return", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Return.value", "name": "value", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Set", "name": "Set", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Set", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Set", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "elts", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Set.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "elts", "kwargs"], "arg_types": ["ast.Set", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Set", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "elts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Set.elts", "name": "elts", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SetComp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>", "name": "SetComp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.SetComp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elt", "generators", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON><PERSON>", "ast.expr", {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SetComp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "elt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.SetComp.elt", "name": "elt", "type": "ast.expr"}}, "generators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.SetComp.generators", "name": "generators", "type": {".class": "Instance", "args": ["ast.comprehension"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.slice"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "Slice", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.slice", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "lower", "upper", "step", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Slice.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "lower", "upper", "step", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": "ast._SliceAttributes", "items": [], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Slice", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "lower": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Slice.lower", "name": "lower", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Slice.step", "name": "step", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "upper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Slice.upper", "name": "upper", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Starred": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON>ed", "name": "Starred", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON>ed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON>ed", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Starred.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "value", "ctx", "kwargs"], "arg_types": ["ast.<PERSON>ed", "ast.expr", "ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Starred.ctx", "name": "ctx", "type": "ast.expr_context"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Starred.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Store": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr_context"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Store", "name": "Store", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Store", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Store", "ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.<PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": "ast._ABC", "defn": {".class": "ClassDef", "fullname": "ast.Str", "name": "Str", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Str is deprecated: Replaced by ast.Con<PERSON>; removed in Python 3.14", "flags": [], "fullname": "ast.Str", "has_param_spec_type": false, "metaclass_type": "ast._ABC", "metadata": {}, "module_name": "ast", "mro": ["ast.Str", "ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "s": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Str.s", "name": "s", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Str.value", "name": "value", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.operator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Sub", "name": "Sub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Sub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Sub", "ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Subscript": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Subscript", "name": "Subscript", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Subscript", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Subscript", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "slice", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Subscript.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "value", "slice", "ctx", "kwargs"], "arg_types": ["ast.Subscript", "ast.expr", "ast.slice", "ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Subscript", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Subscript.ctx", "name": "ctx", "type": "ast.expr_context"}}, "slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Subscript.slice", "name": "slice", "type": "ast.slice"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Subscript.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Suite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.mod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Suite", "name": "Suite", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.Suite is deprecated: Deprecated since Python 3.9. Unused in Python 3.", "flags": [], "fullname": "ast.Suite", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Suite", "ast.mod", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Suite.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["ast.Suite", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Suite", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Suite.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Try": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.Try", "name": "Try", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.Try", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.Try", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "body", "handlers", "orelse", "finalbody", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Try.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "body", "handlers", "orelse", "finalbody", "kwargs"], "arg_types": ["ast.Try", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.<PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Try", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Try.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "finalbody": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Try.finalbody", "name": "finalbody", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Try.handlers", "name": "handlers", "type": {".class": "Instance", "args": ["ast.<PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.<PERSON><PERSON>ore<PERSON>e", "name": "orelse", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "elts", "ctx", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "ast.expr_context", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Tuple.ctx", "name": "ctx", "type": "ast.expr_context"}}, "elts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Tuple.elts", "name": "elts", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeIgnore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.type_ignore"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.TypeIgnore", "name": "TypeIgnore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.TypeIgnore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.TypeIgnore", "ast.type_ignore", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lineno", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.TypeIgnore.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lineno", "tag"], "arg_types": ["ast.TypeIgnore", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypeIgnore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.TypeIgnore.lineno", "name": "lineno", "type": "builtins.int"}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.TypeIgnore.tag", "name": "tag", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UAdd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.U<PERSON>dd", "name": "UAdd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.U<PERSON>dd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.U<PERSON>dd", "ast.unaryop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "USub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.unaryop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "USub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.unaryop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnaryOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON>", "name": "UnaryOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "operand", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.UnaryOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "operand", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON>", "ast.unaryop", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnaryOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.UnaryOp.op", "name": "op", "type": "ast.unaryop"}}, "operand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.UnaryOp.operand", "name": "operand", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "While": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.While", "name": "While", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.While", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.While", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.While.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "test", "body", "orelse", "kwargs"], "arg_types": ["ast.While", "ast.expr", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of While", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.While.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "orelse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.<PERSON>.orelse", "name": "orelse", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.While.test", "name": "test", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "With": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.With", "name": "With", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.With", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.With", "ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.With.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "items", "body", "type_comment", "kwargs"], "arg_types": ["ast.With", {".class": "Instance", "args": ["ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of With", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.With.body", "name": "body", "type": {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.With.items", "name": "items", "type": {".class": "Instance", "args": ["ast.withitem"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.With.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Yield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON>", "name": "Yield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.Yield.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["ast.<PERSON><PERSON>", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Yield", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.Yield.value", "name": "value", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "YieldFrom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.<PERSON><PERSON><PERSON>", "name": "YieldFrom", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.<PERSON><PERSON><PERSON>", "ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.YieldFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "value", "kwargs"], "arg_types": ["ast.<PERSON><PERSON><PERSON>", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of YieldFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.YieldFrom.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ABC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast._ABC", "name": "_ABC", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast._ABC", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast._ABC", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Attributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast._Attributes", "name": "_Attributes", "type_vars": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "ast._EndPositionT", "id": 1, "name": "_EndPositionT", "namespace": "ast._Attributes", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast._Attributes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast._Attributes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": ["_EndPositionT"], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "ast._EndPositionT", "id": 1, "name": "_EndPositionT", "namespace": "ast._Attributes", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}], ["end_col_offset", {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "ast._EndPositionT", "id": 1, "name": "_EndPositionT", "namespace": "ast._Attributes", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}]], "readonly_keys": [], "required_keys": []}}}, "_EndPositionT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "ast._EndPositionT", "name": "_EndPositionT", "upper_bound": "builtins.object", "values": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "variance": 0}}, "_Identifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ast._Identifier", "line": 21, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "_Slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "ast._Slice", "line": 1165, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ast.slice"}}, "_SliceAttributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast._SliceAttributes", "name": "_SliceAttributes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast._SliceAttributes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast._SliceAttributes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [], "readonly_keys": [], "required_keys": []}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "name": "_T", "upper_bound": "ast.AST", "values": [], "variance": 0}}, "_TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ast.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ast.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ast.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ast.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ast.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ast.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "alias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.alias", "name": "alias", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.alias", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.alias", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "asname", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.alias.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "asname", "kwargs"], "arg_types": ["ast.alias", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of alias", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "asname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.alias.asname", "name": "asname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.alias.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.alias.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.alias.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.alias.<PERSON>o", "name": "lineno", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.alias.name", "name": "name", "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "arg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.arg", "name": "arg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.arg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.arg", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "arg", "annotation", "type_comment", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.arg.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "arg", "annotation", "type_comment", "kwargs"], "arg_types": ["ast.arg", "builtins.str", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.annotation", "name": "annotation", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.arg", "name": "arg", "type": "builtins.str"}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.lineno", "name": "lineno", "type": "builtins.int"}}, "type_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arg.type_comment", "name": "type_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.arguments", "name": "arguments", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.arguments", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.arguments", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ast.arguments.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.arguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 5, 3], "arg_names": ["self", "posonlyargs", "args", "vararg", "kwonlyargs", "kw_defaults", "kwarg", "defaults"], "arg_types": ["ast.arguments", {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of arguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.args", "name": "args", "type": {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.defaults", "name": "defaults", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "kw_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.kw_defaults", "name": "kw_defaults", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "kwarg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.kwarg", "name": "kwarg", "type": {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "kwonlyargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.kwon<PERSON>s", "name": "kwonlyargs", "type": {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "posonlyargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.posonlyargs", "name": "posonlyargs", "type": {".class": "Instance", "args": ["ast.arg"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "vararg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.arguments.vararg", "name": "vararg", "type": {".class": "UnionType", "items": ["ast.arg", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "boolop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.boolop", "name": "boolop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.boolop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.boolop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "cmpop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.cmpop", "name": "cmpop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.cmpop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.cmpop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "comprehension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.comprehension", "name": "comprehension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.comprehension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.comprehension", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target", "iter", "ifs", "is_async"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.comprehension.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target", "iter", "ifs", "is_async"], "arg_types": ["ast.comprehension", "ast.expr", "ast.expr", {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of comprehension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ifs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.comprehension.ifs", "name": "ifs", "type": {".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "is_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.comprehension.is_async", "name": "is_async", "type": "builtins.int"}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.comprehension.iter", "name": "iter", "type": "ast.expr"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.comprehension.target", "name": "target", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "copy_location": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["new_node", "old_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.copy_location", "name": "copy_location", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["new_node", "old_node"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.copy_location", "upper_bound": "ast.AST", "values": [], "variance": 0}, "ast.AST"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_location", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.copy_location", "upper_bound": "ast.AST", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.copy_location", "upper_bound": "ast.AST", "values": [], "variance": 0}]}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dump": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["node", "annotate_fields", "include_attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.dump", "name": "dump", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["node", "annotate_fields", "include_attributes"], "arg_types": ["ast.AST", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "excepthandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.except<PERSON>ler", "name": "except<PERSON>ler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.except<PERSON>ler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.except<PERSON>ler", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.excepthandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["ast.except<PERSON>ler", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of excepthandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.excepthandler.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.excepthandler.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.excepthandler.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.excepthandler.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.expr", "name": "expr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.expr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.expr", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.expr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of expr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.expr.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.expr.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.expr.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.expr.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "expr_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.expr_context", "name": "expr_context", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.expr_context", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.expr_context", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "fix_missing_locations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.fix_missing_locations", "name": "fix_missing_locations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.fix_missing_locations", "upper_bound": "ast.AST", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fix_missing_locations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.fix_missing_locations", "upper_bound": "ast.AST", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.fix_missing_locations", "upper_bound": "ast.AST", "values": [], "variance": 0}]}}}, "get_docstring": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["node", "clean"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.get_docstring", "name": "get_docstring", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["node", "clean"], "arg_types": [{".class": "UnionType", "items": ["ast.AsyncFunctionDef", "ast.FunctionDef", "ast.ClassDef", "ast.<PERSON><PERSON><PERSON>"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_docstring", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_source_segment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["source", "node", "padded"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.get_source_segment", "name": "get_source_segment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["source", "node", "padded"], "arg_types": ["builtins.str", "ast.AST", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_source_segment", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "increment_lineno": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["node", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.increment_lineno", "name": "increment_lineno", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["node", "n"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.increment_lineno", "upper_bound": "ast.AST", "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "increment_lineno", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.increment_lineno", "upper_bound": "ast.AST", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ast._T", "id": -1, "name": "_T", "namespace": "ast.increment_lineno", "upper_bound": "ast.AST", "values": [], "variance": 0}]}}}, "iter_child_nodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.iter_child_nodes", "name": "iter_child_nodes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["ast.AST"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_child_nodes", "ret_type": {".class": "Instance", "args": ["ast.AST"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.iter_fields", "name": "iter_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["ast.AST"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_fields", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.keyword", "name": "keyword", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.keyword", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.keyword", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ast.keyword.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.keyword.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 4], "arg_names": ["self", "arg", "value", "kwargs"], "arg_types": ["ast.keyword", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of keyword", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}}}, "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.keyword.arg", "name": "arg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.keyword.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.keyword.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.keyword.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.keyword.lineno", "name": "lineno", "type": "builtins.int"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.keyword.value", "name": "value", "type": "ast.expr"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "literal_eval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node_or_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.literal_eval", "name": "literal_eval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node_or_string"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "ast.AST"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "literal_eval", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.mod", "name": "mod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.mod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.mod", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "operator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.operator", "name": "operator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.operator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.operator", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ast.parse", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exec"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exec"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_type"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_type"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Interactive", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Interactive", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_type"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_type"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Interactive", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Interactive", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.AST", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ast.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.AST", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "exec"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_type"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Interactive", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Expression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "func_type"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.FunctionType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["source", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.Interactive", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["source", "filename", "mode", "type_comments", "feature_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "ast.AST", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.slice", "name": "slice", "type_vars": []}, "deletable_attributes": [], "deprecated": "class ast.slice is deprecated: Deprecated since Python 3.9.", "flags": [], "fullname": "ast.slice", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.slice", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "stmt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.stmt", "name": "stmt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.stmt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.stmt", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.stmt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["ast.stmt", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of stmt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.stmt.col_offset", "name": "col_offset", "type": "builtins.int"}}, "end_col_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.stmt.end_col_offset", "name": "end_col_offset", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "end_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.stmt.end_lineno", "name": "end_lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.stmt.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_ignore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.type_ignore", "name": "type_ignore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.type_ignore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.type_ignore", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unaryop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.unaryop", "name": "unaryop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.unaryop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.unaryop", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "walk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.walk", "name": "walk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["ast.AST"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "walk", "ret_type": {".class": "Instance", "args": ["ast.AST"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "withitem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.AST"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ast.withitem", "name": "withitem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ast.withitem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ast", "mro": ["ast.withitem", "ast.AST", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "context_expr", "optional_vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ast.withitem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "context_expr", "optional_vars"], "arg_types": ["ast.withitem", "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of withitem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "context_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.withitem.context_expr", "name": "context_expr", "type": "ast.expr"}}, "optional_vars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ast.withitem.optional_vars", "name": "optional_vars", "type": {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/mypy/typeshed/stdlib/ast.pyi"}