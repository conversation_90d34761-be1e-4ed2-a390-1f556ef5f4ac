{".class": "MypyFile", "_fullname": "asttokens.astroid_compat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "asttokens.astroid_compat.BaseContainer", "name": "BaseContainer", "type": {".class": "AnyType", "missing_import_name": "asttokens.astroid_compat.BaseContainer", "source_any": null, "type_of_any": 3}}}, "NodeNG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "asttokens.astroid_compat.NodeNG", "name": "NodeNG", "type": {".class": "AnyType", "missing_import_name": "asttokens.astroid_compat.NodeNG", "source_any": null, "type_of_any": 3}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asttokens.astroid_compat.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.astroid_compat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.astroid_compat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.astroid_compat.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.astroid_compat.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.astroid_compat.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.astroid_compat.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "astroid_node_classes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "asttokens.astroid_compat.astroid_node_classes", "name": "astroid_node_classes", "type": {".class": "AnyType", "missing_import_name": "asttokens.astroid_compat.astroid_node_classes", "source_any": null, "type_of_any": 3}}}}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/asttokens/astroid_compat.py"}