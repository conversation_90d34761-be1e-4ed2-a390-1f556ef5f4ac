{"data_mtime": 1748744171, "dep_lines": [1, 1, 1, 1, 10, 5, 9], "dep_prios": [5, 30, 30, 30, 5, 5, 5], "dependencies": ["builtins", "_frozen_importlib", "abc", "typing"], "hash": "fe1b7f35240fb03111cb19ef43edd25b8ccfb8a6", "id": "asttokens.astroid_compat", "ignore_all": true, "interface_hash": "1b4792d01127874d1666f5712af1408b48207219", "mtime": 1744934545, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/asttokens/astroid_compat.py", "plugin_data": null, "size": 586, "suppressed": ["astroid.node_classes", "astroid.nodes", "astroid"], "version_id": "1.15.0"}