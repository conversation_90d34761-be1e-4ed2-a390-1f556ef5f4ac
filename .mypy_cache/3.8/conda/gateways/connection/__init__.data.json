{".class": "MypyFile", "_fullname": "conda.gateways.connection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AuthBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.AuthBase", "name": "AuthBase", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.AuthBase", "source_any": null, "type_of_any": 3}}}, "BaseAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.BaseAdapter", "name": "BaseAdapter", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.BaseAdapter", "source_any": null, "type_of_any": 3}}}, "CaseInsensitiveDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.CaseInsensitiveDict", "name": "CaseInsensitiveDict", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.CaseInsensitiveDict", "source_any": null, "type_of_any": 3}}}, "ChunkedEncodingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.ChunkedEncodingError", "name": "ChunkedEncodingError", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.ChunkedEncodingError", "source_any": null, "type_of_any": 3}}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.ConnectionError", "name": "ConnectionError", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.ConnectionError", "source_any": null, "type_of_any": 3}}}, "DEFAULT_POOLBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.DEFAULT_POOLBLOCK", "name": "DEFAULT_POOLBLOCK", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.DEFAULT_POOLBLOCK", "source_any": null, "type_of_any": 3}}}, "HTTPAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.HTTPAdapter", "name": "HTTPAdapter", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.HTTPAdapter", "source_any": null, "type_of_any": 3}}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.HTTPError", "name": "HTTPError", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.HTTPError", "source_any": null, "type_of_any": 3}}}, "InsecureRequestWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.InsecureRequestWarning", "name": "InsecureRequestWarning", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.InsecureRequestWarning", "source_any": null, "type_of_any": 3}}}, "InvalidSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.InvalidSchema", "name": "InvalidSchema", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.InvalidSchema", "source_any": null, "type_of_any": 3}}}, "PreparedRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.PreparedRequest", "name": "PreparedRequest", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.PreparedRequest", "source_any": null, "type_of_any": 3}}}, "RequestsProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.RequestsProxyError", "name": "RequestsProxyError", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.RequestsProxyError", "source_any": null, "type_of_any": 3}}}, "Response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.Response", "name": "Response", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.Response", "source_any": null, "type_of_any": 3}}}, "Retry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.Retry", "name": "Retry", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.Retry", "source_any": null, "type_of_any": 3}}}, "SSLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.SSLError", "name": "SSLError", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.SSLError", "source_any": null, "type_of_any": 3}}}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.Session", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "conda.gateways.connection.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_basic_auth_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection._basic_auth_str", "name": "_basic_auth_str", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection._basic_auth_str", "source_any": null, "type_of_any": 3}}}, "dispatch_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.dispatch_hook", "name": "dispatch_hook", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.dispatch_hook", "source_any": null, "type_of_any": 3}}}, "extract_cookies_to_jar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.extract_cookies_to_jar", "name": "extract_cookies_to_jar", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.extract_cookies_to_jar", "source_any": null, "type_of_any": 3}}}, "get_auth_from_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.get_auth_from_url", "name": "get_auth_from_url", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.get_auth_from_url", "source_any": null, "type_of_any": 3}}}, "get_netrc_auth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "conda.gateways.connection.get_netrc_auth", "name": "get_netrc_auth", "type": {".class": "AnyType", "missing_import_name": "conda.gateways.connection.get_netrc_auth", "source_any": null, "type_of_any": 3}}}}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/conda/gateways/connection/__init__.py"}