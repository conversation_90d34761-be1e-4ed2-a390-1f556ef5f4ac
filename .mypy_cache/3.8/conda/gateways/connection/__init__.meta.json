{"data_mtime": 1748744171, "dep_lines": [1, 1, 1, 1, 16, 15, 4, 5, 6, 7, 13, 14, 17, 18, 3], "dep_prios": [5, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["builtins", "_frozen_importlib", "abc", "typing"], "hash": "86f89bc899f2343f8137c1203bffe33d8cf8f44f", "id": "conda.gateways.connection", "ignore_all": true, "interface_hash": "48d17c3690b90ef2e60568356a47c328f34bdb7c", "mtime": 1736948019, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/conda/gateways/connection/__init__.py", "plugin_data": null, "size": 973, "suppressed": ["requests.packages.urllib3.util.retry", "requests.packages.urllib3.exceptions", "requests.adapters", "requests.auth", "requests.cookies", "requests.exceptions", "requests.hooks", "requests.models", "requests.structures", "requests.utils", "requests"], "version_id": "1.15.0"}