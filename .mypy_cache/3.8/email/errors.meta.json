{"data_mtime": 1748744171, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 30, 30, 30, 30], "dependencies": ["sys", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "7b86e1bd4be0c84d6eae4f72b0ee53542bfb65bd", "id": "email.errors", "ignore_all": true, "interface_hash": "d89eb67341664115c6a078b7f801b346eb3c2514", "mtime": 1748042741, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/mypy/typeshed/stdlib/email/errors.pyi", "plugin_data": null, "size": 1635, "suppressed": [], "version_id": "1.15.0"}