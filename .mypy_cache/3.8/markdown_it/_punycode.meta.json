{"data_mtime": 1748744171, "dep_lines": [23, 24, 25, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30], "dependencies": ["codecs", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "1ae9d92d8c153f4b63d762d7a2ad4b9202ced053", "id": "markdown_it._punycode", "ignore_all": true, "interface_hash": "b256b4eafdc33e50c08511bc1ff63c159dca79a1", "mtime": 1744934546, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/markdown_it/_punycode.py", "plugin_data": null, "size": 2364, "suppressed": [], "version_id": "1.15.0"}