{"data_mtime": 1748744171, "dep_lines": [4, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 30, 30, 30, 30], "dependencies": ["re", "builtins", "_frozen_importlib", "abc", "enum", "typing"], "hash": "984d5b32b02bfc1b72209861567da3b0756cd18b", "id": "markdown_it.common.html_re", "ignore_all": true, "interface_hash": "6037174bc99acb40ea086eb41d3bf85206ad62a4", "mtime": 1744934546, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/markdown_it/common/html_re.py", "plugin_data": null, "size": 929, "suppressed": [], "version_id": "1.15.0"}