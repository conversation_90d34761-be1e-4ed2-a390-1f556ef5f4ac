{".class": "MypyFile", "_fullname": "outcome._util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlreadyUsedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "outcome._util.AlreadyUsedError", "name": "AlreadyUsedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "outcome._util.AlreadyUsedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "outcome._util", "mro": ["outcome._util.AlreadyUsedError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "outcome._util.AlreadyUsedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "outcome._util.AlreadyUsedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "outcome._util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "outcome._util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "outcome._util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "outcome._util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "outcome._util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "outcome._util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "fixup_module_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_name", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "outcome._util.fixup_module_metadata", "name": "fixup_module_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module_name", "namespace"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixup_module_metadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_tb_frames": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["exc", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "outcome._util.remove_tb_frames", "name": "remove_tb_frames", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["exc", "n"], "arg_types": ["builtins.BaseException", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tb_frames", "ret_type": "builtins.BaseException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/outcome/_util.py"}