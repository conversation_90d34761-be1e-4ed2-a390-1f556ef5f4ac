{"data_mtime": 1748744171, "dep_lines": [2, 5, 1, 1, 1], "dep_prios": [5, 25, 5, 30, 30], "dependencies": ["typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "05f4e8f4e3a08397031f17fca63c6b8ec0bdde4e", "id": "outcome._version", "ignore_all": true, "interface_hash": "5271f6d705ce691508abb57467e08e34170d5872", "mtime": 1747271529, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/outcome/_version.py", "plugin_data": null, "size": 201, "suppressed": [], "version_id": "1.15.0"}