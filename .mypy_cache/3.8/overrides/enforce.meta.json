{"data_mtime": 1748744171, "dep_lines": [1, 1, 1, 1], "dep_prios": [5, 5, 30, 30], "dependencies": ["abc", "builtins", "_frozen_importlib", "typing"], "hash": "9231a456cbf052f7e2016b1760372cc0b721a66f", "id": "overrides.enforce", "ignore_all": true, "interface_hash": "c403f6a1446d7aac1b4dd0e47af5c9bef194b2d6", "mtime": 1744989285, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/overrides/enforce.py", "plugin_data": null, "size": 2349, "suppressed": [], "version_id": "1.15.0"}