{".class": "MypyFile", "_fullname": "sre_parse", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ANY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ANY", "kind": "Gdef"}, "ANY_ALL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ANY_ALL", "kind": "Gdef"}, "ASCIILETTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.ASCIILETTERS", "name": "ASCIILETTERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ASSERT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ASSERT", "kind": "Gdef"}, "ASSERT_NOT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ASSERT_NOT", "kind": "Gdef"}, "AT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT", "kind": "Gdef"}, "ATCODES": {".class": "SymbolTableNode", "cross_ref": "sre_constants.ATCODES", "kind": "Gdef"}, "AT_BEGINNING": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BEGINNING", "kind": "Gdef"}, "AT_BEGINNING_LINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BEGINNING_LINE", "kind": "Gdef"}, "AT_BEGINNING_STRING": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BEGINNING_STRING", "kind": "Gdef"}, "AT_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_BOUNDARY", "kind": "Gdef"}, "AT_END": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_END", "kind": "Gdef"}, "AT_END_LINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_END_LINE", "kind": "Gdef"}, "AT_END_STRING": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_END_STRING", "kind": "Gdef"}, "AT_LOCALE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_LOCALE", "kind": "Gdef"}, "AT_LOC_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_LOC_BOUNDARY", "kind": "Gdef"}, "AT_LOC_NON_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_LOC_NON_BOUNDARY", "kind": "Gdef"}, "AT_MULTILINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_MULTILINE", "kind": "Gdef"}, "AT_NON_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_NON_BOUNDARY", "kind": "Gdef"}, "AT_UNICODE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_UNICODE", "kind": "Gdef"}, "AT_UNI_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_UNI_BOUNDARY", "kind": "Gdef"}, "AT_UNI_NON_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.AT_UNI_NON_BOUNDARY", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BIGCHARSET": {".class": "SymbolTableNode", "cross_ref": "sre_constants.BIGCHARSET", "kind": "Gdef"}, "BRANCH": {".class": "SymbolTableNode", "cross_ref": "sre_constants.BRANCH", "kind": "Gdef"}, "CALL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CALL", "kind": "Gdef"}, "CATEGORIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.CATEGORIES", "name": "CATEGORIES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["sre_constants._NamedIntConstant", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["sre_constants._NamedIntConstant", "sre_constants._NamedIntConstant"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CATEGORY": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY", "kind": "Gdef"}, "CATEGORY_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_DIGIT", "kind": "Gdef"}, "CATEGORY_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_LINEBREAK", "kind": "Gdef"}, "CATEGORY_LOC_NOT_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_LOC_NOT_WORD", "kind": "Gdef"}, "CATEGORY_LOC_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_LOC_WORD", "kind": "Gdef"}, "CATEGORY_NOT_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_DIGIT", "kind": "Gdef"}, "CATEGORY_NOT_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_LINEBREAK", "kind": "Gdef"}, "CATEGORY_NOT_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_SPACE", "kind": "Gdef"}, "CATEGORY_NOT_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_NOT_WORD", "kind": "Gdef"}, "CATEGORY_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_SPACE", "kind": "Gdef"}, "CATEGORY_UNI_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_DIGIT", "kind": "Gdef"}, "CATEGORY_UNI_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_LINEBREAK", "kind": "Gdef"}, "CATEGORY_UNI_NOT_DIGIT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_DIGIT", "kind": "Gdef"}, "CATEGORY_UNI_NOT_LINEBREAK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_LINEBREAK", "kind": "Gdef"}, "CATEGORY_UNI_NOT_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_SPACE", "kind": "Gdef"}, "CATEGORY_UNI_NOT_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_NOT_WORD", "kind": "Gdef"}, "CATEGORY_UNI_SPACE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_SPACE", "kind": "Gdef"}, "CATEGORY_UNI_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_UNI_WORD", "kind": "Gdef"}, "CATEGORY_WORD": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CATEGORY_WORD", "kind": "Gdef"}, "CHARSET": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CHARSET", "kind": "Gdef"}, "CHCODES": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CHCODES", "kind": "Gdef"}, "CH_LOCALE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CH_LOCALE", "kind": "Gdef"}, "CH_UNICODE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.CH_UNICODE", "kind": "Gdef"}, "DIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.DIGITS", "name": "DIGITS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ESCAPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.ESCAPES", "name": "ESCAPES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["sre_constants._NamedIntConstant", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "FAILURE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.FAILURE", "kind": "Gdef"}, "FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.FLAGS", "name": "FLAGS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "GLOBAL_FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.GLOBAL_FLAGS", "name": "GLOBAL_FLAGS", "type": "builtins.int"}}, "GROUPREF": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF", "kind": "Gdef"}, "GROUPREF_EXISTS": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_EXISTS", "kind": "Gdef"}, "GROUPREF_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_IGNORE", "kind": "Gdef"}, "GROUPREF_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_LOC_IGNORE", "kind": "Gdef"}, "GROUPREF_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.GROUPREF_UNI_IGNORE", "kind": "Gdef"}, "HEXDIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.HEXDIGITS", "name": "HEXDIGITS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "IN": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN", "kind": "Gdef"}, "INFO": {".class": "SymbolTableNode", "cross_ref": "sre_constants.INFO", "kind": "Gdef"}, "IN_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN_IGNORE", "kind": "Gdef"}, "IN_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN_LOC_IGNORE", "kind": "Gdef"}, "IN_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.IN_UNI_IGNORE", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JUMP": {".class": "SymbolTableNode", "cross_ref": "sre_constants.JUMP", "kind": "Gdef"}, "LITERAL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL", "kind": "Gdef"}, "LITERAL_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL_IGNORE", "kind": "Gdef"}, "LITERAL_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL_LOC_IGNORE", "kind": "Gdef"}, "LITERAL_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.LITERAL_UNI_IGNORE", "kind": "Gdef"}, "MAGIC": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAGIC", "kind": "Gdef"}, "MARK": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MARK", "kind": "Gdef"}, "MAXGROUPS": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAXGROUPS", "kind": "Gdef"}, "MAXREPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAXREPEAT", "kind": "Gdef"}, "MAX_REPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAX_REPEAT", "kind": "Gdef"}, "MAX_UNTIL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MAX_UNTIL", "kind": "Gdef"}, "MIN_REPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MIN_REPEAT", "kind": "Gdef"}, "MIN_REPEAT_ONE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MIN_REPEAT_ONE", "kind": "Gdef"}, "MIN_UNTIL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.MIN_UNTIL", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NEGATE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NEGATE", "kind": "Gdef"}, "NOT_LITERAL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL", "kind": "Gdef"}, "NOT_LITERAL_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL_IGNORE", "kind": "Gdef"}, "NOT_LITERAL_LOC_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL_LOC_IGNORE", "kind": "Gdef"}, "NOT_LITERAL_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.NOT_LITERAL_UNI_IGNORE", "kind": "Gdef"}, "OCTDIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.OCTDIGITS", "name": "OCTDIGITS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "OPCODES": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OPCODES", "kind": "Gdef"}, "OP_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OP_IGNORE", "kind": "Gdef"}, "OP_LOCALE_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OP_LOCALE_IGNORE", "kind": "Gdef"}, "OP_UNICODE_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.OP_UNICODE_IGNORE", "kind": "Gdef"}, "RANGE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.RANGE", "kind": "Gdef"}, "RANGE_UNI_IGNORE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.RANGE_UNI_IGNORE", "kind": "Gdef"}, "REPEAT": {".class": "SymbolTableNode", "cross_ref": "sre_constants.REPEAT", "kind": "Gdef"}, "REPEAT_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.REPEAT_CHARS", "name": "REPEAT_CHARS", "type": "builtins.str"}}, "REPEAT_ONE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.REPEAT_ONE", "kind": "Gdef"}, "SPECIAL_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.SPECIAL_CHARS", "name": "SPECIAL_CHARS", "type": "builtins.str"}}, "SRE_FLAG_ASCII": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_ASCII", "kind": "Gdef"}, "SRE_FLAG_DEBUG": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_DEBUG", "kind": "Gdef"}, "SRE_FLAG_DOTALL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_DOTALL", "kind": "Gdef"}, "SRE_FLAG_IGNORECASE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_IGNORECASE", "kind": "Gdef"}, "SRE_FLAG_LOCALE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_LOCALE", "kind": "Gdef"}, "SRE_FLAG_MULTILINE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_MULTILINE", "kind": "Gdef"}, "SRE_FLAG_TEMPLATE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_TEMPLATE", "kind": "Gdef"}, "SRE_FLAG_UNICODE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_UNICODE", "kind": "Gdef"}, "SRE_FLAG_VERBOSE": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_FLAG_VERBOSE", "kind": "Gdef"}, "SRE_INFO_CHARSET": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_INFO_CHARSET", "kind": "Gdef"}, "SRE_INFO_LITERAL": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_INFO_LITERAL", "kind": "Gdef"}, "SRE_INFO_PREFIX": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SRE_INFO_PREFIX", "kind": "Gdef"}, "SUBPATTERN": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SUBPATTERN", "kind": "Gdef"}, "SUCCESS": {".class": "SymbolTableNode", "cross_ref": "sre_constants.SUCCESS", "kind": "Gdef"}, "State": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sre_parse.State", "name": "State", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sre_parse.State", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sre_parse", "mro": ["sre_parse.State", "builtins.object"], "names": {".class": "SymbolTable", "checkgroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.State.checkgroup", "name": "checkgroup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gid"], "arg_types": ["sre_parse.State", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkgroup of State", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "checklookbehindgroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "gid", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.State.checklookbehindgroup", "name": "checklookbehindgroup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "gid", "source"], "arg_types": ["sre_parse.State", "builtins.int", "sre_parse.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checklookbehindgroup of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closegroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "gid", "p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.State.closegroup", "name": "closegroup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "gid", "p"], "arg_types": ["sre_parse.State", "builtins.int", "sre_parse.SubPattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closegroup of State", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.State.flags", "name": "flags", "type": "builtins.int"}}, "groupdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.State.groupdict", "name": "groupdict", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sre_parse.State.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.State"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of State", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sre_parse.State.groups", "name": "groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.State"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "groups of State", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "groupwidths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.State.groupwidths", "name": "groupwidths", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "lookbehindgroups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.State.lookbehindgroups", "name": "lookbehindgroups", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "opengroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.State.opengroup", "name": "opengroup", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["sre_parse.State", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opengroup of State", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sre_parse.SubPattern", "name": "SubPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sre_parse", "mro": ["sre_parse.SubPattern", "builtins.object"], "names": {".class": "SymbolTable", "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sre_parse.SubPattern", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of SubPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sre_parse.SubPattern", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SubPattern", "ret_type": {".class": "UnionType", "items": ["sre_parse.SubPattern", {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._CodeType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "data"], "arg_types": ["sre_parse.SubPattern", "sre_parse.State", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._CodeType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sre_parse.SubPattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of SubPattern", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sre_parse.SubPattern", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._CodeType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SubPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["sre_parse.SubPattern", {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._CodeType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of SubPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.SubPattern.data", "name": "data", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._CodeType"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.dump", "name": "dump", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "level"], "arg_types": ["sre_parse.SubPattern", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump of SubPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getwidth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.getwidth", "name": "getwidth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.SubPattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getwidth of SubPattern", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.SubPattern.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "code"], "arg_types": ["sre_parse.SubPattern", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._CodeType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert of SubPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.SubPattern.state", "name": "state", "type": "sre_parse.State"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.SubPattern.width", "name": "width", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.TYPE_FLAGS", "name": "TYPE_FLAGS", "type": "builtins.int"}}, "Tokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sre_parse.Tokenizer", "name": "Tokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sre_parse", "mro": ["sre_parse.Tokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "string"], "arg_types": ["sre_parse.Tokenizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Tokenizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decoded_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.Tokenizer.decoded_string", "name": "decoded_string", "type": "builtins.str"}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "msg", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "msg", "offset"], "arg_types": ["sre_parse.Tokenizer", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of <PERSON><PERSON><PERSON>", "ret_type": "re.error", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Tokenizer", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getuntil": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "terminator", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.getuntil", "name": "getuntil", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "terminator", "name"], "arg_types": ["sre_parse.Tokenizer", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getuntil of Tokenizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getwhile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "n", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.getwhile", "name": "getwhile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "n", "charset"], "arg_types": ["sre_parse.Tokenizer", "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getwhile of Tokenizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.Tokenizer.index", "name": "index", "type": "builtins.int"}}, "istext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.Tokenizer.istext", "name": "istext", "type": "builtins.bool"}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "char"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "char"], "arg_types": ["sre_parse.Tokenizer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of Tokenizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.Tokenizer.next", "name": "next", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sre_parse.Tokenizer.pos", "name": "pos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pos of Tokenizer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sre_parse.Tokenizer.pos", "name": "pos", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pos of Tokenizer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["sre_parse.Tokenizer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seek of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sre_parse.Tokenizer.string", "name": "string", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "tell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.Tokenizer.tell", "name": "tell", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sre_parse.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tell of Tokenizer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Verbose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sre_parse.Verbose", "name": "Verbose", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sre_parse.Verbose", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sre_parse", "mro": ["sre_parse.Verbose", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WHITESPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.WHITESPACE", "name": "WHITESPACE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_AvType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._AvType", "line": 32, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._OpInType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._OpBranchType"}, {".class": "Instance", "args": ["sre_parse.SubPattern"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._OpGroupRefExistsType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._OpSubpatternType"}], "uses_pep604_syntax": true}}}, "_CodeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._CodeType", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["sre_constants._NamedIntConstant", {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._AvType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_Error": {".class": "SymbolTableNode", "cross_ref": "re.error", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NIC": {".class": "SymbolTableNode", "cross_ref": "sre_constants._NamedIntConstant", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_OpBranchType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._OpBranchType", "line": 31, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["sre_parse.SubPattern"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_OpGroupRefExistsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._OpGroupRefExistsType", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "sre_parse.SubPattern", "sre_parse.SubPattern"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_OpInType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._OpInType", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["sre_constants._NamedIntConstant", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_OpSubpatternType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._OpSubpatternType", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "sre_parse.SubPattern"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TemplateByteType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._TemplateByteType", "line": 87, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_TemplateType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sre_parse._TemplateType", "line": 86, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sre_parse.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "error": {".class": "SymbolTableNode", "cross_ref": "re.error", "kind": "Gdef"}, "expand_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["template", "match"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.expand_template", "name": "expand_template", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["template", "match"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_template", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fix_flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.fix_flags", "name": "fix_flags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fix_flags", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["str", "flags", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sre_parse.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["str", "flags", "state"], "arg_types": ["builtins.str", "builtins.int", {".class": "UnionType", "items": ["sre_parse.State", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": "sre_parse.SubPattern", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sre_parse.parse_template", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["source", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "sre_parse.parse_template", "name": "parse_template", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["source", "state"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_template", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sre_parse.parse_template", "name": "parse_template", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["source", "state"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_template", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["source", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "sre_parse.parse_template", "name": "parse_template", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["source", "state"], "arg_types": ["builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_template", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateByteType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sre_parse.parse_template", "name": "parse_template", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["source", "state"], "arg_types": ["builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_template", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateByteType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["source", "state"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_template", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["source", "state"], "arg_types": ["builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_template", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sre_parse._TemplateByteType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/mypy/typeshed/stdlib/sre_parse.pyi"}