{"data_mtime": 1748744171, "dep_lines": [3, 4, 5, 6, 7, 8, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 30], "dependencies": ["abc", "sys", "typing", "_collections_abc", "_typeshed", "contextlib", "builtins", "_frozen_importlib"], "hash": "8b004565f89c760413260f9c21b7d4add31f1bb7", "id": "typing_extensions", "ignore_all": true, "interface_hash": "1383fbbcb1151444fd6b91248090c813df98454b", "mtime": 1748042741, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/mypy/typeshed/stdlib/typing_extensions.pyi", "plugin_data": null, "size": 17919, "suppressed": [], "version_id": "1.15.0"}