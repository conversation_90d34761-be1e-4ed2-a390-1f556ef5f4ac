{"data_mtime": 1748744171, "dep_lines": [6, 7, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30], "dependencies": ["importlib", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "40b6532bdd62121006c338253311d11cd86d2c68", "id": "zmq.backend.select", "ignore_all": true, "interface_hash": "62c2ba293cc2b6ee4a3c6c886f199cf27150b256", "mtime": 1744934543, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/miniforge3/lib/python3.12/site-packages/zmq/backend/select.py", "plugin_data": null, "size": 888, "suppressed": [], "version_id": "1.15.0"}