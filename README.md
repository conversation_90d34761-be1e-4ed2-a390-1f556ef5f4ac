# AreTomo3 GUI

A professional graphical user interface for AreTomo3 tomographic reconstruction software.

## Features

- **Enhanced Parameter Management**: Comprehensive parameter configuration with help system
- **Live Processing**: Real-time file monitoring and automated processing
- **Advanced Analysis**: Comprehensive result analysis and visualization
- **Professional Backup System**: Automated compressed backups with cleanup
- **Modern Architecture**: Built with PyQt6 and modern Python practices

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Launch the application
python -m aretomo3_gui
```

## Documentation

- [User Guide](docs/user/USER_GUIDE.md)
- [Installation Guide](docs/user/INSTALLATION.md)
- [Developer Guide](docs/developer/DEVELOPER_GUIDE.md)
- [API Reference](docs/api/API_REFERENCE.md)

## Requirements

- Python 3.8+
- PyQt6
- NumPy
- Matplotlib (optional, for plotting)

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

See [CONTRIBUTING.md](docs/developer/CONTRIBUTING.md) for contribution guidelines.
