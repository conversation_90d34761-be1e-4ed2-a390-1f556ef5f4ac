# AreTomo3 Analysis Dashboard

## 🔬 Overview

The AreTomo3 Analysis Dashboard is a modern, comprehensive web interface for real-time monitoring and analysis of tomographic reconstruction workflows. Inspired by professional analysis platforms but completely original in design and implementation.

## ✨ Key Features

### 🎯 **Real-Time Analysis Dashboard**
- **Three-panel layout**: Dataset browser, analysis grid, and details panel
- **Live quality assessment** with color-coded indicators
- **Interactive analysis panels** for each tilt series
- **Real-time plot visualization** (Motion, CTF, Alignment, Reconstruction)
- **WebSocket-based live updates**

### 📊 **Comprehensive Monitoring**
- **Processing status tracking** (Completed, Processing, Queued, Failed)
- **Quality metrics visualization** with scoring system
- **Progress monitoring** with time estimates
- **Session management** with auto-save functionality
- **Processing controls** (Start, Pause, Stop)

### 🎨 **Modern UI Design**
- **Dark theme** optimized for long-term use
- **Responsive grid layout** adapting to screen size
- **Professional color scheme** with intuitive status indicators
- **Smooth animations** and hover effects
- **Mobile-friendly** responsive design

### 🔧 **Technical Features**
- **RESTful API** with comprehensive endpoints
- **WebSocket real-time communication**
- **Integrated tomogram viewer** (connects to existing Napari integration)
- **Search and filtering** capabilities
- **Auto-refresh** with manual controls
- **Error handling** and fallback mechanisms

## 🚀 Quick Start

### 1. **Run the Test Dashboard**
```bash
python test_web_dashboard.py
```

### 2. **Access the Dashboard**
- **Main Dashboard**: http://localhost:8080
- **Legacy Interface**: http://localhost:8080/legacy
- **API Documentation**: http://localhost:8080/api/docs

### 3. **Dashboard Layout**

```
┌─────────────────────────────────────────────────────────────┐
│                    Header & Controls                        │
├──────────────┬─────────────────────────────┬────────────────┤
│   Dataset    │                             │    Details     │
│   Browser    │      Analysis Grid          │    Panel       │
│              │                             │                │
│ • Search     │  ┌─────────┐ ┌─────────┐    │ • Metrics      │
│ • Controls   │  │ Motion  │ │ Align   │    │ • Files        │
│ • List       │  └─────────┘ └─────────┘    │ • Parameters   │
│              │  ┌─────────┐ ┌─────────┐    │ • Quality      │
│              │  │   CTF   │ │ Recon   │    │                │
│              │  └─────────┘ └─────────┘    │                │
└──────────────┴─────────────────────────────┴────────────────┘
```

## 📋 API Endpoints

### **Core Endpoints**
- `GET /` - Main analysis dashboard
- `GET /dashboard` - Alternative dashboard access
- `GET /legacy` - Legacy interface
- `GET /api/docs` - API documentation

### **Data Endpoints**
- `GET /api/jobs` - All processing jobs with details
- `GET /api/jobs/{job_id}` - Specific job information
- `GET /api/dashboard/summary` - Dashboard statistics
- `GET /api/dashboard/realtime-plots/{job_id}` - Real-time plots

### **Processing Controls**
- `POST /api/processing/start` - Start processing
- `POST /api/processing/pause` - Pause processing
- `POST /api/processing/stop` - Stop processing

### **Analysis & Quality**
- `GET /api/analysis/quality-metrics` - Quality metrics for all series
- `GET /api/analysis/quality-metrics/{series_name}` - Specific series metrics
- `GET /api/analysis/data` - Real-time analysis data

### **Session Management**
- `GET /api/session/current` - Current session info
- `GET /api/session/list` - All sessions
- `POST /api/session/create` - Create new session

### **Viewer Integration**
- `GET /viewer/{job_id}` - Tomogram viewer page

## 🎨 Design Philosophy

### **Color Scheme**
- **Primary Background**: `#0f1419` (Deep dark blue)
- **Secondary Background**: `#1a1f2e` (Slate blue)
- **Accent Background**: `#252b3a` (Medium slate)
- **Text Primary**: `#ffffff` (White)
- **Text Secondary**: `#a0a6b8` (Light gray)

### **Status Colors**
- **Excellent Quality**: `#10b981` (Green)
- **Good Quality**: `#22c55e` (Light green)
- **Fair Quality**: `#f59e0b` (Orange)
- **Poor Quality**: `#ef4444` (Red)
- **Processing**: `#3b82f6` (Blue)

### **Typography**
- **Primary Font**: Inter, Segoe UI, system-ui
- **Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

## 🔧 Integration Points

### **Real-Time Analysis Tab**
The dashboard integrates with the existing real-time analysis functionality:
```python
web_api.set_main_window(main_window)
# Connects to realtime_analysis_tab for live data
```

### **Napari Viewer**
Tomogram viewing integrates with existing Napari functionality:
- View buttons open tomograms in integrated viewer
- Supports MRC, REC, and other tomogram formats

### **Session Management**
Connects to the session management system:
- Auto-save functionality
- Session history tracking
- Processing mode integration

## 📊 Quality Assessment

### **Quality Indicators**
- **Excellent** (Green): >90% quality score
- **Good** (Light Green): 75-90% quality score
- **Fair** (Orange): 60-75% quality score
- **Poor** (Red): <60% quality score

### **Metrics Tracked**
- **Motion Correction**: Drift measurements and correction quality
- **CTF Estimation**: Defocus accuracy and resolution estimates
- **Alignment Quality**: Tilt series alignment precision
- **Overall Score**: Composite quality assessment

## 🔄 Real-Time Updates

### **WebSocket Communication**
- Live job status updates
- Real-time quality metric changes
- Processing progress notifications
- Log message streaming

### **Auto-Refresh**
- 5-second update interval
- Manual refresh controls
- Pause/resume functionality
- Error handling and reconnection

## 🛠️ Development Notes

### **File Structure**
```
src/aretomo3_gui/web/
├── templates/
│   └── analysis_dashboard.html    # Main dashboard template
├── api_server.py                  # FastAPI server implementation
└── __init__.py

test_web_dashboard.py              # Test script with sample data
```

### **Key Classes**
- `AreTomo3WebAPI`: Main web server class
- `WebSocketManager`: Real-time communication handler
- `ProcessingJob`: Job data structure
- `ProcessingStats`: Statistics tracking

### **Dependencies**
- FastAPI: Web framework
- WebSockets: Real-time communication
- Chart.js: Plot visualization
- Bootstrap: UI components
- Font Awesome: Icons

## 🎯 Future Enhancements

### **Planned Features**
- **Advanced filtering** and sorting options
- **Export functionality** for reports and data
- **User preferences** and customization
- **Multi-user support** with authentication
- **Advanced plotting** with Plotly integration
- **Batch operation controls**
- **Performance monitoring** and optimization

### **Integration Opportunities**
- **Continue mode** processing integration
- **Advanced quality algorithms**
- **Machine learning** quality prediction
- **Cloud storage** integration
- **Notification system** for completion alerts

## 📝 License

This dashboard implementation is completely original and designed specifically for the AreTomo3 GUI project. No third-party dashboard code or designs were copied or adapted.
