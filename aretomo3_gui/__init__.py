"""
AreTomo3 GUI - Professional GUI for AreTomo3 tomographic reconstruction software.

A modern, feature-rich graphical user interface for AreTomo3 that provides:
- Enhanced analysis and visualization capabilities
- Professional UI with dark/light themes
- Comprehensive file management and batch processing
- Advanced parameter configuration
- Real-time system monitoring
"""

__version__ = "1.1.0"
__author__ = "AreTomo3 Team"
__email__ = "<EMAIL>"

__all__ = ['__version__']

def get_main_window():
    """Lazy import of main window to avoid circular imports."""
    from aretomo3_gui.gui.main_window import AreTomoGUI
    return AreTomoG<PERSON>

def check_dependencies():
    """Lazy import of dependency check to avoid immediate execution."""
    from aretomo3_gui.core.dependency_check import check_dependencies as _check_deps
    return _check_deps()