#!/usr/bin/env python3
"""
Interactive Plotly-based plotting for AreTomo3 GUI.
Provides zoomable, interactive plots with smart scaling and broken axis support.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import tempfile
import json

logger = logging.getLogger(__name__)

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    # Create dummy classes to prevent import errors
    class go:
        class Figure:
            def to_html(self, **kwargs):
                return "<div>Plotly not available</div>"
        class Scatter:
            pass
    class px:
        pass


class InteractivePlotter:
    """
    Interactive plotting class using Plotly for CTF analysis and other data visualization.
    Supports zoom, pan, broken axis scales, and smart outlier handling.
    """
    
    def __init__(self):
        """Initialize the interactive plotter."""
        self.theme = "plotly_white"
        self.color_palette = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ]
        
    def create_ctf_resolution_plot(self, ctf_data: Dict[str, Any], 
                                 use_broken_axis: bool = True,
                                 outlier_threshold: float = 3.0) -> str:
        """
        Create interactive CTF resolution plot with smart scaling and broken axis support.
        
        Args:
            ctf_data: CTF analysis data
            use_broken_axis: Whether to use broken axis for outliers
            outlier_threshold: Standard deviations for outlier detection
            
        Returns:
            str: HTML content for the plot
        """
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_message("Plotly not available")
        
        try:
            # Extract resolution data
            all_resolutions = []
            all_tilt_angles = []
            series_names = []
            
            if 'ctf_parameters' in ctf_data:
                for series_name, data in ctf_data['ctf_parameters'].items():
                    if 'parameters' in data and not data['parameters'].empty:
                        df = data['parameters']
                        if 'tilt_angle' in df.columns and 'resolution_limit_A' in df.columns:
                            # Filter out invalid values
                            valid_mask = (df['resolution_limit_A'] > 0) & (df['resolution_limit_A'] < 1000)
                            valid_df = df[valid_mask]
                            
                            if not valid_df.empty:
                                all_resolutions.extend(valid_df['resolution_limit_A'].tolist())
                                all_tilt_angles.extend(valid_df['tilt_angle'].tolist())
                                series_names.extend([series_name] * len(valid_df))
            
            if not all_resolutions:
                return self._create_fallback_message("No valid CTF resolution data available")
            
            # Convert to numpy arrays for analysis
            resolutions = np.array(all_resolutions)
            tilt_angles = np.array(all_tilt_angles)
            
            # Detect outliers
            outliers_mask, main_range, outlier_range = self._detect_outliers(
                resolutions, outlier_threshold
            )
            
            # Create figure with broken axis if needed
            if use_broken_axis and np.any(outliers_mask):
                fig = self._create_broken_axis_plot(
                    tilt_angles, resolutions, series_names, outliers_mask, 
                    main_range, outlier_range
                )
            else:
                fig = self._create_standard_plot(
                    tilt_angles, resolutions, series_names
                )
            
            # Update layout
            fig.update_layout(
                title="CTF Resolution vs Tilt Angle (Interactive)",
                xaxis_title="Tilt Angle (°)",
                yaxis_title="Resolution Limit (Å)",
                template=self.theme,
                hovermode='closest',
                showlegend=True,
                height=600,
                margin=dict(l=60, r=60, t=80, b=60)
            )
            
            # Add zoom and pan tools
            fig.update_layout(
                xaxis=dict(
                    showspikes=True,
                    spikecolor="black",
                    spikesnap="cursor",
                    spikemode="across"
                ),
                yaxis=dict(
                    showspikes=True,
                    spikecolor="black",
                    spikesnap="cursor",
                    spikemode="across"
                )
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="ctf_resolution_plot")
            
        except Exception as e:
            logger.error(f"Error creating CTF resolution plot: {e}")
            return self._create_fallback_message(f"Error creating plot: {str(e)}")
    
    def _detect_outliers(self, data: np.ndarray, threshold: float = 3.0) -> Tuple[np.ndarray, Tuple[float, float], Tuple[float, float]]:
        """
        Detect outliers using modified Z-score method.
        
        Args:
            data: Data array
            threshold: Z-score threshold for outlier detection
            
        Returns:
            Tuple of (outliers_mask, main_range, outlier_range)
        """
        # Calculate median and MAD (Median Absolute Deviation)
        median = np.median(data)
        mad = np.median(np.abs(data - median))
        
        # Modified Z-score
        modified_z_scores = 0.6745 * (data - median) / mad
        outliers_mask = np.abs(modified_z_scores) > threshold
        
        # Calculate ranges
        main_data = data[~outliers_mask]
        if len(main_data) > 0:
            main_range = (np.min(main_data), np.max(main_data))
        else:
            main_range = (np.min(data), np.max(data))
        
        outlier_data = data[outliers_mask]
        if len(outlier_data) > 0:
            outlier_range = (np.min(outlier_data), np.max(outlier_data))
        else:
            outlier_range = main_range
        
        return outliers_mask, main_range, outlier_range
    
    def _create_broken_axis_plot(self, x_data: np.ndarray, y_data: np.ndarray, 
                               series_names: List[str], outliers_mask: np.ndarray,
                               main_range: Tuple[float, float], 
                               outlier_range: Tuple[float, float]) -> go.Figure:
        """Create a plot with broken Y-axis to handle outliers."""
        
        # Create subplots with shared x-axis
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            row_heights=[0.7, 0.3],
            subplot_titles=("Main Data Range", "Outliers")
        )
        
        # Separate data into main and outlier groups
        main_mask = ~outliers_mask
        
        # Plot main data
        if np.any(main_mask):
            main_x = x_data[main_mask]
            main_y = y_data[main_mask]
            main_series = [series_names[i] for i in range(len(series_names)) if main_mask[i]]
            
            # Group by series for coloring
            unique_series = list(set(main_series))
            for i, series in enumerate(unique_series):
                series_mask = [s == series for s in main_series]
                series_x = main_x[series_mask]
                series_y = main_y[series_mask]
                
                fig.add_trace(
                    go.Scatter(
                        x=series_x, y=series_y,
                        mode='markers',
                        name=series,
                        marker=dict(
                            color=self.color_palette[i % len(self.color_palette)],
                            size=6,
                            opacity=0.7
                        ),
                        hovertemplate="<b>%{fullData.name}</b><br>" +
                                    "Tilt Angle: %{x:.1f}°<br>" +
                                    "Resolution: %{y:.1f} Å<extra></extra>"
                    ),
                    row=1, col=1
                )
        
        # Plot outliers
        if np.any(outliers_mask):
            outlier_x = x_data[outliers_mask]
            outlier_y = y_data[outliers_mask]
            outlier_series = [series_names[i] for i in range(len(series_names)) if outliers_mask[i]]
            
            # Group by series for coloring
            unique_series = list(set(outlier_series))
            for i, series in enumerate(unique_series):
                series_mask = [s == series for s in outlier_series]
                series_x = outlier_x[series_mask]
                series_y = outlier_y[series_mask]
                
                fig.add_trace(
                    go.Scatter(
                        x=series_x, y=series_y,
                        mode='markers',
                        name=f"{series} (outliers)",
                        marker=dict(
                            color=self.color_palette[i % len(self.color_palette)],
                            size=8,
                            symbol='x',
                            opacity=0.8
                        ),
                        hovertemplate="<b>%{fullData.name}</b><br>" +
                                    "Tilt Angle: %{x:.1f}°<br>" +
                                    "Resolution: %{y:.1f} Å (outlier)<extra></extra>"
                    ),
                    row=2, col=1
                )
        
        # Update y-axis ranges
        fig.update_yaxes(range=[main_range[0] * 0.95, main_range[1] * 1.05], row=1, col=1)
        if np.any(outliers_mask):
            fig.update_yaxes(range=[outlier_range[0] * 0.95, outlier_range[1] * 1.05], row=2, col=1)
        
        # Add break indicators
        fig.add_annotation(
            x=0.02, y=0.5,
            xref="paper", yref="paper",
            text="⚡ Axis Break ⚡",
            showarrow=False,
            font=dict(size=12, color="red"),
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="red",
            borderwidth=1
        )
        
        return fig
    
    def _create_standard_plot(self, x_data: np.ndarray, y_data: np.ndarray, 
                            series_names: List[str]) -> go.Figure:
        """Create a standard scatter plot."""
        
        fig = go.Figure()
        
        # Group by series for coloring
        unique_series = list(set(series_names))
        for i, series in enumerate(unique_series):
            series_mask = [s == series for s in series_names]
            series_x = x_data[series_mask]
            series_y = y_data[series_mask]
            
            fig.add_trace(
                go.Scatter(
                    x=series_x, y=series_y,
                    mode='markers',
                    name=series,
                    marker=dict(
                        color=self.color_palette[i % len(self.color_palette)],
                        size=6,
                        opacity=0.7
                    ),
                    hovertemplate="<b>%{fullData.name}</b><br>" +
                                "Tilt Angle: %{x:.1f}°<br>" +
                                "Resolution: %{y:.1f} Å<extra></extra>"
                )
            )
        
        return fig
    
    def create_ctf_defocus_plot(self, ctf_data: Dict[str, Any]) -> str:
        """Create interactive CTF defocus plot."""
        if not PLOTLY_AVAILABLE:
            return self._create_fallback_message("Plotly not available")
        
        try:
            fig = go.Figure()
            
            if 'ctf_parameters' in ctf_data:
                for i, (series_name, data) in enumerate(ctf_data['ctf_parameters'].items()):
                    if 'parameters' in data and not data['parameters'].empty:
                        df = data['parameters']
                        if 'tilt_angle' in df.columns and 'defocus1_A' in df.columns:
                            # Defocus U
                            fig.add_trace(
                                go.Scatter(
                                    x=df['tilt_angle'], 
                                    y=df['defocus1_A'] / 10000,  # Convert to μm
                                    mode='markers',
                                    name=f'{series_name} Defocus U',
                                    marker=dict(
                                        color=self.color_palette[i % len(self.color_palette)],
                                        size=6,
                                        opacity=0.7
                                    ),
                                    hovertemplate="<b>%{fullData.name}</b><br>" +
                                                "Tilt Angle: %{x:.1f}°<br>" +
                                                "Defocus U: %{y:.2f} μm<extra></extra>"
                                )
                            )
                            
                            # Defocus V if available
                            if 'defocus2_A' in df.columns:
                                fig.add_trace(
                                    go.Scatter(
                                        x=df['tilt_angle'], 
                                        y=df['defocus2_A'] / 10000,  # Convert to μm
                                        mode='markers',
                                        name=f'{series_name} Defocus V',
                                        marker=dict(
                                            color=self.color_palette[i % len(self.color_palette)],
                                            size=6,
                                            opacity=0.5,
                                            symbol='diamond'
                                        ),
                                        hovertemplate="<b>%{fullData.name}</b><br>" +
                                                    "Tilt Angle: %{x:.1f}°<br>" +
                                                    "Defocus V: %{y:.2f} μm<extra></extra>"
                                    )
                                )
            
            fig.update_layout(
                title="CTF Defocus vs Tilt Angle (Interactive)",
                xaxis_title="Tilt Angle (°)",
                yaxis_title="Defocus (μm)",
                template=self.theme,
                hovermode='closest',
                showlegend=True,
                height=500
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="ctf_defocus_plot")
            
        except Exception as e:
            logger.error(f"Error creating CTF defocus plot: {e}")
            return self._create_fallback_message(f"Error creating plot: {str(e)}")
    
    def _create_fallback_message(self, message: str) -> str:
        """Create fallback HTML message."""
        return f"""
        <div style="text-align: center; padding: 40px; border: 2px dashed #ccc; margin: 20px;">
            <h3>📊 Interactive Plot Not Available</h3>
            <p>{message}</p>
            <p><small>Install plotly for interactive plots: pip install plotly</small></p>
        </div>
        """


# Global interactive plotter instance
interactive_plotter = InteractivePlotter()
