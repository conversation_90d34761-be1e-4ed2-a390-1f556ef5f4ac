#!/usr/bin/env python3
"""
Motion Correction Data Parser for AreTomo3 Output

This module parses motion correction output files from AreTomo3:
- *_MC_GL.csv: Motion correction global parameters and statistics
- Motion corrected images from various formats
- Frame-by-frame motion data
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import logging
import json

try:
    import mrcfile
    HAS_MRCFILE = True
except ImportError:
    HAS_MRCFILE = False
    logging.warning("mrcfile not available - MRC reading will be limited")

try:
    from PIL import Image
    HAS_PIL = True
except ImportError:
    HAS_PIL = False
    logging.warning("PIL not available - TIFF reading will be limited")

logger = logging.getLogger(__name__)


class MotionCorrectionParser:
    """
    Parser for AreTomo3 motion correction output files.

    Handles parsing of:
    - Motion correction statistics and parameters
    - Motion corrected images
    - Frame-by-frame motion trajectories
    - Quality metrics and assessments
    """

    def __init__(self, base_path: Union[str, Path]):
        """
        Initialize motion correction parser.

        Args:
            base_path: Path to AreTomo3 output directory or base filename
        """
        self.base_path = Path(base_path)
        self.motion_data = {}
        self.motion_images = None
        self.motion_trajectories = []
        self.tilt_angles = []
        self.series_name = ""

        # Determine series name from path
        if self.base_path.is_dir():
            self._find_motion_files_in_directory()
        else:
            self.series_name = self.base_path.stem.replace('_MC_GL', '')
            self.base_path = self.base_path.parent

    def _find_motion_files_in_directory(self):
        """Find motion correction files in the given directory."""
        mc_files = list(self.base_path.glob("*_Log/*_MC_GL.csv"))
        if mc_files:
            mc_file = mc_files[0]
            # Extract series name from path like tomo25_Log/tomo25_MC_GL.csv
            self.series_name = mc_file.parent.name.replace('_Log', '')
        else:
            raise FileNotFoundError(f"No motion correction files found in {self.base_path}")

    def parse_all(self) -> Dict:
        """
        Parse all motion correction related files.

        Returns:
            Dictionary containing all parsed motion correction data
        """
        logger.info(f"Parsing motion correction data for series: {self.series_name}")

        # Parse motion correction parameters
        self.parse_motion_parameters()

        # Parse motion corrected images
        self.parse_motion_images()

        # Parse tilt angles
        self.parse_tilt_angles()

        # Calculate motion statistics
        self.calculate_motion_statistics()

        # Combine all data
        return self.get_combined_data()

    def parse_motion_parameters(self) -> pd.DataFrame:
        """
        Parse motion correction parameters from *_MC_GL.csv file.

        Returns:
            DataFrame with motion correction parameters
        """
        mc_file = self.base_path / f"{self.series_name}_Log" / f"{self.series_name}_MC_GL.csv"

        if not mc_file.exists():
            raise FileNotFoundError(f"Motion correction file not found: {mc_file}")

        logger.info(f"Parsing motion correction parameters from: {mc_file}")

        try:
            # Read CSV file without header (AreTomo3 format)
            df = pd.read_csv(mc_file, header=None, sep=r'\s+')

            # AreTomo3 MC_GL.csv format: frame_id, tilt_id, tilt_angle, pixel_size, x_shift, y_shift
            if len(df.columns) >= 6:
                df.columns = ['frame_id', 'tilt_id', 'tilt_angle', 'pixel_size', 'x_shift', 'y_shift']
            else:
                # Fallback column names
                df.columns = [f'col_{i}' for i in range(len(df.columns))]
                df['frame_id'] = range(len(df))
                df['tilt_id'] = 0
                df['tilt_angle'] = 0.0
                df['x_shift'] = 0.0
                df['y_shift'] = 0.0

            # Calculate motion statistics per tilt
            tilt_stats = []
            for tilt_id in df['tilt_id'].unique():
                tilt_data = df[df['tilt_id'] == tilt_id]

                # Calculate total motion for this tilt
                x_shifts = tilt_data['x_shift'].values
                y_shifts = tilt_data['y_shift'].values

                # Total motion as maximum displacement from start
                total_motion = np.sqrt((x_shifts[-1] - x_shifts[0])**2 + (y_shifts[-1] - y_shifts[0])**2)

                # Early vs late motion (first half vs second half)
                mid_point = len(x_shifts) // 2
                early_motion = np.sqrt((x_shifts[mid_point] - x_shifts[0])**2 + (y_shifts[mid_point] - y_shifts[0])**2)
                late_motion = np.sqrt((x_shifts[-1] - x_shifts[mid_point])**2 + (y_shifts[-1] - y_shifts[mid_point])**2)

                # Average shifts
                avg_x_shift = x_shifts[-1] - x_shifts[0]  # Total drift
                avg_y_shift = y_shifts[-1] - y_shifts[0]

                # Frame count
                frame_count = len(tilt_data)

                # Correlation (placeholder - would need actual correlation data)
                correlation = 0.8  # Default value

                # Tilt angle
                tilt_angle = tilt_data['tilt_angle'].iloc[0] if 'tilt_angle' in tilt_data.columns else 0.0

                tilt_stats.append({
                    'tilt_id': int(tilt_id),
                    'tilt_angle': tilt_angle,
                    'total_motion': total_motion,
                    'early_motion': early_motion,
                    'late_motion': late_motion,
                    'x_shift': avg_x_shift,
                    'y_shift': avg_y_shift,
                    'correlation': correlation,
                    'frame_count': frame_count
                })

            # Create summary DataFrame
            df_summary = pd.DataFrame(tilt_stats)

            # Store both frame-by-frame and summary data
            self.motion_data['frame_data'] = df
            self.motion_data['parameters'] = df_summary

            self.motion_data['parameters'] = df
            logger.info(f"Parsed {len(df)} motion correction records")

            return df

        except Exception as e:
            logger.error(f"Error parsing motion correction file: {e}")
            # Create empty DataFrame with expected structure
            df = pd.DataFrame(columns=['tilt_id', 'total_motion', 'early_motion', 'late_motion',
                                     'x_shift', 'y_shift', 'correlation', 'frame_count'])
            self.motion_data['parameters'] = df
            return df

    def parse_motion_images(self) -> Optional[np.ndarray]:
        """
        Parse motion corrected images from various formats.

        Returns:
            3D numpy array (n_tilts, height, width) or None if not found
        """
        logger.info("Searching for motion corrected images...")

        # Look for motion corrected images in various formats
        possible_patterns = [
            f"{self.series_name}.mrc",  # Main aligned stack
            f"{self.series_name}_*.mrc",  # Individual corrected images
            f"{self.series_name}_MC.mrc",  # Motion corrected stack
            "*.tif",  # TIFF format
            "*.tiff"  # TIFF format alternative
        ]

        motion_images = None

        for pattern in possible_patterns:
            image_files = list(self.base_path.glob(pattern))
            if image_files:
                logger.info(f"Found motion corrected images: {pattern}")
                motion_images = self._load_images(image_files)
                if motion_images is not None:
                    break

        if motion_images is None:
            logger.warning("No motion corrected images found")
        else:
            logger.info(f"Loaded motion corrected images: {motion_images.shape}")

        self.motion_images = motion_images
        return motion_images

    def _load_images(self, image_files: List[Path]) -> Optional[np.ndarray]:
        """Load images from various formats."""
        try:
            if len(image_files) == 1 and image_files[0].suffix.lower() == '.mrc':
                # Single MRC stack
                if HAS_MRCFILE:
                    with mrcfile.open(image_files[0], mode='r') as mrc:
                        data = np.array(mrc.data)
                        if data.ndim == 2:
                            data = data[np.newaxis, ...]
                        return data

            elif all(f.suffix.lower() in ['.tif', '.tiff'] for f in image_files):
                # Multiple TIFF files
                if HAS_PIL:
                    images = []
                    for img_file in sorted(image_files):
                        img = Image.open(img_file)
                        images.append(np.array(img))
                    return np.stack(images)

            elif all(f.suffix.lower() == '.mrc' for f in image_files):
                # Multiple MRC files
                if HAS_MRCFILE:
                    images = []
                    for img_file in sorted(image_files):
                        with mrcfile.open(img_file, mode='r') as mrc:
                            images.append(np.array(mrc.data))
                    return np.stack(images)

        except Exception as e:
            logger.error(f"Error loading images: {e}")

        return None

    def parse_tilt_angles(self) -> List[float]:
        """Parse tilt angles from available sources."""
        # Try to get tilt angles from TLT file or MDOC file
        tlt_file = self.base_path / f"{self.series_name}_TLT.txt"

        if tlt_file.exists():
            try:
                with open(tlt_file, 'r') as f:
                    angles = [float(line.strip().split()[0]) for line in f if line.strip()]
                self.tilt_angles = angles
                logger.info(f"Parsed {len(angles)} tilt angles from TLT file")
                return angles
            except Exception as e:
                logger.error(f"Error parsing TLT file: {e}")

        # Fallback: generate sequential angles if motion data exists
        if 'parameters' in self.motion_data and not self.motion_data['parameters'].empty:
            n_tilts = len(self.motion_data['parameters'])
            self.tilt_angles = list(range(n_tilts))
            logger.warning(f"Using sequential indices as tilt angles for {n_tilts} tilts")

        return self.tilt_angles

    def calculate_motion_statistics(self):
        """Calculate additional motion statistics and quality metrics."""
        if 'parameters' not in self.motion_data or self.motion_data['parameters'].empty:
            return

        df = self.motion_data['parameters']

        # Calculate additional statistics
        stats = {
            'mean_total_motion': df['total_motion'].mean(),
            'max_total_motion': df['total_motion'].max(),
            'std_total_motion': df['total_motion'].std(),
            'mean_correlation': df['correlation'].mean(),
            'min_correlation': df['correlation'].min(),
            'motion_trend': self._calculate_motion_trend(df),
            'quality_assessment': self._assess_motion_quality(df)
        }

        self.motion_data['statistics'] = stats

    def _calculate_motion_trend(self, df: pd.DataFrame) -> str:
        """Calculate motion trend across tilt series."""
        if len(df) < 3:
            return "insufficient_data"

        # Simple linear trend analysis
        x = np.arange(len(df))
        y = df['total_motion'].values

        # Calculate correlation coefficient
        correlation = np.corrcoef(x, y)[0, 1]

        if correlation > 0.3:
            return "increasing"
        elif correlation < -0.3:
            return "decreasing"
        else:
            return "stable"

    def _assess_motion_quality(self, df: pd.DataFrame) -> str:
        """Assess overall motion correction quality."""
        mean_motion = df['total_motion'].mean()
        mean_corr = df['correlation'].mean()

        if mean_motion < 2.0 and mean_corr > 0.8:
            return "excellent"
        elif mean_motion < 5.0 and mean_corr > 0.6:
            return "good"
        elif mean_motion < 10.0 and mean_corr > 0.4:
            return "fair"
        else:
            return "poor"

    def get_combined_data(self) -> Dict:
        """Get all parsed data in a combined dictionary."""
        # Add tilt angles to parameters if available
        if ('parameters' in self.motion_data and
            not self.motion_data['parameters'].empty and
            self.tilt_angles and
            len(self.tilt_angles) == len(self.motion_data['parameters'])):
            self.motion_data['parameters']['tilt_angle'] = self.tilt_angles

        return {
            'series_name': self.series_name,
            'base_path': str(self.base_path),
            'parameters': self.motion_data.get('parameters', pd.DataFrame()),
            'statistics': self.motion_data.get('statistics', {}),
            'motion_images': self.motion_images,
            'tilt_angles': self.tilt_angles,
            'n_tilts': len(self.tilt_angles) if self.tilt_angles else 0,
            'has_motion_images': self.motion_images is not None,
            'has_motion_data': 'parameters' in self.motion_data and not self.motion_data['parameters'].empty
        }


def test_motion_parser():
    """Test function for motion correction parser."""
    import sys

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        parser = MotionCorrectionParser(test_path)
        data = parser.parse_all()

        print(f"Successfully parsed motion correction data for: {data['series_name']}")
        print(f"Number of tilts: {data['n_tilts']}")
        print(f"Has motion images: {data['has_motion_images']}")
        print(f"Has motion data: {data['has_motion_data']}")

        if not data['parameters'].empty:
            print(f"\nMotion Statistics:")
            stats = data['statistics']
            for key, value in stats.items():
                print(f"  {key}: {value}")

            print(f"\nFirst few motion parameters:")
            print(data['parameters'].head())

        if data['motion_images'] is not None:
            print(f"\nMotion images shape: {data['motion_images'].shape}")
            print(f"Motion images data type: {data['motion_images'].dtype}")
            print(f"Motion images range: {data['motion_images'].min():.3f} to {data['motion_images'].max():.3f}")

        return True

    except Exception as e:
        print(f"Error testing motion parser: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_motion_parser()
