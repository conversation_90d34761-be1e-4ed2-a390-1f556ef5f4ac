#!/usr/bin/env python3
"""
AreTomo3 GUI Real-time Collaboration Framework
Multi-user collaboration with real-time synchronization, shared workspaces, and conflict resolution.
"""

import logging
import json
import asyncio
import websockets
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Set, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import uuid
import hashlib

# Optional dependencies
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """User roles in collaboration."""
    VIEWER = "viewer"
    COLLABORATOR = "collaborator"
    ADMIN = "admin"
    OWNER = "owner"


class MessageType(Enum):
    """Collaboration message types."""
    USER_JOIN = "user_join"
    USER_LEAVE = "user_leave"
    PARAMETER_UPDATE = "parameter_update"
    PROCESSING_START = "processing_start"
    PROCESSING_COMPLETE = "processing_complete"
    CHAT_MESSAGE = "chat_message"
    CURSOR_POSITION = "cursor_position"
    SELECTION_CHANGE = "selection_change"
    FILE_LOCK = "file_lock"
    FILE_UNLOCK = "file_unlock"
    CONFLICT_DETECTED = "conflict_detected"
    SYNC_REQUEST = "sync_request"


@dataclass
class CollaborationUser:
    """Collaboration user information."""
    user_id: str
    username: str
    display_name: str
    role: UserRole
    avatar_url: Optional[str]
    last_seen: datetime
    active: bool
    cursor_position: Dict[str, Any]
    current_selection: Dict[str, Any]


@dataclass
class CollaborationMessage:
    """Collaboration message."""
    message_id: str
    message_type: MessageType
    sender_id: str
    workspace_id: str
    timestamp: datetime
    data: Dict[str, Any]
    recipients: Optional[List[str]] = None


@dataclass
class SharedWorkspace:
    """Shared workspace for collaboration."""
    workspace_id: str
    name: str
    description: str
    owner_id: str
    created_at: datetime
    last_modified: datetime
    users: Dict[str, CollaborationUser]
    shared_parameters: Dict[str, Any]
    locked_files: Dict[str, str]  # file_path -> user_id
    chat_history: List[Dict[str, Any]]
    permissions: Dict[str, UserRole]


class CollaborationManager:
    """
    Real-time collaboration manager for AreTomo3 GUI.
    Supports multi-user workspaces, real-time synchronization, and conflict resolution.
    """
    
    def __init__(self, server_config: Dict[str, Any] = None):
        """Initialize the collaboration manager."""
        self.server_config = server_config or {
            'host': 'localhost',
            'port': 8765,
            'use_redis': False,
            'redis_host': 'localhost',
            'redis_port': 6379
        }
        
        # Collaboration state
        self.workspaces: Dict[str, SharedWorkspace] = {}
        self.connected_clients: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.user_sessions: Dict[str, str] = {}  # user_id -> workspace_id
        
        # WebSocket server
        self.server = None
        self.server_thread = None
        self.running = False
        
        # Redis for distributed collaboration (optional)
        self.redis_client = None
        if self.server_config.get('use_redis') and REDIS_AVAILABLE:
            self._initialize_redis()
        
        # Event callbacks
        self.event_callbacks: Dict[str, List[Callable]] = {}
        
        logger.info("Collaboration Manager initialized")
    
    def _initialize_redis(self):
        """Initialize Redis connection for distributed collaboration."""
        try:
            self.redis_client = redis.Redis(
                host=self.server_config['redis_host'],
                port=self.server_config['redis_port'],
                decode_responses=True
            )
            self.redis_client.ping()
            logger.info("Redis connection established for distributed collaboration")
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}")
            self.redis_client = None
    
    def start_server(self):
        """Start the collaboration server."""
        if self.running:
            logger.warning("Collaboration server already running")
            return
        
        self.running = True
        self.server_thread = threading.Thread(target=self._run_server, daemon=True)
        self.server_thread.start()
        
        logger.info(f"Collaboration server starting on {self.server_config['host']}:{self.server_config['port']}")
    
    def stop_server(self):
        """Stop the collaboration server."""
        if not self.running:
            return
        
        self.running = False
        
        if self.server:
            self.server.close()
        
        if self.server_thread:
            self.server_thread.join(timeout=5.0)
        
        logger.info("Collaboration server stopped")
    
    def _run_server(self):
        """Run the WebSocket server."""
        async def server_handler():
            self.server = await websockets.serve(
                self._handle_client_connection,
                self.server_config['host'],
                self.server_config['port']
            )
            
            logger.info(f"Collaboration server running on ws://{self.server_config['host']}:{self.server_config['port']}")
            
            # Keep server running
            await self.server.wait_closed()
        
        # Run the server
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            loop.run_until_complete(server_handler())
        except Exception as e:
            logger.error(f"Error in collaboration server: {e}")
        finally:
            loop.close()
    
    async def _handle_client_connection(self, websocket, path):
        """Handle new client connection."""
        client_id = str(uuid.uuid4())
        self.connected_clients[client_id] = websocket
        
        logger.info(f"Client connected: {client_id}")
        
        try:
            async for message in websocket:
                await self._process_message(client_id, message)
        
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected: {client_id}")
        
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
        
        finally:
            # Clean up client
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
            
            # Remove user from workspace if they were in one
            user_id = None
            for uid, cid in self.user_sessions.items():
                if cid == client_id:
                    user_id = uid
                    break
            
            if user_id:
                await self._handle_user_leave(user_id)
    
    async def _process_message(self, client_id: str, message_data: str):
        """Process incoming message from client."""
        try:
            message = json.loads(message_data)
            message_type = MessageType(message['type'])
            
            if message_type == MessageType.USER_JOIN:
                await self._handle_user_join(client_id, message)
            elif message_type == MessageType.USER_LEAVE:
                await self._handle_user_leave(message['user_id'])
            elif message_type == MessageType.PARAMETER_UPDATE:
                await self._handle_parameter_update(message)
            elif message_type == MessageType.CHAT_MESSAGE:
                await self._handle_chat_message(message)
            elif message_type == MessageType.CURSOR_POSITION:
                await self._handle_cursor_update(message)
            elif message_type == MessageType.FILE_LOCK:
                await self._handle_file_lock(message)
            elif message_type == MessageType.FILE_UNLOCK:
                await self._handle_file_unlock(message)
            elif message_type == MessageType.SYNC_REQUEST:
                await self._handle_sync_request(client_id, message)
            else:
                logger.warning(f"Unknown message type: {message_type}")
        
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    async def _handle_user_join(self, client_id: str, message: Dict[str, Any]):
        """Handle user joining a workspace."""
        user_data = message['user']
        workspace_id = message['workspace_id']
        
        # Create or get workspace
        if workspace_id not in self.workspaces:
            workspace = SharedWorkspace(
                workspace_id=workspace_id,
                name=message.get('workspace_name', 'Untitled Workspace'),
                description=message.get('workspace_description', ''),
                owner_id=user_data['user_id'],
                created_at=datetime.now(),
                last_modified=datetime.now(),
                users={},
                shared_parameters={},
                locked_files={},
                chat_history=[],
                permissions={}
            )
            self.workspaces[workspace_id] = workspace
        
        workspace = self.workspaces[workspace_id]
        
        # Create user
        user = CollaborationUser(
            user_id=user_data['user_id'],
            username=user_data['username'],
            display_name=user_data.get('display_name', user_data['username']),
            role=UserRole(user_data.get('role', 'collaborator')),
            avatar_url=user_data.get('avatar_url'),
            last_seen=datetime.now(),
            active=True,
            cursor_position={},
            current_selection={}
        )
        
        workspace.users[user.user_id] = user
        self.user_sessions[user.user_id] = client_id
        
        # Send workspace state to new user
        await self._send_to_client(client_id, {
            'type': 'workspace_state',
            'workspace': self._serialize_workspace(workspace),
            'your_user_id': user.user_id
        })
        
        # Notify other users
        await self._broadcast_to_workspace(workspace_id, {
            'type': 'user_joined',
            'user': asdict(user)
        }, exclude_user=user.user_id)
        
        logger.info(f"User {user.username} joined workspace {workspace.name}")
    
    async def _handle_user_leave(self, user_id: str):
        """Handle user leaving workspace."""
        if user_id not in self.user_sessions:
            return
        
        # Find workspace
        workspace_id = None
        for wid, workspace in self.workspaces.items():
            if user_id in workspace.users:
                workspace_id = wid
                break
        
        if workspace_id:
            workspace = self.workspaces[workspace_id]
            
            # Remove user
            if user_id in workspace.users:
                user = workspace.users[user_id]
                del workspace.users[user_id]
                
                # Release any file locks
                files_to_unlock = [path for path, lock_user in workspace.locked_files.items() if lock_user == user_id]
                for file_path in files_to_unlock:
                    del workspace.locked_files[file_path]
                
                # Notify other users
                await self._broadcast_to_workspace(workspace_id, {
                    'type': 'user_left',
                    'user_id': user_id,
                    'username': user.username,
                    'unlocked_files': files_to_unlock
                })
                
                logger.info(f"User {user.username} left workspace {workspace.name}")
        
        # Clean up session
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
    
    async def _handle_parameter_update(self, message: Dict[str, Any]):
        """Handle parameter update from user."""
        workspace_id = message['workspace_id']
        user_id = message['user_id']
        parameter_path = message['parameter_path']
        new_value = message['value']
        
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        
        # Update parameter
        self._set_nested_parameter(workspace.shared_parameters, parameter_path, new_value)
        workspace.last_modified = datetime.now()
        
        # Broadcast update to other users
        await self._broadcast_to_workspace(workspace_id, {
            'type': 'parameter_updated',
            'user_id': user_id,
            'parameter_path': parameter_path,
            'value': new_value,
            'timestamp': datetime.now().isoformat()
        }, exclude_user=user_id)
        
        # Store in Redis if available
        if self.redis_client:
            self.redis_client.hset(
                f"workspace:{workspace_id}:parameters",
                parameter_path,
                json.dumps(new_value)
            )
    
    async def _handle_chat_message(self, message: Dict[str, Any]):
        """Handle chat message."""
        workspace_id = message['workspace_id']
        user_id = message['user_id']
        chat_text = message['message']
        
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        user = workspace.users.get(user_id)
        
        if not user:
            return
        
        # Create chat message
        chat_message = {
            'message_id': str(uuid.uuid4()),
            'user_id': user_id,
            'username': user.username,
            'display_name': user.display_name,
            'message': chat_text,
            'timestamp': datetime.now().isoformat()
        }
        
        workspace.chat_history.append(chat_message)
        
        # Broadcast to all users in workspace
        await self._broadcast_to_workspace(workspace_id, {
            'type': 'chat_message',
            'message': chat_message
        })
    
    async def _handle_cursor_update(self, message: Dict[str, Any]):
        """Handle cursor position update."""
        workspace_id = message['workspace_id']
        user_id = message['user_id']
        cursor_data = message['cursor_data']
        
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        user = workspace.users.get(user_id)
        
        if user:
            user.cursor_position = cursor_data
            user.last_seen = datetime.now()
            
            # Broadcast cursor update to other users
            await self._broadcast_to_workspace(workspace_id, {
                'type': 'cursor_update',
                'user_id': user_id,
                'cursor_data': cursor_data
            }, exclude_user=user_id)
    
    async def _handle_file_lock(self, message: Dict[str, Any]):
        """Handle file lock request."""
        workspace_id = message['workspace_id']
        user_id = message['user_id']
        file_path = message['file_path']
        
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        
        # Check if file is already locked
        if file_path in workspace.locked_files:
            # Send lock denied
            client_id = self.user_sessions.get(user_id)
            if client_id:
                await self._send_to_client(client_id, {
                    'type': 'file_lock_denied',
                    'file_path': file_path,
                    'locked_by': workspace.locked_files[file_path]
                })
        else:
            # Grant lock
            workspace.locked_files[file_path] = user_id
            
            # Broadcast lock to all users
            await self._broadcast_to_workspace(workspace_id, {
                'type': 'file_locked',
                'file_path': file_path,
                'locked_by': user_id
            })
    
    async def _handle_file_unlock(self, message: Dict[str, Any]):
        """Handle file unlock request."""
        workspace_id = message['workspace_id']
        user_id = message['user_id']
        file_path = message['file_path']
        
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        
        # Check if user owns the lock
        if workspace.locked_files.get(file_path) == user_id:
            del workspace.locked_files[file_path]
            
            # Broadcast unlock to all users
            await self._broadcast_to_workspace(workspace_id, {
                'type': 'file_unlocked',
                'file_path': file_path,
                'unlocked_by': user_id
            })
    
    async def _handle_sync_request(self, client_id: str, message: Dict[str, Any]):
        """Handle synchronization request."""
        workspace_id = message['workspace_id']
        
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        
        # Send full workspace state
        await self._send_to_client(client_id, {
            'type': 'sync_response',
            'workspace': self._serialize_workspace(workspace)
        })
    
    async def _broadcast_to_workspace(self, workspace_id: str, message: Dict[str, Any], exclude_user: str = None):
        """Broadcast message to all users in workspace."""
        if workspace_id not in self.workspaces:
            return
        
        workspace = self.workspaces[workspace_id]
        
        for user_id, user in workspace.users.items():
            if exclude_user and user_id == exclude_user:
                continue
            
            client_id = self.user_sessions.get(user_id)
            if client_id and client_id in self.connected_clients:
                await self._send_to_client(client_id, message)
    
    async def _send_to_client(self, client_id: str, message: Dict[str, Any]):
        """Send message to specific client."""
        if client_id not in self.connected_clients:
            return
        
        try:
            websocket = self.connected_clients[client_id]
            await websocket.send(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending message to client {client_id}: {e}")
    
    def _serialize_workspace(self, workspace: SharedWorkspace) -> Dict[str, Any]:
        """Serialize workspace for transmission."""
        return {
            'workspace_id': workspace.workspace_id,
            'name': workspace.name,
            'description': workspace.description,
            'owner_id': workspace.owner_id,
            'users': {uid: asdict(user) for uid, user in workspace.users.items()},
            'shared_parameters': workspace.shared_parameters,
            'locked_files': workspace.locked_files,
            'chat_history': workspace.chat_history[-50:],  # Last 50 messages
            'last_modified': workspace.last_modified.isoformat()
        }
    
    def _set_nested_parameter(self, params: Dict[str, Any], path: str, value: Any):
        """Set nested parameter value using dot notation."""
        keys = path.split('.')
        current = params
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def create_workspace(self, name: str, description: str, owner_id: str) -> str:
        """Create a new shared workspace."""
        workspace_id = str(uuid.uuid4())
        
        workspace = SharedWorkspace(
            workspace_id=workspace_id,
            name=name,
            description=description,
            owner_id=owner_id,
            created_at=datetime.now(),
            last_modified=datetime.now(),
            users={},
            shared_parameters={},
            locked_files={},
            chat_history=[],
            permissions={}
        )
        
        self.workspaces[workspace_id] = workspace
        logger.info(f"Created workspace: {name} ({workspace_id})")
        
        return workspace_id
    
    def get_workspace_info(self, workspace_id: str) -> Optional[Dict[str, Any]]:
        """Get workspace information."""
        if workspace_id not in self.workspaces:
            return None
        
        workspace = self.workspaces[workspace_id]
        return {
            'workspace_id': workspace_id,
            'name': workspace.name,
            'description': workspace.description,
            'owner_id': workspace.owner_id,
            'user_count': len(workspace.users),
            'active_users': [user.username for user in workspace.users.values() if user.active],
            'created_at': workspace.created_at.isoformat(),
            'last_modified': workspace.last_modified.isoformat()
        }


# Global collaboration manager instance
collaboration_manager = CollaborationManager()


def get_collaboration_manager() -> CollaborationManager:
    """Get the global collaboration manager instance."""
    return collaboration_manager
