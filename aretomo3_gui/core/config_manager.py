#!/usr/bin/env python3
"""
AreTomo3 GUI Configuration Management System
Centralized configuration management with validation, profiles, and persistence.
"""

import logging
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
import copy
import threading
from enum import Enum

logger = logging.getLogger(__name__)


class ConfigFormat(Enum):
    """Configuration file formats."""
    JSON = "json"
    YAML = "yaml"
    INI = "ini"


@dataclass
class ConfigProfile:
    """Configuration profile."""
    name: str
    description: str
    config: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.now)
    modified_at: datetime = field(default_factory=datetime.now)
    is_default: bool = False
    tags: List[str] = field(default_factory=list)


class ConfigManager:
    """
    Centralized configuration management system.
    Handles configuration loading, saving, validation, and profiles.
    """
    
    def __init__(self, config_dir: Union[str, Path] = None):
        """Initialize the configuration manager."""
        self.config_dir = Path(config_dir) if config_dir else Path.home() / ".aretomo3_gui"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration files
        self.main_config_file = self.config_dir / "config.json"
        self.profiles_file = self.config_dir / "profiles.json"
        self.user_settings_file = self.config_dir / "user_settings.json"
        
        # Configuration data
        self.config: Dict[str, Any] = {}
        self.profiles: Dict[str, ConfigProfile] = {}
        self.user_settings: Dict[str, Any] = {}
        self.current_profile: Optional[str] = None
        
        # Default configuration
        self.default_config = self._create_default_config()
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Change callbacks
        self.change_callbacks: List[callable] = []
        
        # Load configurations
        self.load_all_configs()
        
        logger.info(f"Configuration Manager initialized - Config dir: {self.config_dir}")
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration."""
        return {
            "general": {
                "auto_save": True,
                "auto_save_interval": 300,  # seconds
                "backup_count": 5,
                "theme": "dark",
                "language": "en",
                "log_level": "INFO"
            },
            "processing": {
                "default_pixel_size": 1.0,
                "default_voltage": 300.0,
                "default_cs": 2.7,
                "max_parallel_jobs": 4,
                "temp_directory": str(Path.home() / "tmp" / "aretomo3"),
                "cleanup_temp_files": True
            },
            "gui": {
                "window_width": 1200,
                "window_height": 800,
                "remember_window_state": True,
                "show_tooltips": True,
                "animation_enabled": True,
                "font_size": 10,
                "icon_size": 24
            },
            "analysis": {
                "auto_generate_plots": True,
                "plot_format": "png",
                "plot_dpi": 300,
                "interactive_plots": True,
                "cache_analysis_results": True,
                "max_cache_size_mb": 1000
            },
            "web_server": {
                "enabled": True,
                "host": "0.0.0.0",
                "port": 8080,
                "auto_start": True,
                "cors_enabled": True,
                "api_key_required": False
            },
            "paths": {
                "aretomo3_executable": "",
                "imod_directory": "",
                "default_input_directory": "",
                "default_output_directory": "",
                "watch_directory": ""
            },
            "advanced": {
                "debug_mode": False,
                "performance_monitoring": True,
                "memory_limit_mb": 8192,
                "gpu_acceleration": True,
                "plugin_system_enabled": True
            }
        }
    
    def load_all_configs(self):
        """Load all configuration files."""
        with self._lock:
            # Load main configuration
            self.config = self._load_config_file(
                self.main_config_file, 
                self.default_config
            )
            
            # Load profiles
            self._load_profiles()
            
            # Load user settings
            self.user_settings = self._load_config_file(
                self.user_settings_file, 
                {}
            )
            
            logger.info("All configurations loaded successfully")
    
    def _load_config_file(self, file_path: Path, default: Dict[str, Any]) -> Dict[str, Any]:
        """Load configuration from file with fallback to default."""
        try:
            if file_path.exists():
                with open(file_path, 'r') as f:
                    if file_path.suffix.lower() == '.yaml':
                        config = yaml.safe_load(f)
                    else:
                        config = json.load(f)
                
                # Merge with default to ensure all keys exist
                merged_config = copy.deepcopy(default)
                self._deep_merge(merged_config, config)
                return merged_config
            else:
                logger.info(f"Config file not found: {file_path}, using defaults")
                return copy.deepcopy(default)
                
        except Exception as e:
            logger.error(f"Error loading config file {file_path}: {e}")
            return copy.deepcopy(default)
    
    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]):
        """Deep merge source dictionary into target."""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def _load_profiles(self):
        """Load configuration profiles."""
        try:
            if self.profiles_file.exists():
                with open(self.profiles_file, 'r') as f:
                    profiles_data = json.load(f)
                
                self.profiles = {}
                for name, data in profiles_data.items():
                    profile = ConfigProfile(
                        name=data['name'],
                        description=data['description'],
                        config=data['config'],
                        created_at=datetime.fromisoformat(data['created_at']),
                        modified_at=datetime.fromisoformat(data['modified_at']),
                        is_default=data.get('is_default', False),
                        tags=data.get('tags', [])
                    )
                    self.profiles[name] = profile
                
                # Set current profile to default if available
                for name, profile in self.profiles.items():
                    if profile.is_default:
                        self.current_profile = name
                        break
                        
                logger.info(f"Loaded {len(self.profiles)} configuration profiles")
            else:
                # Create default profile
                self._create_default_profile()
                
        except Exception as e:
            logger.error(f"Error loading profiles: {e}")
            self._create_default_profile()
    
    def _create_default_profile(self):
        """Create default configuration profile."""
        default_profile = ConfigProfile(
            name="default",
            description="Default configuration profile",
            config=copy.deepcopy(self.default_config),
            is_default=True,
            tags=["default", "system"]
        )
        self.profiles["default"] = default_profile
        self.current_profile = "default"
        self.save_profiles()
    
    def save_all_configs(self):
        """Save all configurations to files."""
        with self._lock:
            try:
                # Save main configuration
                self._save_config_file(self.main_config_file, self.config)
                
                # Save profiles
                self.save_profiles()
                
                # Save user settings
                self._save_config_file(self.user_settings_file, self.user_settings)
                
                logger.info("All configurations saved successfully")
                
                # Trigger change callbacks
                self._trigger_change_callbacks()
                
            except Exception as e:
                logger.error(f"Error saving configurations: {e}")
    
    def _save_config_file(self, file_path: Path, config: Dict[str, Any], format_type: ConfigFormat = ConfigFormat.JSON):
        """Save configuration to file."""
        try:
            # Create backup if file exists
            if file_path.exists():
                backup_path = file_path.with_suffix(f"{file_path.suffix}.backup")
                file_path.rename(backup_path)
            
            with open(file_path, 'w') as f:
                if format_type == ConfigFormat.YAML:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
                else:
                    json.dump(config, f, indent=2, default=str)
            
            logger.debug(f"Saved configuration to: {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving config file {file_path}: {e}")
            raise
    
    def save_profiles(self):
        """Save configuration profiles."""
        try:
            profiles_data = {}
            for name, profile in self.profiles.items():
                profiles_data[name] = {
                    'name': profile.name,
                    'description': profile.description,
                    'config': profile.config,
                    'created_at': profile.created_at.isoformat(),
                    'modified_at': profile.modified_at.isoformat(),
                    'is_default': profile.is_default,
                    'tags': profile.tags
                }
            
            self._save_config_file(self.profiles_file, profiles_data)
            logger.debug("Configuration profiles saved")
            
        except Exception as e:
            logger.error(f"Error saving profiles: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation."""
        with self._lock:
            keys = key.split('.')
            value = self.config
            
            try:
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                return default
    
    def set(self, key: str, value: Any, save: bool = True):
        """Set configuration value using dot notation."""
        with self._lock:
            keys = key.split('.')
            config = self.config
            
            # Navigate to parent
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # Set value
            config[keys[-1]] = value
            
            if save:
                self.save_all_configs()
            
            logger.debug(f"Configuration updated: {key} = {value}")
    
    def create_profile(self, name: str, description: str, config: Dict[str, Any] = None, tags: List[str] = None) -> bool:
        """Create a new configuration profile."""
        with self._lock:
            if name in self.profiles:
                logger.warning(f"Profile '{name}' already exists")
                return False
            
            profile = ConfigProfile(
                name=name,
                description=description,
                config=config or copy.deepcopy(self.config),
                tags=tags or []
            )
            
            self.profiles[name] = profile
            self.save_profiles()
            
            logger.info(f"Created configuration profile: {name}")
            return True
    
    def load_profile(self, name: str) -> bool:
        """Load a configuration profile."""
        with self._lock:
            if name not in self.profiles:
                logger.error(f"Profile '{name}' not found")
                return False
            
            profile = self.profiles[name]
            self.config = copy.deepcopy(profile.config)
            self.current_profile = name
            
            # Update modified time
            profile.modified_at = datetime.now()
            
            self.save_all_configs()
            logger.info(f"Loaded configuration profile: {name}")
            return True
    
    def delete_profile(self, name: str) -> bool:
        """Delete a configuration profile."""
        with self._lock:
            if name not in self.profiles:
                logger.error(f"Profile '{name}' not found")
                return False
            
            if self.profiles[name].is_default:
                logger.error(f"Cannot delete default profile: {name}")
                return False
            
            del self.profiles[name]
            
            # Switch to default if current profile was deleted
            if self.current_profile == name:
                self.load_profile("default")
            
            self.save_profiles()
            logger.info(f"Deleted configuration profile: {name}")
            return True
    
    def get_profiles(self) -> Dict[str, ConfigProfile]:
        """Get all configuration profiles."""
        return copy.deepcopy(self.profiles)
    
    def export_config(self, file_path: Union[str, Path], format_type: ConfigFormat = ConfigFormat.JSON):
        """Export current configuration to file."""
        file_path = Path(file_path)
        
        export_data = {
            'config': self.config,
            'profiles': {name: asdict(profile) for name, profile in self.profiles.items()},
            'user_settings': self.user_settings,
            'exported_at': datetime.now().isoformat(),
            'version': '1.0'
        }
        
        self._save_config_file(file_path, export_data, format_type)
        logger.info(f"Configuration exported to: {file_path}")
    
    def import_config(self, file_path: Union[str, Path]) -> bool:
        """Import configuration from file."""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"Import file not found: {file_path}")
                return False
            
            with open(file_path, 'r') as f:
                if file_path.suffix.lower() == '.yaml':
                    import_data = yaml.safe_load(f)
                else:
                    import_data = json.load(f)
            
            # Import configuration
            if 'config' in import_data:
                self.config = import_data['config']
            
            # Import profiles
            if 'profiles' in import_data:
                for name, profile_data in import_data['profiles'].items():
                    profile = ConfigProfile(**profile_data)
                    self.profiles[name] = profile
            
            # Import user settings
            if 'user_settings' in import_data:
                self.user_settings = import_data['user_settings']
            
            self.save_all_configs()
            logger.info(f"Configuration imported from: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error importing configuration: {e}")
            return False
    
    def register_change_callback(self, callback: callable):
        """Register callback for configuration changes."""
        self.change_callbacks.append(callback)
    
    def _trigger_change_callbacks(self):
        """Trigger all registered change callbacks."""
        for callback in self.change_callbacks:
            try:
                callback(self.config)
            except Exception as e:
                logger.error(f"Error in config change callback: {e}")
    
    def validate_config(self) -> List[str]:
        """Validate current configuration and return list of issues."""
        issues = []
        
        # Validate paths
        paths = self.get('paths', {})
        for path_key, path_value in paths.items():
            if path_value and not Path(path_value).exists():
                issues.append(f"Path does not exist: {path_key} = {path_value}")
        
        # Validate numeric values
        if self.get('processing.max_parallel_jobs', 0) <= 0:
            issues.append("max_parallel_jobs must be greater than 0")
        
        if self.get('web_server.port', 0) <= 0 or self.get('web_server.port', 0) > 65535:
            issues.append("web_server.port must be between 1 and 65535")
        
        return issues
    
    def reset_to_defaults(self):
        """Reset configuration to defaults."""
        with self._lock:
            self.config = copy.deepcopy(self.default_config)
            self.save_all_configs()
            logger.info("Configuration reset to defaults")


# Global configuration manager instance
config_manager = ConfigManager()
