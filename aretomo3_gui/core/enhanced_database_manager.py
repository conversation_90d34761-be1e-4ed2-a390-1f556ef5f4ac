#!/usr/bin/env python3
"""
Enhanced Database Manager with Connection Pooling and Transaction Safety
Addresses critical stability and security issues in database operations.
"""

import logging
import sqlite3
import threading
import queue
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, ContextManager
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class ConnectionInfo:
    """Database connection information."""
    connection: sqlite3.Connection
    created_at: datetime
    last_used: datetime
    in_use: bool = False
    transaction_active: bool = False


class TransactionManager:
    """Context manager for database transactions with automatic rollback."""
    
    def __init__(self, connection: sqlite3.Connection):
        self.connection = connection
        self.transaction_started = False
    
    def __enter__(self):
        try:
            self.connection.execute("BEGIN")
            self.transaction_started = True
            logger.debug("Transaction started")
            return self
        except Exception as e:
            logger.error(f"Failed to start transaction: {e}")
            raise
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            if exc_type is None:
                self.connection.execute("COMMIT")
                logger.debug("Transaction committed")
            else:
                self.connection.execute("ROLLBACK")
                logger.warning(f"Transaction rolled back due to: {exc_val}")
        except Exception as e:
            logger.error(f"Error in transaction cleanup: {e}")
        finally:
            self.transaction_started = False


class ConnectionPool:
    """Thread-safe database connection pool with automatic cleanup."""
    
    def __init__(self, database_path: str, min_connections: int = 3, max_connections: int = 10):
        self.database_path = database_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        
        # Thread-safe connection management
        self.pool = queue.Queue(maxsize=max_connections)
        self.active_connections: Dict[int, ConnectionInfo] = {}
        self.pool_lock = threading.RLock()
        self.connection_counter = 0
        
        # Initialize minimum connections
        self._initialize_pool()
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_active = True
        self.cleanup_thread.start()
        
        logger.info(f"Connection pool initialized: {min_connections}-{max_connections} connections")
    
    def _initialize_pool(self):
        """Initialize the connection pool with minimum connections."""
        for _ in range(self.min_connections):
            conn_info = self._create_connection()
            if conn_info:
                self.pool.put(conn_info)
    
    def _create_connection(self) -> Optional[ConnectionInfo]:
        """Create a new database connection."""
        try:
            connection = sqlite3.connect(
                self.database_path,
                check_same_thread=False,
                timeout=30.0,
                isolation_level=None  # Autocommit mode, we'll handle transactions manually
            )
            connection.row_factory = sqlite3.Row
            
            # Enable foreign key constraints
            connection.execute("PRAGMA foreign_keys = ON")
            
            # Optimize for performance
            connection.execute("PRAGMA journal_mode = WAL")
            connection.execute("PRAGMA synchronous = NORMAL")
            connection.execute("PRAGMA cache_size = 10000")
            
            conn_info = ConnectionInfo(
                connection=connection,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            with self.pool_lock:
                self.connection_counter += 1
                conn_id = self.connection_counter
                self.active_connections[conn_id] = conn_info
            
            logger.debug(f"Created new database connection: {conn_id}")
            return conn_info
            
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None
    
    def get_connection(self, timeout: float = 5.0) -> Optional[ConnectionInfo]:
        """Get a connection from the pool."""
        try:
            # Try to get from pool first
            conn_info = self.pool.get(timeout=timeout)
            conn_info.last_used = datetime.now()
            conn_info.in_use = True
            return conn_info
            
        except queue.Empty:
            # Pool is empty, try to create new connection if under limit
            with self.pool_lock:
                if len(self.active_connections) < self.max_connections:
                    conn_info = self._create_connection()
                    if conn_info:
                        conn_info.in_use = True
                        return conn_info
            
            logger.warning("Connection pool exhausted and at maximum capacity")
            return None
    
    def return_connection(self, conn_info: ConnectionInfo):
        """Return a connection to the pool."""
        if not conn_info:
            return
        
        try:
            # Rollback any active transaction
            if conn_info.transaction_active:
                conn_info.connection.execute("ROLLBACK")
                conn_info.transaction_active = False
            
            conn_info.in_use = False
            conn_info.last_used = datetime.now()
            
            # Return to pool if not full
            try:
                self.pool.put_nowait(conn_info)
            except queue.Full:
                # Pool is full, close this connection
                self._close_connection(conn_info)
                
        except Exception as e:
            logger.error(f"Error returning connection to pool: {e}")
            self._close_connection(conn_info)
    
    def _close_connection(self, conn_info: ConnectionInfo):
        """Close a database connection."""
        try:
            conn_info.connection.close()
            
            # Remove from active connections
            with self.pool_lock:
                for conn_id, info in list(self.active_connections.items()):
                    if info is conn_info:
                        del self.active_connections[conn_id]
                        break
            
            logger.debug("Database connection closed")
            
        except Exception as e:
            logger.error(f"Error closing database connection: {e}")
    
    def _cleanup_loop(self):
        """Cleanup loop to remove stale connections."""
        while self.cleanup_active:
            try:
                current_time = datetime.now()
                stale_connections = []
                
                with self.pool_lock:
                    for conn_id, conn_info in self.active_connections.items():
                        # Close connections idle for more than 30 minutes
                        if (not conn_info.in_use and 
                            (current_time - conn_info.last_used).total_seconds() > 1800):
                            stale_connections.append(conn_info)
                
                # Close stale connections
                for conn_info in stale_connections:
                    self._close_connection(conn_info)
                
                time.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in connection cleanup: {e}")
                time.sleep(300)
    
    def close_all(self):
        """Close all connections and shutdown pool."""
        self.cleanup_active = False
        
        with self.pool_lock:
            # Close all active connections
            for conn_info in list(self.active_connections.values()):
                self._close_connection(conn_info)
            
            # Clear the pool
            while not self.pool.empty():
                try:
                    conn_info = self.pool.get_nowait()
                    self._close_connection(conn_info)
                except queue.Empty:
                    break
        
        logger.info("Connection pool closed")


class EnhancedDatabaseManager:
    """
    Enhanced database manager with connection pooling, transaction safety,
    and improved error handling.
    """
    
    def __init__(self, db_config: Dict[str, Any] = None):
        """Initialize the enhanced database manager."""
        self.db_config = db_config or {
            'database': str(Path.home() / ".aretomo3_gui" / "aretomo3.db"),
            'min_connections': 3,
            'max_connections': 10,
            'backup_enabled': True,
            'backup_interval_hours': 24
        }
        
        # Ensure database directory exists
        db_path = Path(self.db_config['database'])
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize connection pool
        self.connection_pool = ConnectionPool(
            database_path=self.db_config['database'],
            min_connections=self.db_config.get('min_connections', 3),
            max_connections=self.db_config.get('max_connections', 10)
        )
        
        # Initialize database schema
        self._initialize_schema()
        
        logger.info("Enhanced Database Manager initialized")
    
    def _initialize_schema(self):
        """Initialize database schema with proper indexes."""
        with self.get_connection() as conn:
            with TransactionManager(conn.connection):
                # Create tables with proper constraints
                conn.connection.executescript("""
                    CREATE TABLE IF NOT EXISTS processing_sessions (
                        session_id TEXT PRIMARY KEY,
                        project_name TEXT NOT NULL,
                        start_time TIMESTAMP NOT NULL,
                        end_time TIMESTAMP,
                        status TEXT NOT NULL CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
                        input_directory TEXT NOT NULL,
                        output_directory TEXT NOT NULL,
                        parameters TEXT,
                        results_summary TEXT,
                        user_name TEXT,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    
                    CREATE TABLE IF NOT EXISTS tilt_series (
                        series_id TEXT PRIMARY KEY,
                        session_id TEXT NOT NULL,
                        series_name TEXT NOT NULL,
                        input_path TEXT NOT NULL,
                        output_path TEXT,
                        processing_time REAL,
                        status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
                        tilt_angles TEXT,
                        pixel_size REAL,
                        voltage REAL,
                        dose_rate REAL,
                        ctf_parameters TEXT,
                        motion_parameters TEXT,
                        alignment_parameters TEXT,
                        quality_metrics TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES processing_sessions (session_id) ON DELETE CASCADE
                    );
                    
                    CREATE TABLE IF NOT EXISTS quality_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        series_id TEXT NOT NULL,
                        resolution_estimate REAL,
                        motion_correction_quality REAL,
                        ctf_estimation_quality REAL,
                        alignment_quality REAL,
                        overall_score REAL,
                        outlier_frames TEXT,
                        recommendations TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (series_id) REFERENCES tilt_series (series_id) ON DELETE CASCADE
                    );
                    
                    CREATE TABLE IF NOT EXISTS processing_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        series_id TEXT,
                        log_level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES processing_sessions (session_id) ON DELETE CASCADE
                    );
                    
                    -- Create indexes for performance
                    CREATE INDEX IF NOT EXISTS idx_sessions_status ON processing_sessions (status);
                    CREATE INDEX IF NOT EXISTS idx_sessions_created ON processing_sessions (created_at);
                    CREATE INDEX IF NOT EXISTS idx_series_session ON tilt_series (session_id);
                    CREATE INDEX IF NOT EXISTS idx_series_status ON tilt_series (status);
                    CREATE INDEX IF NOT EXISTS idx_quality_series ON quality_metrics (series_id);
                    CREATE INDEX IF NOT EXISTS idx_logs_session ON processing_logs (session_id);
                    CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON processing_logs (timestamp);
                """)
        
        logger.info("Database schema initialized with indexes")
    
    def get_connection(self) -> ContextManager[ConnectionInfo]:
        """Get a database connection context manager."""
        return DatabaseConnectionContext(self.connection_pool)
    
    def execute_query(self, query: str, params: tuple = None, fetch_one: bool = False, 
                     fetch_all: bool = False) -> Any:
        """Execute a parameterized query safely."""
        with self.get_connection() as conn:
            try:
                cursor = conn.connection.cursor()
                cursor.execute(query, params or ())
                
                if fetch_one:
                    return cursor.fetchone()
                elif fetch_all:
                    return cursor.fetchall()
                else:
                    return cursor.rowcount
                    
            except Exception as e:
                logger.error(f"Database query failed: {e}")
                logger.error(f"Query: {query}")
                logger.error(f"Params: {params}")
                raise
    
    def execute_transaction(self, operations: List[tuple]) -> bool:
        """Execute multiple operations in a single transaction."""
        with self.get_connection() as conn:
            try:
                with TransactionManager(conn.connection):
                    cursor = conn.connection.cursor()
                    for query, params in operations:
                        cursor.execute(query, params or ())
                return True
                
            except Exception as e:
                logger.error(f"Transaction failed: {e}")
                return False
    
    def close(self):
        """Close the database manager and all connections."""
        self.connection_pool.close_all()
        logger.info("Enhanced Database Manager closed")


class DatabaseConnectionContext:
    """Context manager for database connections."""
    
    def __init__(self, connection_pool: ConnectionPool):
        self.connection_pool = connection_pool
        self.connection_info = None
    
    def __enter__(self) -> ConnectionInfo:
        self.connection_info = self.connection_pool.get_connection()
        if not self.connection_info:
            raise RuntimeError("Failed to get database connection from pool")
        return self.connection_info
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.connection_info:
            self.connection_pool.return_connection(self.connection_info)


# Global enhanced database manager instance
enhanced_database_manager = EnhancedDatabaseManager()


def get_enhanced_database_manager() -> EnhancedDatabaseManager:
    """Get the global enhanced database manager instance."""
    return enhanced_database_manager
