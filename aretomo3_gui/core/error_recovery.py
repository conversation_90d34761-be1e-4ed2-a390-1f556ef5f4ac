#!/usr/bin/env python3
"""
AreTomo3 GUI Error Recovery System
Handles errors gracefully and provides recovery mechanisms.
"""

import logging
import traceback
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import functools
import weakref

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorRecord:
    """Error record data structure."""
    timestamp: datetime
    error_type: str
    error_message: str
    traceback_str: str
    severity: ErrorSeverity
    component: str
    context: Dict[str, Any] = field(default_factory=dict)
    recovery_attempted: bool = False
    recovery_successful: bool = False
    recovery_method: Optional[str] = None
    occurrence_count: int = 1


class ErrorRecoverySystem:
    """
    Comprehensive error recovery system for AreTomo3 GUI.
    Handles errors gracefully and provides automatic recovery mechanisms.
    """
    
    def __init__(self):
        """Initialize the error recovery system."""
        self.error_history: List[ErrorRecord] = []
        self.max_history_size = 1000
        self.recovery_strategies: Dict[str, Callable] = {}
        self.error_patterns: Dict[str, ErrorSeverity] = {}
        self.recovery_locks: Dict[str, threading.Lock] = {}
        self.component_health: Dict[str, bool] = {}
        
        # Recovery settings
        self.max_recovery_attempts = 3
        self.recovery_cooldown = timedelta(minutes=5)
        self.auto_recovery_enabled = True
        
        # Initialize default recovery strategies
        self._register_default_strategies()
        self._register_error_patterns()
        
        logger.info("Error Recovery System initialized")
    
    def _register_default_strategies(self):
        """Register default recovery strategies."""
        self.recovery_strategies.update({
            "memory_error": self._recover_memory_error,
            "file_not_found": self._recover_file_error,
            "permission_error": self._recover_permission_error,
            "network_error": self._recover_network_error,
            "gui_freeze": self._recover_gui_freeze,
            "plot_error": self._recover_plot_error,
            "data_corruption": self._recover_data_corruption,
            "config_error": self._recover_config_error
        })
    
    def _register_error_patterns(self):
        """Register error patterns and their severities."""
        self.error_patterns.update({
            "MemoryError": ErrorSeverity.HIGH,
            "OutOfMemoryError": ErrorSeverity.HIGH,
            "FileNotFoundError": ErrorSeverity.MEDIUM,
            "PermissionError": ErrorSeverity.MEDIUM,
            "ConnectionError": ErrorSeverity.MEDIUM,
            "TimeoutError": ErrorSeverity.MEDIUM,
            "ValueError": ErrorSeverity.LOW,
            "KeyError": ErrorSeverity.LOW,
            "AttributeError": ErrorSeverity.LOW,
            "ImportError": ErrorSeverity.HIGH,
            "ModuleNotFoundError": ErrorSeverity.HIGH,
            "SystemExit": ErrorSeverity.CRITICAL,
            "KeyboardInterrupt": ErrorSeverity.CRITICAL
        })
    
    def handle_error(self, error: Exception, component: str, context: Dict[str, Any] = None) -> bool:
        """
        Handle an error with automatic recovery.
        
        Args:
            error: The exception that occurred
            component: Component where error occurred
            context: Additional context information
            
        Returns:
            bool: True if recovery was successful, False otherwise
        """
        try:
            # Create error record
            error_record = self._create_error_record(error, component, context or {})
            
            # Check for duplicate errors
            self._check_duplicate_error(error_record)
            
            # Store error
            self._store_error(error_record)
            
            # Log error
            self._log_error(error_record)
            
            # Attempt recovery if enabled
            if self.auto_recovery_enabled:
                return self._attempt_recovery(error_record)
            
            return False
            
        except Exception as recovery_error:
            logger.error(f"Error in error recovery system: {recovery_error}")
            return False
    
    def _create_error_record(self, error: Exception, component: str, context: Dict[str, Any]) -> ErrorRecord:
        """Create an error record from an exception."""
        error_type = type(error).__name__
        error_message = str(error)
        traceback_str = traceback.format_exc()
        
        # Determine severity
        severity = self.error_patterns.get(error_type, ErrorSeverity.MEDIUM)
        
        return ErrorRecord(
            timestamp=datetime.now(),
            error_type=error_type,
            error_message=error_message,
            traceback_str=traceback_str,
            severity=severity,
            component=component,
            context=context
        )
    
    def _check_duplicate_error(self, error_record: ErrorRecord):
        """Check for duplicate errors and update occurrence count."""
        for existing_error in reversed(self.error_history[-10:]):  # Check last 10 errors
            if (existing_error.error_type == error_record.error_type and
                existing_error.component == error_record.component and
                existing_error.error_message == error_record.error_message):
                
                # Update occurrence count
                existing_error.occurrence_count += 1
                error_record.occurrence_count = existing_error.occurrence_count
                break
    
    def _store_error(self, error_record: ErrorRecord):
        """Store error record in history."""
        self.error_history.append(error_record)
        
        # Trim history if too large
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
        
        # Update component health
        self.component_health[error_record.component] = False
    
    def _log_error(self, error_record: ErrorRecord):
        """Log error with appropriate level."""
        log_message = (f"Error in {error_record.component}: "
                      f"{error_record.error_type}: {error_record.error_message}")
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _attempt_recovery(self, error_record: ErrorRecord) -> bool:
        """Attempt to recover from an error."""
        component = error_record.component
        
        # Check recovery cooldown
        if not self._check_recovery_cooldown(component):
            logger.info(f"Recovery for {component} in cooldown period")
            return False
        
        # Check max attempts
        recent_attempts = self._count_recent_recovery_attempts(component)
        if recent_attempts >= self.max_recovery_attempts:
            logger.warning(f"Max recovery attempts reached for {component}")
            return False
        
        # Get recovery lock
        if component not in self.recovery_locks:
            self.recovery_locks[component] = threading.Lock()
        
        with self.recovery_locks[component]:
            return self._execute_recovery(error_record)
    
    def _execute_recovery(self, error_record: ErrorRecord) -> bool:
        """Execute recovery strategy."""
        try:
            error_record.recovery_attempted = True
            
            # Determine recovery strategy
            strategy_key = self._get_recovery_strategy_key(error_record)
            recovery_strategy = self.recovery_strategies.get(strategy_key)
            
            if recovery_strategy:
                logger.info(f"Attempting recovery for {error_record.component} using {strategy_key}")
                success = recovery_strategy(error_record)
                
                error_record.recovery_successful = success
                error_record.recovery_method = strategy_key
                
                if success:
                    logger.info(f"Recovery successful for {error_record.component}")
                    self.component_health[error_record.component] = True
                else:
                    logger.warning(f"Recovery failed for {error_record.component}")
                
                return success
            else:
                logger.warning(f"No recovery strategy found for {error_record.error_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error during recovery execution: {e}")
            return False
    
    def _get_recovery_strategy_key(self, error_record: ErrorRecord) -> str:
        """Determine the appropriate recovery strategy key."""
        error_type = error_record.error_type.lower()
        
        if "memory" in error_type:
            return "memory_error"
        elif "file" in error_type or "notfound" in error_type:
            return "file_not_found"
        elif "permission" in error_type:
            return "permission_error"
        elif "connection" in error_type or "network" in error_type:
            return "network_error"
        elif "plot" in error_record.component.lower():
            return "plot_error"
        elif "config" in error_record.component.lower():
            return "config_error"
        else:
            return "generic_error"
    
    def _check_recovery_cooldown(self, component: str) -> bool:
        """Check if component is in recovery cooldown."""
        cutoff_time = datetime.now() - self.recovery_cooldown
        
        for error_record in reversed(self.error_history):
            if (error_record.component == component and
                error_record.recovery_attempted and
                error_record.timestamp > cutoff_time):
                return False
        
        return True
    
    def _count_recent_recovery_attempts(self, component: str) -> int:
        """Count recent recovery attempts for a component."""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        count = 0
        for error_record in reversed(self.error_history):
            if (error_record.component == component and
                error_record.recovery_attempted and
                error_record.timestamp > cutoff_time):
                count += 1
        
        return count
    
    # Recovery strategy implementations
    def _recover_memory_error(self, error_record: ErrorRecord) -> bool:
        """Recover from memory errors."""
        try:
            import gc
            gc.collect()
            
            # Clear caches if available
            from .performance_monitor import performance_monitor
            performance_monitor._cleanup_caches()
            
            return True
        except Exception:
            return False
    
    def _recover_file_error(self, error_record: ErrorRecord) -> bool:
        """Recover from file errors."""
        try:
            # Create missing directories
            file_path = error_record.context.get('file_path')
            if file_path:
                from pathlib import Path
                Path(file_path).parent.mkdir(parents=True, exist_ok=True)
                return True
        except Exception:
            pass
        return False
    
    def _recover_permission_error(self, error_record: ErrorRecord) -> bool:
        """Recover from permission errors."""
        # Log suggestion for user action
        logger.info("Permission error detected. Please check file/directory permissions.")
        return False
    
    def _recover_network_error(self, error_record: ErrorRecord) -> bool:
        """Recover from network errors."""
        try:
            # Wait and retry
            time.sleep(2)
            return True
        except Exception:
            return False
    
    def _recover_gui_freeze(self, error_record: ErrorRecord) -> bool:
        """Recover from GUI freeze."""
        try:
            # Force GUI update
            from PyQt5.QtWidgets import QApplication
            if QApplication.instance():
                QApplication.processEvents()
            return True
        except Exception:
            return False
    
    def _recover_plot_error(self, error_record: ErrorRecord) -> bool:
        """Recover from plotting errors."""
        try:
            import matplotlib.pyplot as plt
            plt.close('all')
            return True
        except Exception:
            return False
    
    def _recover_data_corruption(self, error_record: ErrorRecord) -> bool:
        """Recover from data corruption."""
        # Log suggestion for data reload
        logger.info("Data corruption detected. Consider reloading data.")
        return False
    
    def _recover_config_error(self, error_record: ErrorRecord) -> bool:
        """Recover from configuration errors."""
        try:
            # Reset to default configuration
            from ..core.config_manager import config_manager
            config_manager.reset_to_defaults()
            return True
        except Exception:
            return False
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get error summary statistics."""
        if not self.error_history:
            return {"status": "no_errors"}
        
        recent_errors = [e for e in self.error_history 
                        if e.timestamp > datetime.now() - timedelta(hours=24)]
        
        error_counts = {}
        component_errors = {}
        
        for error in recent_errors:
            error_counts[error.error_type] = error_counts.get(error.error_type, 0) + 1
            component_errors[error.component] = component_errors.get(error.component, 0) + 1
        
        recovery_success_rate = 0
        if recent_errors:
            successful_recoveries = sum(1 for e in recent_errors if e.recovery_successful)
            recovery_success_rate = successful_recoveries / len(recent_errors) * 100
        
        return {
            "total_errors": len(self.error_history),
            "recent_errors_24h": len(recent_errors),
            "error_types": error_counts,
            "component_errors": component_errors,
            "recovery_success_rate": recovery_success_rate,
            "component_health": self.component_health.copy(),
            "auto_recovery_enabled": self.auto_recovery_enabled
        }


def error_handler(component: str, context: Dict[str, Any] = None):
    """Decorator for automatic error handling."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_recovery_system.handle_error(e, component, context)
                raise
        return wrapper
    return decorator


# Global error recovery system instance
error_recovery_system = ErrorRecoverySystem()
