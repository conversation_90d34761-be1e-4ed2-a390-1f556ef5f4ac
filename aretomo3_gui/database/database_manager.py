#!/usr/bin/env python3
"""
AreTomo3 GUI Database Integration Framework
Comprehensive database management for processing results, metadata, and analysis.
"""

import logging
import sqlite3
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
import hashlib

# Optional database backends
try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

try:
    import pymongo
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ProcessingSession:
    """Processing session record."""
    session_id: str
    project_name: str
    start_time: datetime
    end_time: Optional[datetime]
    status: str  # running, completed, failed, cancelled
    input_directory: str
    output_directory: str
    parameters: Dict[str, Any]
    results_summary: Dict[str, Any]
    user: str
    notes: str


@dataclass
class TiltSeriesRecord:
    """Tilt series processing record."""
    series_id: str
    session_id: str
    series_name: str
    input_path: str
    output_path: str
    processing_time: float
    status: str
    tilt_angles: List[float]
    pixel_size: float
    voltage: float
    dose_rate: float
    ctf_parameters: Dict[str, Any]
    motion_parameters: Dict[str, Any]
    alignment_parameters: Dict[str, Any]
    quality_metrics: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


@dataclass
class QualityMetrics:
    """Quality assessment metrics."""
    series_id: str
    resolution_estimate: float
    motion_correction_quality: float
    ctf_estimation_quality: float
    alignment_quality: float
    overall_score: float
    outlier_frames: List[int]
    recommendations: List[str]
    timestamp: datetime


class DatabaseManager:
    """
    Comprehensive database manager for AreTomo3 GUI.
    Supports SQLite, PostgreSQL, and MongoDB backends.
    """
    
    def __init__(self, db_config: Dict[str, Any] = None):
        """Initialize the database manager."""
        self.db_config = db_config or {
            'backend': 'sqlite',
            'database': str(Path.home() / ".aretomo3_gui" / "aretomo3.db"),
            'auto_backup': True,
            'backup_interval_hours': 24
        }
        
        self.backend = self.db_config.get('backend', 'sqlite')
        self.connection = None
        self.lock = threading.Lock()
        
        # Initialize database
        self._initialize_database()
        
        logger.info(f"Database Manager initialized with {self.backend} backend")
    
    def _initialize_database(self):
        """Initialize database connection and schema."""
        try:
            if self.backend == 'sqlite':
                self._initialize_sqlite()
            elif self.backend == 'postgresql' and POSTGRESQL_AVAILABLE:
                self._initialize_postgresql()
            elif self.backend == 'mongodb' and MONGODB_AVAILABLE:
                self._initialize_mongodb()
            else:
                logger.warning(f"Backend {self.backend} not available, falling back to SQLite")
                self.backend = 'sqlite'
                self._initialize_sqlite()
        
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            # Fallback to in-memory SQLite
            self.backend = 'sqlite'
            self.db_config['database'] = ':memory:'
            self._initialize_sqlite()
    
    def _initialize_sqlite(self):
        """Initialize SQLite database."""
        db_path = Path(self.db_config['database'])
        if db_path != Path(':memory:'):
            db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.connection = sqlite3.connect(
            self.db_config['database'],
            check_same_thread=False,
            timeout=30.0
        )
        self.connection.row_factory = sqlite3.Row
        
        # Create tables
        self._create_sqlite_tables()
        
        logger.info(f"SQLite database initialized: {self.db_config['database']}")
    
    def _create_sqlite_tables(self):
        """Create SQLite tables."""
        cursor = self.connection.cursor()
        
        # Processing sessions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS processing_sessions (
                session_id TEXT PRIMARY KEY,
                project_name TEXT NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                status TEXT NOT NULL,
                input_directory TEXT NOT NULL,
                output_directory TEXT NOT NULL,
                parameters TEXT,
                results_summary TEXT,
                user_name TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tilt series table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tilt_series (
                series_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                series_name TEXT NOT NULL,
                input_path TEXT NOT NULL,
                output_path TEXT,
                processing_time REAL,
                status TEXT NOT NULL,
                tilt_angles TEXT,
                pixel_size REAL,
                voltage REAL,
                dose_rate REAL,
                ctf_parameters TEXT,
                motion_parameters TEXT,
                alignment_parameters TEXT,
                quality_metrics TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES processing_sessions (session_id)
            )
        """)
        
        # Quality metrics table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS quality_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                series_id TEXT NOT NULL,
                resolution_estimate REAL,
                motion_correction_quality REAL,
                ctf_estimation_quality REAL,
                alignment_quality REAL,
                overall_score REAL,
                outlier_frames TEXT,
                recommendations TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (series_id) REFERENCES tilt_series (series_id)
            )
        """)
        
        # Processing logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS processing_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                series_id TEXT,
                log_level TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES processing_sessions (session_id)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_status ON processing_sessions (status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_series_session ON tilt_series (session_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_series_status ON tilt_series (status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_quality_series ON quality_metrics (series_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_session ON processing_logs (session_id)")
        
        self.connection.commit()
        logger.info("SQLite tables created successfully")
    
    def create_session(self, session: ProcessingSession) -> bool:
        """Create a new processing session."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._create_session_sqlite(session)
                # Add other backends here
            return False
        
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return False
    
    def _create_session_sqlite(self, session: ProcessingSession) -> bool:
        """Create session in SQLite."""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO processing_sessions (
                session_id, project_name, start_time, end_time, status,
                input_directory, output_directory, parameters, results_summary,
                user_name, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            session.session_id,
            session.project_name,
            session.start_time,
            session.end_time,
            session.status,
            session.input_directory,
            session.output_directory,
            json.dumps(session.parameters),
            json.dumps(session.results_summary),
            session.user,
            session.notes
        ))
        
        self.connection.commit()
        logger.info(f"Created session: {session.session_id}")
        return True
    
    def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update a processing session."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._update_session_sqlite(session_id, updates)
            return False
        
        except Exception as e:
            logger.error(f"Error updating session: {e}")
            return False
    
    def _update_session_sqlite(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update session in SQLite with secure parameter handling."""
        cursor = self.connection.cursor()

        # Whitelist of allowed columns to prevent SQL injection
        allowed_columns = {
            'project_name', 'start_time', 'end_time', 'status',
            'input_directory', 'output_directory', 'parameters',
            'results_summary', 'user_name', 'notes'
        }

        # Build dynamic update query with validated columns
        set_clauses = []
        values = []

        for key, value in updates.items():
            # Validate column name against whitelist
            if key not in allowed_columns:
                logger.warning(f"Attempted to update invalid column: {key}")
                continue

            if key in ['parameters', 'results_summary'] and isinstance(value, dict):
                set_clauses.append(f"{key} = ?")
                values.append(json.dumps(value))
            else:
                set_clauses.append(f"{key} = ?")
                values.append(value)

        if not set_clauses:
            logger.warning("No valid columns to update")
            return False

        set_clauses.append("updated_at = CURRENT_TIMESTAMP")
        values.append(session_id)

        query = f"UPDATE processing_sessions SET {', '.join(set_clauses)} WHERE session_id = ?"

        cursor.execute(query, values)
        self.connection.commit()

        logger.info(f"Updated session: {session_id}")
        return True
    
    def add_tilt_series(self, series: TiltSeriesRecord) -> bool:
        """Add a tilt series record."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._add_tilt_series_sqlite(series)
            return False
        
        except Exception as e:
            logger.error(f"Error adding tilt series: {e}")
            return False
    
    def _add_tilt_series_sqlite(self, series: TiltSeriesRecord) -> bool:
        """Add tilt series to SQLite."""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO tilt_series (
                series_id, session_id, series_name, input_path, output_path,
                processing_time, status, tilt_angles, pixel_size, voltage,
                dose_rate, ctf_parameters, motion_parameters, alignment_parameters,
                quality_metrics, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            series.series_id,
            series.session_id,
            series.series_name,
            series.input_path,
            series.output_path,
            series.processing_time,
            series.status,
            json.dumps(series.tilt_angles),
            series.pixel_size,
            series.voltage,
            series.dose_rate,
            json.dumps(series.ctf_parameters),
            json.dumps(series.motion_parameters),
            json.dumps(series.alignment_parameters),
            json.dumps(series.quality_metrics),
            series.created_at,
            series.updated_at
        ))
        
        self.connection.commit()
        logger.info(f"Added tilt series: {series.series_id}")
        return True
    
    def get_session(self, session_id: str) -> Optional[ProcessingSession]:
        """Get a processing session by ID."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._get_session_sqlite(session_id)
            return None
        
        except Exception as e:
            logger.error(f"Error getting session: {e}")
            return None
    
    def _get_session_sqlite(self, session_id: str) -> Optional[ProcessingSession]:
        """Get session from SQLite."""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            SELECT * FROM processing_sessions WHERE session_id = ?
        """, (session_id,))
        
        row = cursor.fetchone()
        if not row:
            return None
        
        return ProcessingSession(
            session_id=row['session_id'],
            project_name=row['project_name'],
            start_time=datetime.fromisoformat(row['start_time']),
            end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
            status=row['status'],
            input_directory=row['input_directory'],
            output_directory=row['output_directory'],
            parameters=json.loads(row['parameters']) if row['parameters'] else {},
            results_summary=json.loads(row['results_summary']) if row['results_summary'] else {},
            user=row['user_name'] or '',
            notes=row['notes'] or ''
        )
    
    def get_sessions(self, filters: Dict[str, Any] = None) -> List[ProcessingSession]:
        """Get processing sessions with optional filters."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._get_sessions_sqlite(filters)
            return []
        
        except Exception as e:
            logger.error(f"Error getting sessions: {e}")
            return []
    
    def _get_sessions_sqlite(self, filters: Dict[str, Any] = None) -> List[ProcessingSession]:
        """Get sessions from SQLite with secure filtering."""
        cursor = self.connection.cursor()

        query = "SELECT * FROM processing_sessions"
        params = []

        if filters:
            # Whitelist of allowed filter columns
            allowed_filters = {'status', 'project_name', 'user', 'start_time', 'end_time'}

            where_clauses = []
            for key, value in filters.items():
                # Validate filter key against whitelist
                if key not in allowed_filters:
                    logger.warning(f"Invalid filter key ignored: {key}")
                    continue

                if key == 'status':
                    where_clauses.append("status = ?")
                    params.append(value)
                elif key == 'project_name':
                    where_clauses.append("project_name LIKE ?")
                    params.append(f"%{value}%")
                elif key == 'user':
                    where_clauses.append("user_name = ?")
                    params.append(value)
                elif key == 'start_time':
                    where_clauses.append("start_time >= ?")
                    params.append(value)
                elif key == 'end_time':
                    where_clauses.append("end_time <= ?")
                    params.append(value)

            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

        query += " ORDER BY start_time DESC"

        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        sessions = []
        for row in rows:
            sessions.append(ProcessingSession(
                session_id=row['session_id'],
                project_name=row['project_name'],
                start_time=datetime.fromisoformat(row['start_time']),
                end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                status=row['status'],
                input_directory=row['input_directory'],
                output_directory=row['output_directory'],
                parameters=json.loads(row['parameters']) if row['parameters'] else {},
                results_summary=json.loads(row['results_summary']) if row['results_summary'] else {},
                user=row['user_name'] or '',
                notes=row['notes'] or ''
            ))
        
        return sessions
    
    def add_quality_metrics(self, metrics: QualityMetrics) -> bool:
        """Add quality metrics for a tilt series."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._add_quality_metrics_sqlite(metrics)
            return False
        
        except Exception as e:
            logger.error(f"Error adding quality metrics: {e}")
            return False
    
    def _add_quality_metrics_sqlite(self, metrics: QualityMetrics) -> bool:
        """Add quality metrics to SQLite."""
        cursor = self.connection.cursor()
        
        cursor.execute("""
            INSERT INTO quality_metrics (
                series_id, resolution_estimate, motion_correction_quality,
                ctf_estimation_quality, alignment_quality, overall_score,
                outlier_frames, recommendations, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            metrics.series_id,
            metrics.resolution_estimate,
            metrics.motion_correction_quality,
            metrics.ctf_estimation_quality,
            metrics.alignment_quality,
            metrics.overall_score,
            json.dumps(metrics.outlier_frames),
            json.dumps(metrics.recommendations),
            metrics.timestamp
        ))
        
        self.connection.commit()
        logger.info(f"Added quality metrics for series: {metrics.series_id}")
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            with self.lock:
                if self.backend == 'sqlite':
                    return self._get_statistics_sqlite()
            return {}
        
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}
    
    def _get_statistics_sqlite(self) -> Dict[str, Any]:
        """Get statistics from SQLite."""
        cursor = self.connection.cursor()
        
        stats = {}
        
        # Session statistics
        cursor.execute("SELECT COUNT(*) as total FROM processing_sessions")
        stats['total_sessions'] = cursor.fetchone()['total']
        
        cursor.execute("SELECT status, COUNT(*) as count FROM processing_sessions GROUP BY status")
        stats['sessions_by_status'] = {row['status']: row['count'] for row in cursor.fetchall()}
        
        # Tilt series statistics
        cursor.execute("SELECT COUNT(*) as total FROM tilt_series")
        stats['total_series'] = cursor.fetchone()['total']
        
        cursor.execute("SELECT status, COUNT(*) as count FROM tilt_series GROUP BY status")
        stats['series_by_status'] = {row['status']: row['count'] for row in cursor.fetchall()}
        
        # Quality statistics
        cursor.execute("""
            SELECT 
                AVG(overall_score) as avg_score,
                MIN(overall_score) as min_score,
                MAX(overall_score) as max_score
            FROM quality_metrics
        """)
        quality_row = cursor.fetchone()
        if quality_row and quality_row['avg_score'] is not None:
            stats['quality_scores'] = {
                'average': quality_row['avg_score'],
                'minimum': quality_row['min_score'],
                'maximum': quality_row['max_score']
            }
        
        return stats
    
    def backup_database(self, backup_path: Path = None) -> bool:
        """Create a database backup."""
        try:
            if self.backend != 'sqlite':
                logger.warning("Backup only supported for SQLite currently")
                return False
            
            if not backup_path:
                backup_dir = Path.home() / ".aretomo3_gui" / "backups"
                backup_dir.mkdir(parents=True, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_dir / f"aretomo3_db_backup_{timestamp}.db"
            
            # Create backup
            backup_conn = sqlite3.connect(str(backup_path))
            self.connection.backup(backup_conn)
            backup_conn.close()
            
            logger.info(f"Database backup created: {backup_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False
    
    def close(self):
        """Close database connection."""
        try:
            if self.connection:
                self.connection.close()
                logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database: {e}")


# Global database manager instance
database_manager = DatabaseManager()


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    return database_manager
