#!/usr/bin/env python3
"""
AreTomo3 GUI Distributed Computing Framework
High-performance computing cluster integration for large-scale tomographic processing.
"""

import logging
import asyncio
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import subprocess
import threading
import queue
import time

# Distributed computing imports with fallbacks
try:
    import dask
    from dask.distributed import Client, as_completed
    from dask import delayed
    DASK_AVAILABLE = True
except ImportError:
    DASK_AVAILABLE = False

try:
    import ray
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

try:
    from mpi4py import MPI
    MPI_AVAILABLE = True
except ImportError:
    MPI_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ClusterConfig:
    """Cluster configuration."""
    cluster_type: str  # slurm, pbs, sge, kubernetes, local
    scheduler_address: str
    num_workers: int
    worker_memory: str
    worker_cores: int
    queue_name: str
    walltime: str
    project_id: str
    modules_to_load: List[str]
    environment_setup: List[str]


@dataclass
class ComputeJob:
    """Distributed compute job."""
    job_id: str
    job_name: str
    job_type: str
    status: str
    submitted_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    input_files: List[str]
    output_files: List[str]
    parameters: Dict[str, Any]
    resources: Dict[str, Any]
    progress: float
    error_message: Optional[str]
    cluster_job_id: Optional[str]


@dataclass
class WorkerNode:
    """Worker node information."""
    node_id: str
    hostname: str
    cores: int
    memory_gb: float
    gpu_count: int
    status: str  # idle, busy, offline
    current_jobs: List[str]
    load_average: float
    last_heartbeat: datetime


class ClusterManager:
    """
    Distributed computing cluster manager for AreTomo3 GUI.
    Handles job submission, monitoring, and resource management across HPC clusters.
    """
    
    def __init__(self, config_file: Path = None):
        """Initialize the cluster manager."""
        self.config_file = config_file or Path.home() / ".aretomo3_gui" / "cluster_config.json"
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Cluster configurations
        self.cluster_configs: Dict[str, ClusterConfig] = {}
        self.active_cluster: Optional[str] = None
        
        # Job management
        self.active_jobs: Dict[str, ComputeJob] = {}
        self.job_queue = queue.Queue()
        self.job_callbacks: Dict[str, Callable] = {}
        
        # Worker management
        self.worker_nodes: Dict[str, WorkerNode] = {}
        
        # Distributed computing clients
        self.dask_client = None
        self.ray_cluster = None
        
        # Monitoring
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Load configuration
        self._load_config()
        
        logger.info("Cluster Manager initialized")
    
    def add_cluster_config(self, name: str, config: ClusterConfig) -> bool:
        """Add a cluster configuration."""
        try:
            self.cluster_configs[name] = config
            self._save_config()
            
            logger.info(f"Cluster configuration added: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding cluster config: {e}")
            return False
    
    def connect_to_cluster(self, cluster_name: str) -> bool:
        """Connect to a cluster."""
        if cluster_name not in self.cluster_configs:
            logger.error(f"Cluster configuration not found: {cluster_name}")
            return False
        
        try:
            config = self.cluster_configs[cluster_name]
            
            if config.cluster_type == "dask" and DASK_AVAILABLE:
                return self._connect_dask_cluster(config)
            elif config.cluster_type == "ray" and RAY_AVAILABLE:
                return self._connect_ray_cluster(config)
            elif config.cluster_type in ["slurm", "pbs", "sge"]:
                return self._connect_hpc_cluster(config)
            else:
                logger.error(f"Unsupported cluster type: {config.cluster_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to cluster: {e}")
            return False
    
    def _connect_dask_cluster(self, config: ClusterConfig) -> bool:
        """Connect to Dask cluster."""
        try:
            self.dask_client = Client(config.scheduler_address)
            
            # Test connection
            self.dask_client.get_versions(check=True)
            
            self.active_cluster = "dask"
            logger.info(f"Connected to Dask cluster: {config.scheduler_address}")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to Dask cluster: {e}")
            return False
    
    def _connect_ray_cluster(self, config: ClusterConfig) -> bool:
        """Connect to Ray cluster."""
        try:
            ray.init(address=config.scheduler_address)
            
            self.active_cluster = "ray"
            logger.info(f"Connected to Ray cluster: {config.scheduler_address}")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to Ray cluster: {e}")
            return False
    
    def _connect_hpc_cluster(self, config: ClusterConfig) -> bool:
        """Connect to HPC cluster (SLURM/PBS/SGE)."""
        try:
            # Test cluster connectivity
            if config.cluster_type == "slurm":
                result = subprocess.run(["sinfo", "-h"], capture_output=True, text=True)
            elif config.cluster_type == "pbs":
                result = subprocess.run(["qstat", "-B"], capture_output=True, text=True)
            elif config.cluster_type == "sge":
                result = subprocess.run(["qhost"], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.active_cluster = config.cluster_type
                logger.info(f"Connected to {config.cluster_type.upper()} cluster")
                return True
            else:
                logger.error(f"Failed to connect to {config.cluster_type} cluster")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to HPC cluster: {e}")
            return False
    
    def submit_processing_job(self, job_name: str, job_type: str, 
                            input_files: List[str], parameters: Dict[str, Any],
                            resources: Dict[str, Any] = None,
                            callback: Callable = None) -> Optional[str]:
        """Submit a processing job to the cluster."""
        try:
            # Generate job ID
            job_id = self._generate_job_id()
            
            # Create job
            job = ComputeJob(
                job_id=job_id,
                job_name=job_name,
                job_type=job_type,
                status="submitted",
                submitted_at=datetime.now(),
                started_at=None,
                completed_at=None,
                input_files=input_files,
                output_files=[],
                parameters=parameters,
                resources=resources or {},
                progress=0.0,
                error_message=None,
                cluster_job_id=None
            )
            
            # Store job
            self.active_jobs[job_id] = job
            if callback:
                self.job_callbacks[job_id] = callback
            
            # Submit to cluster
            if self.active_cluster == "dask":
                self._submit_dask_job(job)
            elif self.active_cluster == "ray":
                self._submit_ray_job(job)
            elif self.active_cluster in ["slurm", "pbs", "sge"]:
                self._submit_hpc_job(job)
            else:
                logger.error("No active cluster connection")
                return None
            
            logger.info(f"Job submitted: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error submitting job: {e}")
            return None
    
    def _submit_dask_job(self, job: ComputeJob):
        """Submit job to Dask cluster."""
        try:
            if not self.dask_client:
                raise Exception("No Dask client available")
            
            # Create Dask delayed computation
            @delayed
            def process_tomogram(input_files, parameters):
                # This would be the actual processing function
                import time
                import numpy as np
                
                # Simulate processing
                for i in range(10):
                    time.sleep(1)
                    # Update progress (this would need proper communication)
                
                # Return results
                return {
                    'output_files': [f"output_{job.job_id}.mrc"],
                    'statistics': {'processing_time': 10.0}
                }
            
            # Submit computation
            future = process_tomogram(job.input_files, job.parameters)
            cluster_job = self.dask_client.compute(future)
            
            # Store cluster job reference
            job.cluster_job_id = str(id(cluster_job))
            job.status = "running"
            job.started_at = datetime.now()
            
            # Monitor job in background
            self._monitor_dask_job(job, cluster_job)
            
        except Exception as e:
            logger.error(f"Error submitting Dask job: {e}")
            job.status = "failed"
            job.error_message = str(e)
    
    def _submit_ray_job(self, job: ComputeJob):
        """Submit job to Ray cluster."""
        try:
            @ray.remote
            def process_tomogram_ray(input_files, parameters):
                import time
                
                # Simulate processing
                for i in range(10):
                    time.sleep(1)
                
                return {
                    'output_files': [f"output_{job.job_id}.mrc"],
                    'statistics': {'processing_time': 10.0}
                }
            
            # Submit to Ray
            future = process_tomogram_ray.remote(job.input_files, job.parameters)
            
            job.cluster_job_id = str(future)
            job.status = "running"
            job.started_at = datetime.now()
            
            # Monitor job
            self._monitor_ray_job(job, future)
            
        except Exception as e:
            logger.error(f"Error submitting Ray job: {e}")
            job.status = "failed"
            job.error_message = str(e)
    
    def _submit_hpc_job(self, job: ComputeJob):
        """Submit job to HPC cluster."""
        try:
            config = self.cluster_configs[self.active_cluster]
            
            # Create job script
            script_path = self._create_job_script(job, config)
            
            # Submit job
            if config.cluster_type == "slurm":
                cmd = ["sbatch", str(script_path)]
            elif config.cluster_type == "pbs":
                cmd = ["qsub", str(script_path)]
            elif config.cluster_type == "sge":
                cmd = ["qsub", str(script_path)]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Extract job ID from output
                cluster_job_id = self._extract_cluster_job_id(result.stdout, config.cluster_type)
                job.cluster_job_id = cluster_job_id
                job.status = "queued"
                
                logger.info(f"HPC job submitted: {cluster_job_id}")
            else:
                raise Exception(f"Job submission failed: {result.stderr}")
                
        except Exception as e:
            logger.error(f"Error submitting HPC job: {e}")
            job.status = "failed"
            job.error_message = str(e)
    
    def _create_job_script(self, job: ComputeJob, config: ClusterConfig) -> Path:
        """Create job script for HPC submission."""
        script_dir = Path.home() / ".aretomo3_gui" / "job_scripts"
        script_dir.mkdir(parents=True, exist_ok=True)
        
        script_path = script_dir / f"{job.job_id}.sh"
        
        # Create job script content
        if config.cluster_type == "slurm":
            script_content = f"""#!/bin/bash
#SBATCH --job-name={job.job_name}
#SBATCH --nodes=1
#SBATCH --ntasks-per-node={config.worker_cores}
#SBATCH --mem={config.worker_memory}
#SBATCH --time={config.walltime}
#SBATCH --partition={config.queue_name}
#SBATCH --account={config.project_id}
#SBATCH --output={job.job_id}.out
#SBATCH --error={job.job_id}.err

# Load modules
{chr(10).join([f'module load {mod}' for mod in config.modules_to_load])}

# Environment setup
{chr(10).join(config.environment_setup)}

# Run AreTomo3 processing
python -c "
import sys
sys.path.append('/path/to/aretomo3_gui')
from aretomo3_gui.processing.aretomo3_runner import run_aretomo3
run_aretomo3({json.dumps(job.parameters)})
"
"""
        
        elif config.cluster_type == "pbs":
            script_content = f"""#!/bin/bash
#PBS -N {job.job_name}
#PBS -l nodes=1:ppn={config.worker_cores}
#PBS -l mem={config.worker_memory}
#PBS -l walltime={config.walltime}
#PBS -q {config.queue_name}
#PBS -A {config.project_id}
#PBS -o {job.job_id}.out
#PBS -e {job.job_id}.err

cd $PBS_O_WORKDIR

# Load modules and run processing
{chr(10).join([f'module load {mod}' for mod in config.modules_to_load])}
{chr(10).join(config.environment_setup)}

python -c "
import sys
sys.path.append('/path/to/aretomo3_gui')
from aretomo3_gui.processing.aretomo3_runner import run_aretomo3
run_aretomo3({json.dumps(job.parameters)})
"
"""
        
        # Write script
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        script_path.chmod(0o755)
        
        return script_path
    
    def _extract_cluster_job_id(self, output: str, cluster_type: str) -> str:
        """Extract cluster job ID from submission output."""
        if cluster_type == "slurm":
            # SLURM output: "Submitted batch job 12345"
            return output.split()[-1].strip()
        elif cluster_type == "pbs":
            # PBS output: "12345.cluster.domain"
            return output.strip()
        elif cluster_type == "sge":
            # SGE output: "Your job 12345 ("job_name") has been submitted"
            return output.split()[2]
        
        return output.strip()
    
    def _monitor_dask_job(self, job: ComputeJob, cluster_job):
        """Monitor Dask job progress."""
        def monitor():
            try:
                result = cluster_job.result()
                job.status = "completed"
                job.completed_at = datetime.now()
                job.output_files = result.get('output_files', [])
                job.progress = 100.0
                
                # Call callback
                if job.job_id in self.job_callbacks:
                    self.job_callbacks[job.job_id](job)
                    
            except Exception as e:
                job.status = "failed"
                job.error_message = str(e)
                
                if job.job_id in self.job_callbacks:
                    self.job_callbacks[job.job_id](job)
        
        # Run monitoring in background thread
        threading.Thread(target=monitor, daemon=True).start()
    
    def _monitor_ray_job(self, job: ComputeJob, future):
        """Monitor Ray job progress."""
        def monitor():
            try:
                result = ray.get(future)
                job.status = "completed"
                job.completed_at = datetime.now()
                job.output_files = result.get('output_files', [])
                job.progress = 100.0
                
                if job.job_id in self.job_callbacks:
                    self.job_callbacks[job.job_id](job)
                    
            except Exception as e:
                job.status = "failed"
                job.error_message = str(e)
                
                if job.job_id in self.job_callbacks:
                    self.job_callbacks[job.job_id](job)
        
        threading.Thread(target=monitor, daemon=True).start()
    
    def get_job_status(self, job_id: str) -> Optional[ComputeJob]:
        """Get status of a compute job."""
        return self.active_jobs.get(job_id)
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a compute job."""
        if job_id not in self.active_jobs:
            return False
        
        try:
            job = self.active_jobs[job_id]
            
            if self.active_cluster in ["slurm", "pbs", "sge"] and job.cluster_job_id:
                # Cancel HPC job
                if self.active_cluster == "slurm":
                    subprocess.run(["scancel", job.cluster_job_id])
                elif self.active_cluster == "pbs":
                    subprocess.run(["qdel", job.cluster_job_id])
                elif self.active_cluster == "sge":
                    subprocess.run(["qdel", job.cluster_job_id])
            
            job.status = "cancelled"
            logger.info(f"Job cancelled: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling job: {e}")
            return False
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """Get cluster status information."""
        status = {
            'active_cluster': self.active_cluster,
            'connected': self.active_cluster is not None,
            'active_jobs': len(self.active_jobs),
            'worker_nodes': len(self.worker_nodes),
            'capabilities': {
                'dask': DASK_AVAILABLE,
                'ray': RAY_AVAILABLE,
                'mpi': MPI_AVAILABLE
            }
        }
        
        if self.dask_client:
            try:
                status['dask_workers'] = len(self.dask_client.scheduler_info()['workers'])
            except:
                pass
        
        return status
    
    def _generate_job_id(self) -> str:
        """Generate unique job ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        import hashlib
        random_suffix = hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()[:8]
        return f"job_{timestamp}_{random_suffix}"
    
    def _load_config(self):
        """Load cluster configuration."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                for name, config_data in data.get('clusters', {}).items():
                    self.cluster_configs[name] = ClusterConfig(**config_data)
                
                self.active_cluster = data.get('active_cluster')
                
        except Exception as e:
            logger.error(f"Error loading cluster config: {e}")
    
    def _save_config(self):
        """Save cluster configuration."""
        try:
            config_data = {
                'clusters': {name: asdict(config) for name, config in self.cluster_configs.items()},
                'active_cluster': self.active_cluster
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving cluster config: {e}")


# Global cluster manager instance
cluster_manager = ClusterManager()


def submit_distributed_job(job_name: str, job_type: str, input_files: List[str], 
                         parameters: Dict[str, Any]) -> Optional[str]:
    """Convenience function to submit distributed job."""
    return cluster_manager.submit_processing_job(job_name, job_type, input_files, parameters)
