#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main_window.py - AreTomo3 GUI Professional Edition

Part of the AreTomo3 GUI suite for tomographic reconstruction.
This file implements the main application window and user interface.

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License
"""

"""
Main window implementation for AreTomo3 GUI.
"""
import glob
import logging
import os
import platform
import re
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import mrcfile
import psutil
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# Use standard PyQt6 spinboxes (enhanced spinbox was removed during cleanup)
# We'll use the standard spinboxes with enhanced styling via CSS
from PyQt6.QtCore import (
    QT_VERSION_STR,
    QSize,
    Qt,
    QThread,
    QTimer,
    pyqtSignal,
    pyqtSlot,
)
from PyQt6.QtGui import QAction, QImage, QPixmap
from PyQt6.QtWidgets import (
    QA<PERSON>lication,
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QFormLayout,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QMainWindow,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QScrollArea,
    QSpinBox,
    QSplitter,
    QStatusBar,
    QTableWidget,
    QTableWidgetItem,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from ..core.config.config import ARETOMO3_SETTINGS, MICROSCOPE_SETTINGS
from ..core.config.config_validation import (
    AreTomo3Config,
    validate_aretomo_installation,
    validate_input_files,
    validate_output_directory,
)
from ..core.error_handling import FileSystemError, handle_exception, try_operation
from ..core.file_watcher import FileWatcher
from ..core.realtime_processor import RealTimeProcessor
from ..core.system_monitor import GPUMonitor, SystemMonitor
from ..core.thread_manager import TaskPriority, get_thread_manager
from ..utils.export_functions import (
    export_to_eman,
    export_to_imagej,
    export_to_mrc,
    export_to_relion,
    export_to_tiff,
)
from ..utils.mdoc_parser import parse_mdoc
from .advanced_settings_tab import AdvancedSettingsTab
from .components.parameter_manager import ParameterManager
from .tabs.enhanced_analysis_tab import EnhancedAnalysisTab
from .tabs.enhanced_parameters_tab import EnhancedParametersTab
from .tabs.reorganized_main_tab import ReorganizedMainTab
from .tabs.unified_live_processing_tab import UnifiedLiveProcessingTab
from .theme_manager import ThemeManager
from .viewers.analysis_viewer import AnalysisViewer
from .viewers.napari_mrc_viewer import NapariMRCViewer

# from .viewers.visualization import MetricsDashboard  # Temporarily
# commented out due to scipy issue
from .viewers.preview_grid import PreviewGridView
from .widgets.advanced_file_browser import AdvancedFileBrowser
from .widgets.batch_processing import BatchProcessingWidget
from .widgets.project_management import ProjectManagementWidget
from .widgets.resource_monitor import ResourceMonitor
from .widgets.unified_processing_monitor import UnifiedProcessingMonitor
from .widgets.web_server_widget import WebServerControlWidget

logger = logging.getLogger(__name__)

__all__ = ["AreTomo3MainWindow", "AreTomoGUI"]


class SystemResourceMonitor(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Create labels for system resources
        self.cpu_label = QLabel("CPU Usage: ---%")
        self.memory_label = QLabel("Memory Usage: ---%")
        self.disk_label = QLabel("Disk Usage: ---%")
        self.gpu_label = QLabel("GPU: Checking...")

        # Add labels to layout
        layout.addWidget(self.cpu_label)
        layout.addWidget(self.memory_label)
        layout.addWidget(self.disk_label)
        layout.addWidget(self.gpu_label)

        # Initialize system monitor
        self.monitor = SystemMonitor(update_interval=2.0)
        self.monitor.start()

        # Setup update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_stats)
        self.update_timer.start(2000)  # Update every 2 seconds

    def update_stats(self):
        """Update system statistics display"""
        data = self.monitor.get_latest_data()
        if not data:
            return

        if "error" in data:
            self.cpu_label.setText(f"Error: {data['error']}")
            return

        self.cpu_label.setText(f"CPU Usage: {data.get('cpu', 0):.1f}%")

        mem = data.get("memory", {})
        if mem:
            used_gb = mem.get("used", 0) / (1024**3)
            total_gb = mem.get("total", 0) / (1024**3)
            self.memory_label.setText(
                f"Memory: {
    used_gb:.1f}GB / {
        total_gb:.1f}GB ({
            mem.get(
                'percent',
                 0)}%)"
            )

        disk = data.get("disk", {})
        if disk:
            used_gb = disk.get("used", 0) / (1024**3)
            total_gb = disk.get("total", 0) / (1024**3)
            self.disk_label.setText(
                f"Disk: {
    used_gb:.1f}GB / {
        total_gb:.1f}GB ({
            disk.get(
                'percent',
                 0)}%)"
            )

        gpu = data.get("gpu")
        if gpu:
            self.gpu_label.setText(
                f"GPU: {gpu.get('name', 'Unknown')} - "
                f"Mem: {gpu.get(
                    'memory_used',
                    0)}MB / {gpu.get('memory_total',
                    0
                )}MB - "
                f"Temp: {gpu.get('temperature', 0)}°C - "
                f"Load: {gpu.get('utilization', 0)}%"
            )
        else:
            self.gpu_label.setText("GPU: Not available")

    def closeEvent(self, event):
        """Clean up when widget is closed"""
        self.monitor.stop()
        super().closeEvent(event)


class TiltSeries:
    def __init__(self, position_name):
        self.position_name = position_name
        self.files = []  # List of .eer files
        self.angles = []  # List of tilt angles
        self.mdoc_params = None  # Parameters from .mdoc file
        self.mdoc_file = None  # Path to .mdoc file
        self.gain_ref = None  # Path to gain reference file
        self.series_params = {  # Processing parameters (from mdoc or GUI)
            "pixel_size": 1.91,  # Default from example
            "tilt_axis": -95.75,  # Default from example
            "voltage": 300,  # Default from example
            "cs": 2.7,  # Default from example
            "amp_contrast": 0.1,  # Default from example
            "frame_dose": 0.14,  # Default from example
        }
        # AreTomo3 specific parameters
        self.aretomo_params = {
            "mc_bin": 1,  # Motion correction binning
            "mc_patch": [1, 1],  # Motion correction patch size
            "fm_int": 12,  # Frame interval
            "split_sum": 1,  # Split sum option
            "vol_z": 2048,  # Volume Z size
            "dark_tol": 0.7,  # Dark tolerance
            "flip_gain": 1,  # Flip gain flag
            "flip_vol": 1,  # Flip volume flag
            "out_imod": 1,  # Output IMOD files
            "out_xf": 1,  # Output transform files
            "wbp": 1,  # Weighted back-projection
            "tilt_cor": 1,  # Tilt correction
            "patch": [0, 0],  # Patch size
            "ctf": [1, 15],  # CTF correction [flag, lowpass]
            "at_bin": 4,  # Alignment binning
        }

    def add_file(self, filepath, angle):
        """Add a file to the tilt series and try to find its .mdoc file."""
        self.files.append(filepath)
        self.angles.append(angle)

        # Try different patterns to find the corresponding .mdoc file
        possible_mdoc_files = [
            filepath.replace("_EER.eer", ".mdoc"),  # Standard pattern
            os.path.join(
                os.path.dirname(filepath), f"{self.position_name}.mdoc"
            ),  # Position-based
            # Simple extension replacement
            os.path.splitext(filepath)[0] + ".mdoc",
        ]

        # Try each possible mdoc file location
        for mdoc_file in possible_mdoc_files:
            if os.path.exists(mdoc_file):
                self.mdoc_file = mdoc_file
                try:
                    self.mdoc_params = parse_mdoc(mdoc_file)
                    logger.info(f"Successfully loaded mdoc file: {mdoc_file}")

                    # Update series parameters from mdoc file
                    if self.mdoc_params:
                        for key in self.series_params.keys():
                            if (
                                key in self.mdoc_params
                                and self.series_params[key] is None
                            ):
                                self.series_params[key] = self.mdoc_params[key]

                    # Look for gain reference file in the same directory
                    gain_files = ["GainReference.gain", "gain.dm4", "gain.mrc"]
                    for gain_file in gain_files:
                        gain_path = os.path.join(
    os.path.dirname(filepath), gain_file)
                        if os.path.exists(gain_path):
                            self.gain_ref = gain_path
                            logger.info(f"Found gain reference: {gain_path}")
                            break

                    logger.info(f"Series parameters: {self.series_params}")
                    break
                except Exception as e:
                    logger.info(
    f"Error parsing mdoc file {mdoc_file}: {
        str(e)}")
                    logger.error(f"Error parsing mdoc file: {str(e)}")
                break

    def parse_mdoc(self, mdoc_file):
        """Parse MDOC file to extract parameters."""
        try:
            with open(mdoc_file, "r") as f:
                content = f.read()

            # Get basic parameters from header with careful error handling
            pixel_match = re.search(r"PixelSpacing\s*=\s*([\d.]+)", content)
            if pixel_match:
                self.series_params["pixel_size"] = float(pixel_match.group(1))

            voltage_match = re.search(r"Voltage\s*=\s*([\d.]+)", content)
            if voltage_match:
                self.series_params["voltage"] = int(
                    float(voltage_match.group(1)))

            # Get Cs value
            cs_match = re.search(r"Cs\s*=\s*([\d.]+)", content)
            if cs_match:
                self.series_params["cs"] = float(cs_match.group(1))

            # Get amplitude contrast
            amp_match = re.search(r"AmplitudeContrast\s*=\s*([\d.]+)", content)
            if amp_match:
                self.series_params["amp_contrast"] = float(amp_match.group(1))

            # Get tilt axis from header if available
            tilt_axis_match = re.search(
    r"TiltAxisAngle\s*=\s*([-\d.]+)", content)
            if tilt_axis_match:
                self.series_params["tilt_axis"] = float(
                    tilt_axis_match.group(1))

            # Get frame dose if available
            dose_match = re.search(r"ExposureDose\s*=\s*([\d.]+)", content)
            if dose_match:
                self.series_params["frame_dose"] = float(dose_match.group(1))

            # Log the parameters we found
            logger.info(
                f"Parameters found in MDOC for {
    self.position_name}: {
        self.series_params}"
            )

            # Extract tilt angles
            angles = []
            for match in re.finditer(
                r"\[ZValue = \d+\]\s*\nTiltAngle\s*=\s*([-\d.]+)", content
            ):
                angles.append(float(match.group(1)))

            if angles:
                self.angles = angles

            logger.info(f"Successfully parsed MDOC file: {mdoc_file}")
            self.mdoc_file = mdoc_file
            self.mdoc_params = content  # Store full content for reference

        except Exception as e:
            logger.error(f"Error parsing MDOC file {mdoc_file}: {str(e)}")
            raise

    def sort_by_angle(self):
        # Sort files by tilt angle
        sorted_pairs = sorted(zip(self.angles, self.files))
        self.angles, self.files = map(list, zip(*sorted_pairs))

    @staticmethod
    def parse_mdoc_file(mdoc_path: str) -> dict:
        """
        Parse MDOC file to extract tilt series information.

        Returns:
            dict: Contains series metadata and file mappings
        """
        try:
            series_info = {
    "files": [],
    "angles": [],
    "metadata": {},
     "frame_data": []}

            with open(mdoc_path, "r") as f:
                lines = f.readlines()

            # Parse global metadata
            current_frame = {}
            in_frame_section = False

            for line in lines:
                line = line.strip()
                if not line or line.startswith("[T ="):
                    continue

                if line.startswith("[ZValue"):
                    # Start of a new frame section
                    if current_frame and "TiltAngle" in current_frame:
                        series_info["frame_data"].append(current_frame.copy())
                    current_frame = {}
                    in_frame_section = True
                    continue

                # Parse key-value pairs
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()

                    if in_frame_section:
                        current_frame[key] = value
                    else:
                        series_info["metadata"][key] = value

            # Add the last frame
            if current_frame and "TiltAngle" in current_frame:
                series_info["frame_data"].append(current_frame)

            # Extract file paths and angles from frame data
            mdoc_dir = os.path.dirname(mdoc_path)

            for frame in series_info["frame_data"]:
                if "TiltAngle" in frame:
                    angle = float(frame["TiltAngle"])
                    series_info["angles"].append(angle)

                    # Try to find the corresponding file
                    file_path = None
                    if "SubFramePath" in frame:
                        # EER files typically have SubFramePath
                        subframe_path = frame["SubFramePath"]
                        if subframe_path.startswith("./"):
                            subframe_path = subframe_path[2:]
                        file_path = os.path.join(mdoc_dir, subframe_path)
                        if not os.path.exists(file_path):
                            # Try relative to parent directory
                            file_path = os.path.join(
                                os.path.dirname(mdoc_dir), subframe_path
                            )

                    if not file_path or not os.path.exists(file_path):
                        # Try to find files by matching angles with tolerance
                        file_path = TiltSeries._find_file_by_angle(
                            mdoc_dir, angle, mdoc_basename
                        )

                        if not file_path:
                            # Try to construct filename from MDOC name and
                            # angle
                            mdoc_basename = os.path.splitext(
                                os.path.basename(mdoc_path)
                            )[0]

                            # Try different naming conventions with rounded
                            # angles
                            # Round to 1 decimal place
                            rounded_angle = round(angle, 1)
                            possible_names = [
                                f"{mdoc_basename}[{angle:.2f}]_EER.eer",
                                f"{mdoc_basename}[{rounded_angle:.1f}]_EER.eer",
                                f"{mdoc_basename}[{int(round(angle)):d}.00]_EER.eer",
                                f"{mdoc_basename}_{
    len(
        series_info['files']) +
         1:03d}_{angle}.tif",
                                f"{mdoc_basename}_{
    len(
        series_info['files']) +
         1:03d}_{rounded_angle}.tif",
                                f"{mdoc_basename}_{
    len(
        series_info['files']) +
         1:03d}_{angle}.tiff",
                                f"{mdoc_basename}_{
    len(
        series_info['files']) +
         1:05d}_{angle}_*.tif",
                            ]

                            for name_pattern in possible_names:
                                if "*" in name_pattern:
                                    # Use glob for patterns with wildcards
                                    matches = glob.glob(
                                        os.path.join(mdoc_dir, name_pattern)
                                    )
                                    if matches:
                                        file_path = matches[0]
                                        break
                                else:
                                    test_path = os.path.join(
                                        mdoc_dir, name_pattern)
                                    if os.path.exists(test_path):
                                        file_path = test_path
                                        break

                    if file_path and os.path.exists(file_path):
                        series_info["files"].append(file_path)
                    else:
                        logger.warning(
                            f"Could not find file for angle {angle} in MDOC {mdoc_path}"
                        )

            return series_info

        except Exception as e:
            logger.error(f"Error parsing MDOC file {mdoc_path}: {e}")
            return {}

    @staticmethod
    def _find_file_by_angle(
        directory: str, target_angle: float, series_name: str, tolerance: float = 0.5
    ) -> str:
        """
        Find a file in the directory that matches the target angle within tolerance.

        Args:
            directory: Directory to search in
            target_angle: Target tilt angle to match
            series_name: Series name for filename patterns
            tolerance: Angle tolerance for matching (default 0.5 degrees)

        Returns:
            Path to matching file or None if not found
        """
        try:
            # Get all image files in directory
            image_extensions = ["*.eer", "*.tif", "*.tiff", "*.mrc"]
            all_files = []
            for ext in image_extensions:
                all_files.extend(glob.glob(os.path.join(directory, ext)))

            # Try to extract angles from filenames and find closest match
            best_match = None
            best_diff = float("inf")

            for file_path in all_files:
                filename = os.path.basename(file_path)

                # Try different angle extraction patterns
                angle_patterns = [
                    r"\[([-\d.]+)\]",  # [angle] format
                    # _frame_angle.ext format
                    r"_(\d+)_([-\d.]+)\.(?:tif|tiff|mrc)$",
                    # _angle_timestamp.ext format
                    r"_([-\d.]+)_[^_]*\.(?:tif|tiff)$",
                ]

                for pattern in angle_patterns:
                    matches = re.findall(pattern, filename)
                    if matches:
                        try:
                            if len(matches) == 1:
                                file_angle = float(matches[0])
                            else:
                                # For patterns with multiple groups, take the
                                # angle (usually second group)
                                file_angle = float(matches[-1])

                            angle_diff = abs(file_angle - target_angle)
                            if angle_diff <= tolerance and angle_diff < best_diff:
                                best_diff = angle_diff
                                best_match = file_path

                        except (ValueError, IndexError):
                            continue
                        break  # Found angle in this file, no need to try other patterns

            if best_match:
                logger.debug(
                    f"Found file {
    os.path.basename(best_match)} for angle {
        target_angle:.2f}° (diff: {
            best_diff:.2f}°)"
                )
                return best_match

            return None

        except Exception as e:
            logger.error(
                f"Error finding file by angle {target_angle} in {directory}: {e}"
            )
            return None

    @staticmethod
    def parse_tilt_series(directory):
        """
        Parse tilt series using MDOC files as primary source, with filename pattern fallback.
        """
        series_dict = {}

        # Check if directory exists
        if not os.path.exists(directory):
            logger.error(f"Directory does not exist: {directory}")
            return {}

        # Find all MDOC files first (primary method)
        mdoc_files = glob.glob(
    os.path.join(
        directory,
        "**",
        "*.mdoc"),
         recursive=True)

        # Parse MDOC files first
        for mdoc_file in mdoc_files:
            try:
                mdoc_info = TiltSeries.parse_mdoc_file(mdoc_file)
                if mdoc_info and mdoc_info["files"]:
                    # Create series name from MDOC filename
                    series_name = os.path.splitext(
                        os.path.basename(mdoc_file))[0]

                    # Handle special cases like TS_1.mrc.mdoc
                    if series_name.endswith(".mrc"):
                        series_name = series_name[:-4]

                    series = TiltSeries(series_name)
                    series.files = mdoc_info["files"]
                    series.angles = mdoc_info["angles"]
                    series.mdoc_file = mdoc_file
                    series.mdoc_params = mdoc_info["metadata"]

                    # Extract key parameters from MDOC
                    if "PixelSpacing" in mdoc_info["metadata"]:
                        series.series_params["pixel_size"] = float(
                            mdoc_info["metadata"]["PixelSpacing"]
                        )
                    if "Voltage" in mdoc_info["metadata"]:
                        series.series_params["voltage"] = float(
                            mdoc_info["metadata"]["Voltage"]
                        )

                    # Sort by angle
                    series.sort_by_angle()

                    series_dict[series_name] = series
                    logger.info(
                        f"Parsed MDOC series '{series_name}': {len(series.files)} files, "
                        f"angles {min(series.angles):.1f}° to {max(series.angles):.1f}°"
                    )

            except Exception as e:
                logger.error(f"Error processing MDOC file {mdoc_file}: {e}")

        # If no MDOC files found or they didn't contain all files, fall back to filename pattern matching
        if not series_dict:
            logger.info(
                "No MDOC files found or processed, falling back to filename pattern matching"
            )
            series_dict = TiltSeries._parse_by_filename_patterns(directory)

        # Find gain reference files
        gain_files = glob.glob(os.path.join(directory, "**", "*gain*"), recursive=True)
        if gain_files:
            gain_ref = None
            for f in gain_files:
                if any(
                    keyword in f.lower()
                    for keyword in ["gainref", "gain_ref", "gainreference"]
                ):
                    gain_ref = f
                    break
            if not gain_ref:
                gain_ref = gain_files[0]  # Use first gain file found

            if gain_ref:
                logger.info(f"Found gain reference file: {gain_ref}")
                for series in series_dict.values():
                    series.gain_ref = gain_ref

        return series_dict

    @staticmethod
    def _parse_by_filename_patterns(directory):
        """
        Fallback method: Parse tilt series using filename patterns.
        """
        # Regular expressions to match different tilt series file formats

        # EER file patterns
        eer_position_pattern = (
            r"Position_(\d+(?:_\d+)?)_\d+_([-\d.]+)_\d+_\d+_EER\.eer$"
        )
        eer_tomo_pattern = r"(tomo\d+)\[([-\d.]+)\]_EER\.eer$"

        # SerialEM TIFF file patterns
        serialem_simple_pattern = r"([^_]+_\d+)_(\d+)_([-\d.]+)\.tiff?$"
        serialem_timestamp_pattern = (
            r"([^_]+(?:_[^_]+)*?)_(\d+)_([-\d.]+)_[^_]+\.tiff?$"
        )
        serialem_tiff_pattern = r"([^_]+)_(\d+)_([-\d.]+)\.tiff?$"
        serialem_tomo_pattern = r"(tomo\d*)_(\d+)_([-\d.]+)\.tiff?$"
        serialem_position_pattern = r"(Position_\d+)_(\d+)_([-\d.]+)\.tiff?$"

        # MRC file patterns
        mrc_pattern = r"([^_]+)_(\d+)_([-\d.]+)\.mrc$"

        series_dict = {}

        # List all relevant files recursively in subdirectories
        eer_files = glob.glob(os.path.join(directory, "**", "*.eer"), recursive=True)
        tiff_files = glob.glob(os.path.join(directory, "**", "*.tif"), recursive=True)
        tiff_files.extend(
            glob.glob(os.path.join(directory, "**", "*.tiff"), recursive=True)
        )
        mrc_files = glob.glob(os.path.join(directory, "**", "*.mrc"), recursive=True)

        # Combine all image files
        all_image_files = eer_files + tiff_files + mrc_files

        if not all_image_files:
            logger.warning(
                f"No image files (.eer, .tif, .tiff, .mrc) found in directory: {directory} or subdirectories"
            )
            return {}

        logger.info(
            f"Found {len(eer_files)} .eer files, "
            f"{len(tiff_files)} .tiff files, "
            f"{len(mrc_files)} .mrc files recursively"
        )

        # Process each file
        for file in all_image_files:
            try:
                basename = os.path.basename(file)
                matched = False

                # Try EER position pattern first
                match = re.search(eer_position_pattern, basename)
                if match:
                    position = match.group(1)
                    angle = float(match.group(2))
                    series_name = f"Position_{position}"
                    if series_name not in series_dict:
                        series_dict[series_name] = TiltSeries(series_name)
                    series_dict[series_name].add_file(file, angle)
                    matched = True

                # Try EER tomo pattern
                if not matched:
                    match = re.search(eer_tomo_pattern, basename)
                    if match:
                        position = match.group(1)  # tomo69
                        angle = float(match.group(2))
                        if position not in series_dict:
                            series_dict[position] = TiltSeries(position)
                        series_dict[position].add_file(file, angle)
                        matched = True

                # Try SerialEM TIFF patterns
                if not matched:
                    # Try SerialEM simple pattern (TS_88_001_-0.0.tif)
                    match = re.search(serialem_simple_pattern, basename)
                    if match:
                        series_name = match.group(1)  # TS_88
                        frame_num = match.group(2)
                        angle = float(match.group(3))
                        if series_name not in series_dict:
                            series_dict[series_name] = TiltSeries(series_name)
                        series_dict[series_name].add_file(file, angle)
                        matched = True

                # Try SerialEM timestamp pattern (2Dvs3D_53-1_00001_-0.0_Jul31_10.36.03.tif)
                if not matched:
                    match = re.search(serialem_timestamp_pattern, basename)
                    if match:
                        series_name = match.group(1)  # 2Dvs3D_53-1
                        frame_num = match.group(2)
                        angle = float(match.group(3))
                        if series_name not in series_dict:
                            series_dict[series_name] = TiltSeries(series_name)
                        series_dict[series_name].add_file(file, angle)
                        matched = True

                # Try SerialEM position pattern
                if not matched:
                    match = re.search(serialem_position_pattern, basename)
                    if match:
                        position = match.group(1)  # Position_1
                        frame_num = match.group(2)
                        angle = float(match.group(3))
                        if position not in series_dict:
                            series_dict[position] = TiltSeries(position)
                        series_dict[position].add_file(file, angle)
                        matched = True

                # Try SerialEM tomo pattern
                if not matched:
                    match = re.search(serialem_tomo_pattern, basename)
                    if match:
                        position = match.group(1) or "tomo"  # tomo or tomo1
                        frame_num = match.group(2)
                        angle = float(match.group(3))
                        if position not in series_dict:
                            series_dict[position] = TiltSeries(position)
                        series_dict[position].add_file(file, angle)
                        matched = True

                # Try general SerialEM TIFF pattern
                if not matched:
                    match = re.search(serialem_tiff_pattern, basename)
                    if match:
                        series_name = match.group(1)  # series name
                        frame_num = match.group(2)
                        angle = float(match.group(3))
                        if series_name not in series_dict:
                            series_dict[series_name] = TiltSeries(series_name)
                        series_dict[series_name].add_file(file, angle)
                        matched = True

                # Try MRC pattern
                if not matched:
                    match = re.search(mrc_pattern, basename)
                    if match:
                        series_name = match.group(1)  # series name
                        frame_num = match.group(2)
                        angle = float(match.group(3))
                        if series_name not in series_dict:
                            series_dict[series_name] = TiltSeries(series_name)
                        series_dict[series_name].add_file(file, angle)
                        matched = True

                if not matched:
                    logger.warning(f"File doesn't match any known pattern: {basename}")

            except Exception as e:
                logger.error(f"Error processing file {file}: {str(e)}")
                continue

        # Find MDOC files for pattern-matched series
        mdoc_files = glob.glob(os.path.join(directory, "**", "*.mdoc"), recursive=True)

        # Try to find and load corresponding .mdoc files
        for series in series_dict.values():
            # Try to find mdoc file for this series
            mdoc_matches = [
                f for f in mdoc_files if series.position_name in os.path.basename(f)
            ]
            if mdoc_matches:
                try:
                    series.mdoc_file = mdoc_matches[0]
                    logger.info(
                        f"Found MDOC file for {series.position_name}: {mdoc_matches[0]}"
                    )
                except Exception as e:
                    logger.error(
                        f"Error loading MDOC for {series.position_name}: {str(e)}"
                    )

        # Sort each series by angle
        for series in series_dict.values():
            if series.files:  # Only sort if there are files
                series.sort_by_angle()

        return series_dict

    def validate(self):
        """Validate that the tilt series has required data."""
        if not self.files:
            raise ValueError("Tilt series has no files")
        if not self.angles:
            raise ValueError("Tilt series has no angles")
        if len(self.files) != len(self.angles):
            raise ValueError("Number of files does not match number of angles")
        return True

class AreTomoWorker(QThread):
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, command, series_name=None):
        super().__init__()
        self.command = command
        self.current_file = series_name or "Processing..."
        self.start_time = time.time()

    def run(self):
        try:
            # Get path to wrapper script
            wrapper_script = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "utils",
                "aretomo3_wrapper.sh",
            )

            # Construct wrapped command
            wrapped_command = f"{wrapper_script} {self.command}"
            logger.info(f"Starting AreTomo3 process with command: {wrapped_command}")

            process = subprocess.Popen(
                wrapped_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Merge stderr into stdout
                stdin=subprocess.DEVNULL,  # Close stdin to avoid blocking
                text=True,
                bufsize=1,
                universal_newlines=True,
                shell=True,
            )

            # Robustly drain all output
            for output in process.stdout:
                output = output.strip()
                if output:
                    logger.info(f"AreTomo3: {output}")
                    self.progress.emit(output)

            rc = process.wait()  # Ensure process has fully exited

            if rc == 0:
                logger.info("AreTomo3 process completed successfully")
                self.finished.emit(True, "Processing completed successfully!")
            else:
                logger.error(f"AreTomo3 process failed with return code {rc}")
                self.finished.emit(False, f"Error during processing: Return code {rc}")
        except Exception as e:
            logger.error(f"Error running AreTomo3 process: {str(e)}", exc_info=True)
            self.finished.emit(False, f"Error: {str(e)}")

class AreTomoGUI(QMainWindow):
    def __init__(self):
        """Initialize the AreTomo3 GUI main window."""
        try:
            logger.info("Creating AreTomo3 GUI window")
            super().__init__()

            # Initialize theme manager
            self.theme_manager = ThemeManager()
            QApplication.instance().setStyleSheet(
                self.theme_manager.get_theme_stylesheet()
            )

            # Set window title and flags
            self.setWindowTitle("AreTomo3 GUI")
            self.setWindowFlags(
                self.windowFlags()
                | Qt.WindowType.Window
                | Qt.WindowType.WindowMinimizeButtonHint
                | Qt.WindowType.WindowMaximizeButtonHint
            )
            self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)

            # Set window size to 90% of screen size
            screen = QApplication.primaryScreen().availableGeometry()
            width = int(screen.width() * 0.9)  # Use 90% of screen width
            height = int(screen.height() * 0.9)  # Use 90% of screen height
            self.resize(width, height)
            self.setMinimumSize(800, 600)  # Set minimum size to ensure usability
            # Center the window on screen
            self.move(screen.center() - self.frameGeometry().center())

            # Initialize logging widget
            logger.info("Setting up logging widget")
            self.log_text = QTextEdit()
            self.log_text.setReadOnly(True)
            font = self.log_text.font()
            font.setPointSize(10)
            self.log_text.setFont(font)
            self.log_text.setLineWrapMode(QTextEdit.LineWrapMode.WidgetWidth)

            # Initialize recent files list widget to avoid AttributeError
            self.recent_list = QListWidget()

            # Set up status bar
            self.status_bar = QStatusBar(self)
            self.setStatusBar(self.status_bar)
            self.status_bar.showMessage("Initializing...")

            # Initialize processing state first
            logger.info("Initializing processing state")
            self.current_worker: Optional[AreTomoWorker] = None
            self.tilt_series: Dict[str, TiltSeries] = {}
            self.file_watcher: Optional[FileWatcher] = None
            self.processing_queue: List[Dict[str, Any]] = []
            self.is_processing: bool = False

            # Initialize batch processing state
            self.batch_workers: Dict[str, AreTomoWorker] = {}
            self.batch_queue: List[Dict[str, Any]] = []
            self.current_batch_index: int = 0

            # Create initial AreTomo3Config with defaults
            logger.info("Creating default configuration")
            self.config = AreTomo3Config(pixel_size=1.91)  # Only required parameter

            # Initialize progress_bar attribute
            self.progress_bar = None

            # Initialize parameter manager
            self.parameter_manager = ParameterManager(self)

            # Initialize real-time processor
            watch_dirs = [Path.home() / "AreTomo3_Watch"]  # Default watch directory
            output_dir = Path.home() / "AreTomo3_Output"  # Default output directory
            self.realtime_processor = RealTimeProcessor(watch_dirs, output_dir)

            # Initialize session manager and continue mode manager
            from ..core.continue_mode_manager import ContinueModeManager
            from ..core.session_manager import SessionManager

            self.session_manager = SessionManager()
            self.continue_manager = ContinueModeManager()

            logger.info("Session manager and continue mode manager initialized")

            # Initialize UI components (after all dependencies are ready)
            logger.info("Initializing UI components")
            self.init_ui()

            # Connect parameter changes for command preview
            self._connect_parameter_changes()

            # Connect tab change detection for auto-loading analysis files
            self._connect_tab_change_signal()

            # Auto-restore previous session
            self._restore_last_session()

            # Update status
            self.status_bar.showMessage("Ready")
            logger.info("AreTomo3 GUI window initialized successfully")

        except Exception as e:
            logger.critical(
                f"Failed to initialize AreTomo3 GUI window: {e}", exc_info=True
            )
            raise

    def closeEvent(self, event):
        """Handle application close event - automatically save current session."""
        try:
            logger.info("Application closing - saving current session")
            self._save_current_session()

            # Stop any running processes
            if (
                hasattr(self, "current_worker")
                and self.current_worker
                and self.current_worker.isRunning()
            ):
                self.current_worker.terminate()

            # Stop batch processing
            if hasattr(self, "batch_workers"):
                for worker in self.batch_workers.values():
                    if worker.isRunning():
                        worker.terminate()

            # Clean up system monitors
            if hasattr(self, "sys_monitor"):
                self.sys_monitor.closeEvent(event)

            logger.info("Application closed successfully")
            event.accept()

        except Exception as e:
            logger.error(f"Error during application close: {e}")
            event.accept()  # Still close even if save fails

    def _get_auto_session_path(self) -> Path:
        """Get the path for automatic session storage."""
        app_data_dir = Path.home() / ".aretomo3_gui"
        app_data_dir.mkdir(exist_ok=True)
        return app_data_dir / "last_session.json"

    def _save_current_session(self) -> None:
        """Save current GUI state to auto-session file."""
        try:
            session_data = {
                "metadata": {
                    "name": "Auto-saved Session",
                    "created": datetime.now().isoformat(),
                    "version": "1.0.0",
                },
                "gui_state": self.collect_current_gui_state(),
                "processing_parameters": self._collect_current_processing_parameters(),
                "tilt_series_data": self._collect_tilt_series_data(),
            }

            session_path = self._get_auto_session_path()
            with open(session_path, "w") as f:
                import json

                json.dump(session_data, f, indent=2)

            logger.info(f"Auto-saved session to: {session_path}")

            # Update recent sessions if project widget exists
            if hasattr(self, "project_widget") and self.project_widget:
                self.project_widget.add_to_recent_sessions(str(session_path))

        except Exception as e:
            logger.error(f"Error auto-saving session: {e}")

    def _restore_last_session(self) -> None:
        """Restore the last auto-saved session if it exists."""
        try:
            session_path = self._get_auto_session_path()
            if not session_path.exists():
                logger.info("No previous session found")
                return

            with open(session_path, "r") as f:
                import json

                session_data = json.load(f)

            # Restore GUI state
            self.restore_gui_state_from_session(session_data)

            # Restore tilt series if paths are still valid
            self._restore_tilt_series_from_session(session_data)

            logger.info("Previous session restored successfully")
            self.status_bar.showMessage("Previous session restored", 3000)

        except Exception as e:
            logger.error(f"Error restoring previous session: {e}")

    def collect_current_gui_state(self) -> dict:
        """Collect current GUI state for session saving."""
        try:
            gui_state = {
                "window_geometry": {
                    "width": self.size().width(),
                    "height": self.size().height(),
                    "x": self.pos().x(),
                    "y": self.pos().y(),
                },
                "active_tab": getattr(self, "tab_widget", None)
                and self.tab_widget.currentIndex()
                or 0,
                "paths": {
                    "input_dir": getattr(self, "input_dir", None)
                    and self.input_dir.text()
                    or "",
                    "output_dir": getattr(self, "output_dir", None)
                    and self.output_dir.text()
                    or "",
                    "aretomo_path": getattr(self, "aretomo_path", None)
                    and self.aretomo_path.text()
                    or "",
                },
            }
            return gui_state
        except Exception as e:
            logger.error(f"Error collecting GUI state: {e}")
            return {}

    def restore_gui_state_from_session(self, session_data: dict) -> None:
        """Restore GUI state from session data."""
        try:
            if "gui_state" not in session_data:
                return

            gui_state = session_data["gui_state"]

            # Restore window geometry
            if "window_geometry" in gui_state:
                geom = gui_state["window_geometry"]
                self.resize(geom.get("width", 1200), geom.get("height", 800))
                self.move(geom.get("x", 100), geom.get("y", 100))

            # Restore active tab
            if "active_tab" in gui_state and hasattr(self, "tab_widget"):
                self.tab_widget.setCurrentIndex(gui_state["active_tab"])

            # Restore paths
            if "paths" in gui_state:
                paths = gui_state["paths"]
                if hasattr(self, "input_dir") and paths.get("input_dir"):
                    self.input_dir.setText(paths["input_dir"])
                if hasattr(self, "output_dir") and paths.get("output_dir"):
                    self.output_dir.setText(paths["output_dir"])
                if hasattr(self, "aretomo_path") and paths.get("aretomo_path"):
                    self.aretomo_path.setText(paths["aretomo_path"])

        except Exception as e:
            logger.error(f"Error restoring GUI state: {e}")

    def _collect_current_processing_parameters(self) -> dict:
        """Collect current processing parameters for session saving."""
        try:
            params = {
                "microscope_settings": {
                    "pixel_size": (
                        self.pixel_size.value() if hasattr(self, "pixel_size") else 1.91
                    ),
                    "voltage": (
                        self.voltage.value() if hasattr(self, "voltage") else 300
                    ),
                    "cs": self.cs.value() if hasattr(self, "cs") else 2.7,
                    "amp_contrast": (
                        self.amp_contrast.value()
                        if hasattr(self, "amp_contrast")
                        else 0.1
                    ),
                    "tilt_axis": (
                        self.tilt_axis.value() if hasattr(self, "tilt_axis") else -95.75
                    ),
                    "frame_dose": (
                        self.frame_dose.value() if hasattr(self, "frame_dose") else 0.14
                    ),
                },
                "motion_correction": {
                    "mc_bin": self.mc_bin.value() if hasattr(self, "mc_bin") else 1,
                    "mc_patch_x": (
                        self.mc_patch_x.value() if hasattr(self, "mc_patch_x") else 1
                    ),
                    "mc_patch_y": (
                        self.mc_patch_y.value() if hasattr(self, "mc_patch_y") else 1
                    ),
                    "fm_int": self.fm_int.value() if hasattr(self, "fm_int") else 12,
                },
                "reconstruction": {
                    "at_bin": self.at_bin.value() if hasattr(self, "at_bin") else 4,
                    "volume_z": (
                        self.volume_z.value() if hasattr(self, "volume_z") else 2048
                    ),
                    "dark_tol": (
                        self.dark_tol.value() if hasattr(self, "dark_tol") else 0.7
                    ),
                    "lowpass": self.lowpass.value() if hasattr(self, "lowpass") else 15,
                },
                "options": {
                    "flip_gain": (
                        self.flip_gain.isChecked()
                        if hasattr(self, "flip_gain")
                        else True
                    ),
                    "flip_volume": (
                        self.flip_volume.isChecked()
                        if hasattr(self, "flip_volume")
                        else True
                    ),
                    "correct_ctf": (
                        self.correct_ctf.isChecked()
                        if hasattr(self, "correct_ctf")
                        else True
                    ),
                    "out_xf": (
                        self.out_xf.isChecked() if hasattr(self, "out_xf") else True
                    ),
                    "out_imod": (
                        self.out_imod.isChecked() if hasattr(self, "out_imod") else True
                    ),
                    "wbp": self.wbp.isChecked() if hasattr(self, "wbp") else True,
                    "tilt_cor": (
                        self.tilt_cor.isChecked() if hasattr(self, "tilt_cor") else True
                    ),
                },
            }

            # Add advanced settings if available
            if hasattr(self, "advanced_tab") and self.advanced_tab:
                try:
                    params["advanced_options"] = self.advanced_tab.get_settings()
                except Exception:
                    pass

            return params

        except Exception as e:
            logger.error(f"Error collecting processing parameters: {e}")
            return {}

    def _collect_tilt_series_data(self) -> dict:
        """Collect tilt series data for session saving."""
        try:
            if not hasattr(self, "tilt_series") or not self.tilt_series:
                return {}

            series_data = {}
            for name, series in self.tilt_series.items():
                series_data[name] = {
                    "position_name": series.position_name,
                    "files": series.files,
                    "angles": series.angles,
                    "mdoc_file": series.mdoc_file,
                    "gain_ref": series.gain_ref,
                    "series_params": series.series_params,
                }

            return series_data

        except Exception as e:
            logger.error(f"Error collecting tilt series data: {e}")
            return {}

    def _restore_tilt_series_from_session(self, session_data: dict) -> None:
        """Restore tilt series data from session."""
        try:
            series_data = session_data.get("tilt_series_data", {})
            if not series_data:
                return

            self.tilt_series = {}

            for name, data in series_data.items():
                # Verify files still exist
                valid_files = []
                valid_angles = []

                for file_path, angle in zip(
                    data.get("files", []), data.get("angles", [])
                ):
                    if os.path.exists(file_path):
                        valid_files.append(file_path)
                        valid_angles.append(angle)

                if valid_files:
                    series = TiltSeries(data["position_name"])
                    series.files = valid_files
                    series.angles = valid_angles
                    series.mdoc_file = (
                        data.get("mdoc_file")
                        if data.get("mdoc_file")
                        and os.path.exists(data.get("mdoc_file", ""))
                        else None
                    )
                    series.gain_ref = (
                        data.get("gain_ref")
                        if data.get("gain_ref")
                        and os.path.exists(data.get("gain_ref", ""))
                        else None
                    )
                    series.series_params = data.get("series_params", {})

                    self.tilt_series[name] = series

            if self.tilt_series:
                logger.info(
                    f"Restored {len(self.tilt_series)} tilt series from session"
                )

                # Update UI with first series
                first_series = next(iter(self.tilt_series.values()))
                self.update_ui_from_series(first_series)

        except Exception as e:
            logger.error(f"Error restoring tilt series from session: {e}")

    def log_message(self, message: str, level: str = "INFO") -> None:
        """Log messages to both the GUI log widget and the application logger."""
        # Add to GUI log widget
        if hasattr(self, "log_text"):
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {level}: {message}"
            self.log_text.append(formatted_message)

        # Also log to application logger
        if level.upper() == "DEBUG":
            logger.debug(message)
        elif level.upper() == "INFO":
            logger.info(message)
        elif level.upper() == "WARNING":
            logger.warning(message)
        elif level.upper() == "ERROR":
            logger.error(message)
        elif level.upper() == "CRITICAL":
            logger.critical(message)
        else:
            logger.info(f"{level}: {message}")

    def init_ui(self) -> None:
        """Initialize the user interface."""
        try:
            logger.info("Setting up main menu")
            # Set window title
            self.setWindowTitle("AreTomo3 GUI")

            # Set up main menu
            menubar = self.menuBar()
            file_menu = menubar.addMenu("File")
            help_menu = menubar.addMenu("Help")
            exit_action = QAction("Exit", self)
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)
            about_action = QAction("About", self)
            about_action.triggered.connect(self._show_about)
            help_menu.addAction(about_action)

            # Create File menu
            file_menu = self.menuBar().addMenu("&File")

            open_action = QAction("&Open...", self)
            open_action.setShortcut("Ctrl+O")
            open_action.triggered.connect(self.open_file)
            file_menu.addAction(open_action)

            save_action = QAction("&Save", self)
            save_action.setShortcut("Ctrl+S")
            save_action.triggered.connect(self.save_file)
            file_menu.addAction(save_action)

            file_menu.addSeparator()

            exit_action = QAction("E&xit", self)
            exit_action.setShortcut("Ctrl+Q")
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)

            file_menu.addSeparator()

            exit_action = QAction("E&xit", self)
            exit_action.setShortcut("Ctrl+Q")
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)

            logger.info("Setting up central widget")
            # Set up central widget and main layout
            self.central_widget = QWidget(self)
            self.setCentralWidget(self.central_widget)
            main_layout = QVBoxLayout(self.central_widget)

            # Create tab widget
            logger.info("Creating tab widget")
            self.tab_widget = QTabWidget(self.central_widget)
            self.tab_widget.setDocumentMode(True)
            self.tab_widget.setMovable(True)

            # Create reorganized tab pages
            logger.info("Creating reorganized tab pages")

            # Main Control Center tab (replaces old main + project tabs)
            self.main_tab = ReorganizedMainTab(self)

            # Enhanced Parameters tab (replaces old parameters + advanced tabs)
            self.enhanced_params_tab = EnhancedParametersTab(self)

            # Unified Live Processing tab (consolidates live processing + real-time analysis)
            self.unified_live_tab = UnifiedLiveProcessingTab(self)

            # Enhanced Analysis tab (dedicated analysis tab preserved as requested)
            self.enhanced_analysis_tab = EnhancedAnalysisTab(self)

            # Create remaining tabs that need setup
            self.batch_tab = QWidget(self.tab_widget)
            self.viewer_tab = QWidget(self.tab_widget)  # Enhanced viewer with analysis
            self.log_tab = QWidget(self.tab_widget)

            # Set up tab integration and data sharing
            self._setup_tab_integration()

            # Set up remaining tab content
            logger.info("Setting up batch processing tab")
            batch_layout = QVBoxLayout(self.batch_tab)
            self.setup_batch_tab(self.batch_tab, batch_layout)

            logger.info("Setting up enhanced viewer tab")
            viewer_layout = QVBoxLayout(self.viewer_tab)
            self.setup_viewer_tab(self.viewer_tab, viewer_layout)

            logger.info("Setting up log tab")
            log_layout = QVBoxLayout(self.log_tab)
            self.setup_log_tab(self.log_tab, log_layout)

            # Web Interface tab (new web server control)
            self.web_tab = QWidget(self.tab_widget)
            web_layout = QVBoxLayout(self.web_tab)
            self.web_server_widget = WebServerControlWidget(
                self.realtime_processor,
                session_manager=self.session_manager,
                continue_manager=self.continue_manager,
            )
            web_layout.addWidget(self.web_server_widget)

            # Auto-start web server by default
            self.auto_start_web_server()

            # Add tabs with professional, logical naming
            # Organized by workflow: Setup → Configure → Process → Analyze → Monitor
            self.tab_widget.addTab(self.main_tab, "Project Setup")
            self.tab_widget.addTab(
                self.enhanced_params_tab, "Reconstruction Parameters"
            )
            self.tab_widget.addTab(self.unified_live_tab, "Live Processing")
            self.tab_widget.addTab(self.batch_tab, "Batch Processing")
            self.tab_widget.addTab(self.enhanced_analysis_tab, "Data Analysis")
            self.tab_widget.addTab(self.viewer_tab, "3D Viewer")
            self.tab_widget.addTab(self.web_tab, "Remote Dashboard")
            self.tab_widget.addTab(self.log_tab, "System Logs")

            # Add tabs widget to main layout
            main_layout.addWidget(self.tab_widget)

            # Set reference for backward compatibility
            self.tabs = self.tab_widget

            logger.info("UI initialization completed successfully")
        except Exception as e:
            logger.critical(f"Failed to initialize UI: {e}", exc_info=True)
            raise

    def setup_analysis_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the analysis tab for data visualization and analysis."""
        vsplitter = QSplitter(Qt.Orientation.Vertical)

        # Upper section - Visualization
        viz_widget = QWidget()
        viz_layout = QVBoxLayout()

        # View controls
        controls = QHBoxLayout()

        view_controls = QWidget()
        view_layout = QHBoxLayout()
        self.view_type = QComboBox()
        self.view_type.addItems(["Tomogram", "CTF", "Motion", "Statistics"])
        view_layout.addWidget(QLabel("View:"))
        view_layout.addWidget(self.view_type)
        view_controls.setLayout(view_layout)
        controls.addWidget(view_controls)

        # Analysis controls
        analysis_controls = QWidget()
        analysis_layout = QHBoxLayout()

        self.auto_update = QCheckBox("Auto Update")
        self.auto_update.setChecked(True)
        analysis_layout.addWidget(self.auto_update)

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self._refresh_analysis)
        analysis_layout.addWidget(refresh_btn)

        analysis_controls.setLayout(analysis_layout)
        controls.addWidget(analysis_controls)

        viz_layout.addLayout(controls)

        # Visualization area
        self.analysis_viewer = AnalysisViewer()
        viz_layout.addWidget(self.analysis_viewer)

        viz_widget.setLayout(viz_layout)
        vsplitter.addWidget(viz_widget)

        # Lower section - Metrics dashboard
        # self.metrics_dashboard = MetricsDashboard()  # Temporarily commented out due to scipy issue
        # vsplitter.addWidget(self.metrics_dashboard)  # Temporarily commented out due to scipy issue

        vsplitter.setStretchFactor(0, 2)
        vsplitter.setStretchFactor(1, 1)

        layout.addWidget(vsplitter)

    def setup_viewer_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the viewer tab for professional Napari-based MRC viewing."""
        self.napari_viewer = NapariMRCViewer(tab_widget)
        layout.addWidget(self.napari_viewer)

    def _refresh_analysis(self):
        """Refresh the analysis display with current data."""
        try:
            # Check if we have an input directory set
            if hasattr(self, "input_dir") and self.input_dir.text():
                input_path = Path(self.input_dir.text())
                if input_path.exists():
                    # Try to load analysis data from the input directory
                    self.analysis_viewer.load_directory_analysis(input_path)
                    logger.info(f"Refreshed analysis data from {input_path}")
                else:
                    logger.warning(f"Input directory does not exist: {input_path}")

            # Also check if we have an output directory with results
            if hasattr(self, "output_dir") and self.output_dir.text():
                output_path = Path(self.output_dir.text())
                if output_path.exists():
                    # Look for AreTomo3 output files in the output directory
                    self.analysis_viewer.load_directory_analysis(output_path)
                    logger.info(
                        f"Refreshed analysis data from output directory: {output_path}"
                    )

            # If no directories are set, show a message
            if not (hasattr(self, "input_dir") and self.input_dir.text()) and not (
                hasattr(self, "output_dir") and self.output_dir.text()
            ):
                QMessageBox.information(
                    self,
                    "Refresh Analysis",
                    "Please set input or output directories to refresh analysis data.",
                )

        except Exception as e:
            logger.error(f"Error refreshing analysis: {e}")
            QMessageBox.warning(
                self, "Refresh Error", f"Failed to refresh analysis data: {str(e)}"
            )

    def setup_batch_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the batch processing tab."""
        self.batch_widget = BatchProcessingWidget(
            self
        )  # Pass self (AreTomoGUI) as parent instead of tab_widget
        # Connect batch processing signal
        self.batch_widget.start_batch.connect(self.on_start_batch_processing)
        layout.addWidget(self.batch_widget)

    def setup_project_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the project management tab."""
        from aretomo3_gui.gui.widgets.project_management import ProjectManagementWidget

        self.project_widget = ProjectManagementWidget(self)
        layout.addWidget(self.project_widget)

    def setup_file_browser_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the advanced file browser tab."""
        self.file_browser_widget = AdvancedFileBrowser(self)
        layout.addWidget(self.file_browser_widget)

    def setup_export_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the export tab for various file format exports."""
        format_group = QGroupBox("Export Format", tab_widget)
        format_layout = QVBoxLayout()
        self.format_combo = QComboBox()
        self.format_combo.addItems(
            ["IMOD MRC", "TIFF Stack", "Relion", "EMAN2", "ImageJ"]
        )
        format_layout.addWidget(self.format_combo)
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)
        options_group = QGroupBox("Export Options", tab_widget)
        options_layout = QGridLayout()
        self.compression_check = QCheckBox("Use Compression")
        options_layout.addWidget(self.compression_check, 0, 0)
        self.calibration_check = QCheckBox("Include Calibration")
        options_layout.addWidget(self.calibration_check, 1, 0)
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        export_btn = QPushButton("Export")
        export_btn.clicked.connect(self.on_export)
        layout.addWidget(export_btn)
        layout.addStretch()

    def setup_log_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the log tab for displaying application logs and recent files."""
        layout.addWidget(self.log_text)
        # Add recent files list below the log
        recent_group = QGroupBox("Recent Files", tab_widget)
        recent_layout = QVBoxLayout()
        recent_layout.addWidget(self.recent_list)
        recent_group.setLayout(recent_layout)
        layout.addWidget(recent_group)
        controls_layout = QHBoxLayout()
        clear_log_btn = QPushButton("Clear Log", tab_widget)
        clear_log_btn.clicked.connect(self.log_text.clear)
        save_log_btn = QPushButton("Save Log")
        save_log_btn.clicked.connect(self.on_save_log)
        controls_layout.addWidget(clear_log_btn)
        controls_layout.addWidget(save_log_btn)
        layout.addLayout(controls_layout)

    def open_file(self):
        """Handle file open action."""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open File", "", "All Files (*)"
        )
        if file_path:
            self.log_message(f"Opening file: {file_path}")

    def save_file(self):
        """Handle file save action."""
        self.log_message("Save functionality not yet implemented")

    # TODO: Add input validation

    def on_browse_input(self) -> None:
        """Handle input directory browse button click."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Input Directory",
            str(Path.home()),
            QFileDialog.Option.ShowDirsOnly,
        )
        if directory:
            self.input_dir.setText(directory)
            self._update_output_dir()  # Update output directory accordingly
            if hasattr(self, "file_browser_widget"):
                self.file_browser_widget.change_directory(directory)
            # Sync file browser to the loaded directory
            # self._sync_file_browser_directory(str(input_path))

    def _update_output_dir(self) -> None:
        """Update output directory based on input directory."""
        if self.input_dir.text():
            input_path = Path(self.input_dir.text())
            default_output = input_path / f"{input_path.name}_output"
            self.output_dir.setText(str(default_output))

    def on_browse_output(self) -> None:
        """Handle output directory browse button click."""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Output Directory", str(Path.home()), QFileDialog.ShowDirsOnly
        )
        if directory:
            self.output_dir.setText(directory)

    def on_browse_aretomo(self) -> None:
        """Handle AreTomo3 path browse button click."""
        # Set default path and filter based on OS
        if platform.system() == "Windows":
            default_path = "/usr/local/bin"
            file_filter = "All Files (*)"

        # Try environment variable first
        # Set a default path (user's home directory)
        default_path = os.path.expanduser("~")

        # Try environment variable first
        env_path = os.environ.get("ARETOMO3_PATH", "")
        if env_path and os.path.exists(env_path):
            if os.path.isdir(env_path):
                default_path = env_path
            else:
                default_path = os.path.dirname(env_path)

        # First try directory selection
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select AreTomo3 Directory",
            default_path,
            QFileDialog.Option.ShowDirsOnly,
        )

        if directory:
            # First, try validating the directory itself (it might be the installation dir)
            success, message = validate_aretomo_installation(directory)
            if success:
                selected_path = os.path.join(directory, "AreTomo3")
            else:
                # If not a valid installation directory, let user select the executable
                executable, _ = QFileDialog.getOpenFileName(
                    self, "Select AreTomo3 Executable", directory, file_filter
                )
                if not executable:
                    return
                success, message = validate_aretomo_installation(executable)
                if not success:
                    QMessageBox.warning(
                        self,
                        "Invalid AreTomo3 Executable",
                        f"Selected file is not a valid AreTomo3 executable:\n{message}\n\n"
                        "Please select the correct AreTomo3 executable file.",
                    )
                    logger.warning(
                        f"Invalid AreTomo3 executable selected: {executable} - {message}"
                    )
                    return
                selected_path = executable

            try:
                if not os.access(selected_path, os.X_OK):
                    os.chmod(selected_path, os.stat(selected_path).st_mode | 0o111)
            except Exception as e:
                QMessageBox.warning(
                    self,
                    "Permission Error",
                    f"Cannot make file executable: {str(e)}\nPlease ensure you have proper permissions.",
                )
                return

            self.aretomo_path.setText(selected_path)
            # Update environment variable
            os.environ["ARETOMO3_PATH"] = selected_path
            logger.info(f"AreTomo3 path updated to: {selected_path}")
            self.status_bar.showMessage("AreTomo3 executable set successfully", 3000)

    def _show_about(self) -> None:
        """Show the about dialog."""
        version = "1.0.0"
        about_text = f"""
        <h2>AreTomo3 GUI</h2>
        <p>Version {version}</p>
        <p>A graphical interface for AreTomo3 tilt series alignment and reconstruction.</p>
        <p>Running with:</p>
        <ul>
        <li>Python {platform.python_version()}</li>
        <li>Qt {QT_VERSION_STR}</li>
        <li>OS: {platform.system()} {platform.release()}</li>
        </ul>
        <p>For help and documentation, please visit the project repository.</p>
        """
        QMessageBox.about(self, "About AreTomo3 GUI", about_text)
        """Show the about dialog."""
        version = "1.0.0"
        about_text = f"""
        <h2>AreTomo3 GUI</h2>
        <p>Version {version}</p>
        <p>A graphical interface for AreTomo3 tilt series alignment and reconstruction.</p>
        <p>Running with:</p>
        <ul>
        <li>Python {platform.python_version()}</li>
        <li>Qt {QT_VERSION_STR}</li>
        <li>OS: {platform.system()} {platform.release()}</li>
        </ul>
        <p>For help and documentation, please visit the project repository.</p>
        """

        QMessageBox.about(self, "About AreTomo3 GUI", about_text)

    def find_tilt_series(self, directory: str) -> Dict[str, TiltSeries]:
        """
        Find all tilt series in the specified directory.
        Uses TiltSeries.parse_tilt_series under the hood.

        Args:
            directory (str): Directory path to scan for tilt series

        Returns:
            Dict[str, TiltSeries]: Dictionary mapping position names to TiltSeries objects
        """
        logger.info(f"Scanning for tilt series in {directory}")
        try:
            series_dict = TiltSeries.parse_tilt_series(directory)
            if series_dict:
                logger.info(f"Found {len(series_dict)} tilt series")
            else:
                logger.info("No tilt series found")
            return series_dict
        except Exception as e:
            logger.error(f"Error finding tilt series: {str(e)}", exc_info=True)
            return {}

    def update_ui_from_series(self, series: TiltSeries) -> None:
        """Update UI controls with values from the loaded tilt series."""
        logger.info(f"Updating UI controls from series {series.position_name}")

        try:
            # Update Parameters tab with values from .mdoc file
            p = series.series_params

            # Convert series parameters to Parameters tab format
            params_for_tab = {
                "pix_size": p.get("pixel_size", 1.91),
                "tilt_axis": p.get("tilt_axis", -95.75),
                "kv": p.get("voltage", 300),
                "cs": p.get("cs", 2.7),
                "amp_contrast": p.get("amp_contrast", 0.1),
                "fm_dose": p.get("frame_dose", 0.14),
            }

            # Update Enhanced Parameters tab if it exists
            if hasattr(self, "enhanced_params_tab") and self.enhanced_params_tab:
                # Use the specialized mdoc update method
                self.enhanced_params_tab.update_from_mdoc_data(p)
                logger.info(
                    f"Updated Parameters tab with .mdoc values: {params_for_tab}"
                )
            else:
                logger.warning(
                    "Enhanced Parameters tab not found - cannot update with .mdoc parameters"
                )

            logger.info(f"Updated UI controls with parameters from .mdoc: {p}")

        except Exception as e:
            logger.error(f"Error updating UI from series: {e}")
            # Don't let this error stop the session restore process

    def on_load_tilt_series(self) -> None:
        """Handle loading a tilt series from a file."""
        if not self.input_dir.text():
            QMessageBox.warning(
                self, "No Input Directory", "Please select an input directory first."
            )
            return

        input_path = Path(self.input_dir.text())
        if not input_path.exists():
            QMessageBox.warning(
                self, "Invalid Directory", f"Directory {input_path} does not exist."
            )
            return

        # Look for supported file types
        supported_types = ["*.mrc", "*.mrcs", "*.st", "*.tif", "*.tiff", "*.eer"]
        tilt_files = []
        for ext in supported_types:
            tilt_files.extend(input_path.glob(ext))

        if not tilt_files:
            QMessageBox.warning(
                self,
                "No Files Found",
                f"No supported tilt series files found in {input_path}.\n"
                "Supported formats: MRC, TIFF, EER",
            )
            return

        # Parse the tilt series and organize files
        self.tilt_series = TiltSeries.parse_tilt_series(str(input_path))

        # If we found any series, update the UI with values from the first one
        if self.tilt_series:
            first_series = next(iter(self.tilt_series.values()))
            self.update_ui_from_series(first_series)

        # Sync file browser to the loaded directory
        if hasattr(self, "file_browser_widget"):
            self.file_browser_widget.change_directory(str(input_path))

        # Count files with and without mdoc
        files_with_mdoc = sum(
            1 for series in self.tilt_series.values() if series.mdoc_params is not None
        )
        total_series = len(self.tilt_series)

        self.status_bar.showMessage(
            f"Found {len(tilt_files)} files in {total_series} tilt series. "
            f"{files_with_mdoc}/{total_series} series have .mdoc files."
        )

    def get_selected_series(self) -> List[TiltSeries]:
        """Get list of selected tilt series from the UI."""
        if not self.tilt_series:
            return []

        # For now return all series, later we can add selection UI
        return list(self.tilt_series.values())

    def construct_aretomo_command(self, series: TiltSeries) -> str:
        """
        Construct AreTomo3 command for a tilt series with validation.

        Args:
            series: The tilt series to generate command for

        Returns:
            The complete AreTomo3 command string

        Raises:
            ValueError: If AreTomo3 path is not set or invalid
        """
        # Get AreTomo3 directory from Control Center
        aretomo_dir = ""
        if hasattr(self, "main_tab") and hasattr(self.main_tab, "aretomo_path"):
            aretomo_dir = self.main_tab.aretomo_path.text().strip()

        if not aretomo_dir:
            raise ValueError("AreTomo3 directory not set in Control Center")

        success, message = validate_aretomo_installation(aretomo_dir)
        if not success:
            raise ValueError(f"Invalid AreTomo3 installation: {message}")

        # Get parameters from Enhanced Parameters tab
        if hasattr(self, "enhanced_params_tab") and self.enhanced_params_tab:
            gui_params = self.enhanced_params_tab.get_parameters()
        else:
            # Fallback to series params if Parameters tab not available
            gui_params = {
                "pix_size": series.series_params.get("pixel_size", 1.82),
                "kv": series.series_params.get("voltage", 300),
                "cs": series.series_params.get("cs", 2.7),
                "amp_contrast": series.series_params.get("amp_contrast", 0.1),
                "tilt_axis": series.series_params.get("tilt_axis", 175.9),
                "fm_dose": series.series_params.get("frame_dose", 2.56),
                "vol_z": 2048,
                "at_bin": 4.0,
                "dark_tol": 0.7,
            }

        # Build command using helper method
        # Check if we're processing a batch item by looking for the series in our batch queue
        batch_input_dir = None
        if hasattr(self, "batch_queue") and self.batch_queue:
            for batch_item in self.batch_queue:
                if batch_item.get("series") == series:
                    batch_input_dir = batch_item.get("input_dir")
                    break

        return self._build_command(aretomo_dir, series, batch_input_dir, gui_params)

    def _build_command(
        self,
        aretomo_dir: str,
        series: TiltSeries,
        batch_input_dir: str = None,
        gui_params: dict = None,
    ) -> str:
        """
        Build the actual AreTomo3 command string.

        Args:
            aretomo_dir: Path to AreTomo3 executable
            series: The tilt series to process
            batch_input_dir: Input directory for batch processing (if processing a batch item)
            gui_params: Parameters from Enhanced Parameters tab

        Returns:
            The complete command string

        Raises:
            ValueError: If required parameters are missing
        """
        if not series.files:
            raise ValueError(f"No files found for series {series.position_name}")

        if gui_params is None:
            gui_params = {}

        # Determine input and output directories with smart subdirectory detection
        if batch_input_dir:
            # Batch processing - detect if files are in subdirectories
            input_dir, actual_input_prefix = self._detect_input_structure(
                batch_input_dir, series
            )
            outdir = os.path.join(batch_input_dir, "aretomo_output")
        else:
            # Individual processing - use directory containing the files
            input_dir = os.path.dirname(series.files[0])
            actual_input_prefix = input_dir
            outdir = os.path.join(input_dir, "aretomo_output")

        os.makedirs(outdir, exist_ok=True)

        # Start building command (matching your working script format)
        cmd_parts = [aretomo_dir]

        # Determine input format and suffix based on file types
        first_file = series.files[0] if series.files else ""
        file_extension = os.path.splitext(first_file)[1].lower()

        # Essential I/O parameters with format-specific settings
        if file_extension in [".tif", ".tiff"]:
            # For TIFF files, use TIFF stack input
            cmd_parts.extend(
                [
                    f'-InPrefix "{actual_input_prefix}/"',  # Use detected input prefix
                    '-Insuffix ".tif"',  # TIFF suffix for SerialEM TIFF files
                    f'-OutDir "{outdir}"',
                ]
            )
            logger.info(f"Configured for TIFF input: {actual_input_prefix}")
        elif file_extension == ".mrc":
            # For MRC files
            cmd_parts.extend(
                [
                    f'-InPrefix "{actual_input_prefix}/"',  # Use detected input prefix
                    '-Insuffix ".mrc"',  # MRC suffix
                    f'-OutDir "{outdir}"',
                ]
            )
            logger.info(f"Configured for MRC input: {actual_input_prefix}")
        else:
            # Default to EER/MDOC format
            cmd_parts.extend(
                [
                    f'-InPrefix "{actual_input_prefix}/"',  # Use detected input prefix
                    '-Insuffix ".mdoc"',  # MDOC suffix for EER files
                    f'-OutDir "{outdir}"',
                ]
            )
            logger.info(f"Configured for EER/MDOC input: {actual_input_prefix}")

        # Core microscope parameters (from GUI or fallback to .mdoc)
        cmd_parts.extend(
            [
                f"-PixSize {gui_params.get('pix_size', 1.82)}",
                f"-Kv {gui_params.get('kv', 300)}",
                f"-Cs {gui_params.get('cs', 2.7)}",
                f"-AmpContrast {gui_params.get('amp_contrast', 0.1)}",
                f"-TiltAxis {gui_params.get('tilt_axis', 175.9)}",
                f"-FmDose {gui_params.get('fm_dose', 2.56)}",
            ]
        )

        # Essential processing parameters (matching your script)
        cmd_parts.extend(
            [
                "-FlipGain 1",  # From your script
                f"-McBin {gui_params.get('mc_bin', 1)}",
                f"-McPatch {gui_params.get(
                    'mc_patch_x',
                    1)} {gui_params.get('mc_patch_y',
                    1
                )}",
                "-FmInt 12",  # From your script
                "-SplitSum 1",
                f"-VolZ {gui_params.get('vol_z', 2048)}",
                f"-AtBin {gui_params.get('at_bin', 4)}",
                "-OutXF 1",  # From your script
                "-OutImod 1",  # From your script
                "-Wbp 1",  # From your script
                "-FlipVol 1",  # From your script
                "-TiltCor 1",  # From your script
                "-Patch 0 0",  # From your script
                f"-DarkTol {gui_params.get('dark_tol', 0.7)}",
                "-CorrCTF 1 15",  # From your script
            ]
        )

        # Add gain reference if available
        if series.gain_ref:
            cmd_parts.append(f'-Gain "{series.gain_ref}"')

        # Add GPU selection (space-separated, not comma-separated)
        gpu_ids = gui_params.get("gpu", "0")
        if gpu_ids and str(gpu_ids).strip():
            # Convert comma-separated to space-separated if needed
            gpu_str = str(gpu_ids).replace(",", " ")
            cmd_parts.append(f"-Gpu {gpu_str}")

        return " ".join(cmd_parts)

    def _detect_input_structure(self, base_dir: str, series: TiltSeries) -> tuple:
        """
        Detect whether files are in subdirectories or flat structure.

        Args:
            base_dir: Base directory to search in
            series: TiltSeries object with file information

        Returns:
            tuple: (
                input_dir,
                actual_input_prefix
            ) where actual_input_prefix is the correct path for AreTomo3
        """
        try:
            # Check if the series files are in the base directory (flat structure)
            if series.files:
                first_file_dir = os.path.dirname(series.files[0])

                # If the file is directly in the base directory
                if os.path.samefile(first_file_dir, base_dir):
                    logger.info(f"Flat structure detected for {series.position_name}")
                    return base_dir, base_dir

                # If the file is in a subdirectory, use that subdirectory
                elif first_file_dir.startswith(base_dir):
                    logger.info(
                        f"Subdirectory structure detected for {series.position_name}: {first_file_dir}"
                    )
                    return first_file_dir, first_file_dir

            # Fallback: look for subdirectory with series name
            potential_subdir = os.path.join(base_dir, series.position_name)
            if os.path.exists(potential_subdir):
                logger.info(
                    f"Found subdirectory for {series.position_name}: {potential_subdir}"
                )
                return potential_subdir, potential_subdir

            # Final fallback: use base directory
            logger.warning(
                f"Could not detect structure for {series.position_name}, using base directory"
            )
            return base_dir, base_dir

        except Exception as e:
            logger.error(
                f"Error detecting input structure for {series.position_name}: {e}"
            )
            return base_dir, base_dir

    def on_process(self) -> None:
        """Handle processing button click."""
        try:
            # Basic validation
            if not self.tilt_series:
                QMessageBox.warning(
                    self, "No Data", "Please load tilt series data first."
                )
                return

            # Get AreTomo3 path from Control Center for validation
            aretomo_path = ""
            if hasattr(self, "main_tab") and hasattr(self.main_tab, "aretomo_path"):
                aretomo_path = self.main_tab.aretomo_path.text().strip()

            if not aretomo_path:
                QMessageBox.warning(
                    self,
                    "No AreTomo3",
                    "Please set the AreTomo3 directory path in Control Center.",
                )
                return

            # Output directories will be created automatically in input directories

            # Get selected series
            selected_series = self.get_selected_series()
            if not selected_series:
                QMessageBox.warning(
                    self, "No Selection", "No tilt series selected for processing."
                )
                return

            # Process each selected series
            for series in selected_series:
                try:
                    # Construct command
                    command = self.construct_aretomo_command(series)
                    logger.info(f"Starting processing for {series.position_name}")
                    logger.debug(f"Command: {command}")

                    # Determine output directory for file browser monitoring
                    input_dir = os.path.dirname(series.files[0])
                    output_dir = os.path.join(
                        input_dir, "aretomo_output", series.position_name
                    )

                    # Switch file browser to monitor output directory
                    self._switch_file_browser_to_output_monitoring(output_dir)

                    # Update UI
                    self.log_text.append(f"Processing {series.position_name}...")
                    self.status_bar.showMessage(f"Processing {series.position_name}...")
                    self.update_progress_safely(0, 100)

                    # Create and start worker
                    worker = AreTomoWorker(command, series.position_name)
                    worker.progress.connect(self.handle_progress)
                    worker.finished.connect(
                        lambda s, m, ts=series: self.handle_process_finished(ts, s, m)
                    )
                    self.current_worker = worker

                    # Update unified processing monitor
                    if hasattr(self, "processing_monitor"):
                        self.processing_monitor.add_job(series.position_name, "Running")
                        self.processing_monitor.start_processing(series.position_name)

                    worker.start()

                except Exception as e:
                    logger.error(
                        f"Error setting up processing for {series.position_name}: {str(e)}"
                    )
                    QMessageBox.critical(
                        self,
                        "Processing Error",
                        f"Error setting up processing for {series.position_name}: {str(e)}",
                    )
        except Exception as e:
            logger.error(f"Error during processing: {str(e)}", exc_info=True)
            QMessageBox.critical(
                self,
                "Processing Error",
                f"An error occurred during processing:\n{str(e)}",
            )

    def handle_process_finished(
        self, series: TiltSeries, success: bool, message: str
    ) -> None:
        """Handle completion of a series processing."""
        logger.info(f"Processing finished for {series.position_name}: {message}")

        # Clear current worker
        self.current_worker = None
        self.update_progress_safely(100 if success else 0)

        # Restore file browser from output monitoring
        self._restore_file_browser_from_output_monitoring()

        # Update unified processing monitor
        status = "Completed" if success else "Failed"
        if hasattr(self, "processing_monitor"):
            self.processing_monitor.update_job_status(
                series.position_name, status, 100 if success else 0
            )

        if success:
            self.status_bar.showMessage(
                f"Successfully processed {series.position_name}"
            )
            self.log_text.append(f"✓ {series.position_name} processed successfully")

            # Update recent files list
            input_dir = os.path.dirname(series.files[0])
            outfile = os.path.join(
                input_dir,
                "aretomo_output",
                series.position_name,
                f"{series.position_name}.mrc",
            )
            if os.path.exists(outfile):
                self.update_recent_files(outfile)

            # Add to viewer if it exists
            if hasattr(self, "mrc_viewer") and self.mrc_viewer:
                self.mrc_viewer.load_mrc(outfile)

        else:
            self.status_bar.showMessage(f"Error processing {series.position_name}")
            self.log_text.append(
                f"✗ Error processing {series.position_name}: {message}"
            )
            QMessageBox.warning(
                self,
                "Processing Error",
                f"Error processing {series.position_name}:\n{message}",
            )

    def update_progress_safely(self, value, maximum=100):
        """Update the progress bar safely, checking if it exists first."""
        if hasattr(self, "progress_bar") and self.progress_bar is not None:
            self.progress_bar.setMaximum(maximum)
            self.progress_bar.setValue(value)
        else:
            logger.warning("Attempted to update non-existent progress bar")

    def handle_progress(self, message: str) -> None:
        """
        Handle progress updates from processing.

        Args:
            message: Progress message from worker
        """
        self.log_message(f"Progress: {message}")

        # Try to extract percentage from message if present
        try:
            percent = int(
                [s for s in message.split() if s.endswith("%")][0].replace("%", "")
            )
            self.update_progress_safely(percent)
        except (ValueError, IndexError):
            pass  # Not a percentage message or couldn't parse

        # Update unified monitor with current status
        if hasattr(self, "processing_monitor") and hasattr(self, "current_worker"):
            current_file = getattr(self.current_worker, "current_file", "Processing...")
            self.processing_monitor.update_job_status(
                current_file, "Running", details=message[:50]
            )

    def on_stop(self) -> None:
        """Handle stop button click."""
        # TODO: Implement proper stopping mechanism
        self.status_bar.showMessage("Stopping current operation...")

    def on_pause_processing(self) -> None:
        """Handle pause button click for queue processing."""
        if not self.is_processing:
            QMessageBox.information(
                self, "Not Processing", "No processing operation is currently running."
            )
            return

        # TODO: Implement proper pause/resume mechanism
        # For now, just update status
        self.status_bar.showMessage("Processing paused...")
        self.status_label.setText("Processing paused")

    def stop_batch_processing(self) -> None:
        """Stop current batch processing operations."""
        # Stop legacy single worker if running
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.terminate()

        # Stop new batch processing workers
        if hasattr(self, "batch_workers"):
            for worker in self.batch_workers.values():
                if worker.isRunning():
                    self.log_message(f"Stopping batch job: {position_name}")
                    worker.terminate()

                    # Update status in monitoring widgets
                    if hasattr(self, "processing_monitor"):
                        self.processing_monitor.update_job_status(
                            position_name, "Stopped"
                        )
                    if hasattr(self, "batch_widget"):
                        self.batch_widget.update_item_status(position_name, "Stopped")

            # Clear batch processing state
            self.batch_workers.clear()
            self.batch_queue = []
            self.current_batch_index = 0

        self.is_processing = False
        self.processing_queue.clear()

        # Re-enable batch processing controls
        if hasattr(self, "batch_widget"):
            self.batch_widget.start_batch_btn.setEnabled(True)
            self.batch_widget.stop_batch_btn.setEnabled(False)

        self.status_bar.showMessage("Batch processing stopped")

    def on_remove_from_queue(self) -> None:
        """Remove selected items from the processing queue."""
        selected_items = self.queue_list.selectedItems()
        for item in selected_items:
            self.queue_list.takeItem(self.queue_list.row(item))

    def on_clear_queue(self) -> None:
        """Clear all items from the processing queue."""
        self.queue_list.clear()
        self.processing_queue.clear()
        self.update_queue_stats()

    def on_add_current_to_queue(self) -> None:
        """Add current settings to the processing queue."""
        if not self.input_dir.text():
            QMessageBox.warning(
                self, "No Input Directory", "Please select an input directory first."
            )
            return

        # Create job entry
        job_entry = {
            "input_dir": self.input_dir.text(),
            "output_dir": os.path.join(self.input_dir.text(), "aretomo_output"),
            "pixel_size": self.pixel_size.value(),
            "voltage": self.voltage.value(),
            "cs": self.cs.value(),
            "tilt_axis": self.tilt_axis.value(),
            "status": "Queued",
        }

        # Add to queue
        self.processing_queue.append(job_entry)

        # Add to list widget
        job_name = (
            f"Job {len(self.processing_queue)}: {Path(job_entry['input_dir']).name}"
        )
        self.queue_list.addItem(job_name)

        self.update_queue_stats()
        self.status_bar.showMessage(f"Added job to queue: {job_name}")

    def on_start_queue_processing(self) -> None:
        """Start processing the queue."""
        if not self.processing_queue:
            QMessageBox.information(self, "Empty Queue", "No jobs in queue to process.")
            return

        if self.is_processing:
            QMessageBox.warning(
                self, "Already Processing", "Queue processing is already in progress."
            )
            return

        self.is_processing = True
        self.status_label.setText("Processing queue...")
        self.current_job_label.setText(
            f"Starting job 1 of {len(self.processing_queue)}"
        )
        self.status_bar.showMessage("Queue processing started")

        # TODO: Implement actual queue processing logic
        # For now, just simulate processing
        self.progress_bar.setValue(0)

    def on_start_batch_processing(self, batch_items: list) -> None:
        """Handle batch processing start signal from BatchProcessingWidget.

        Args:
            batch_items: List of batch items to process, each containing:
                - position: Position name
                - input_dir: Input directory path
                - series: TiltSeries object
                - status: Current status
                - file_count: Number of files
        """
        try:
            self.log_message(
                f"Received batch processing request for {len(batch_items)} items"
            )

            # Filter out items that don't have valid series data
            valid_items = [
                item
                for item in batch_items
                if item.get("series") is not None and item.get("status") != "Not Found"
            ]

            if not valid_items:
                self.log_message(
                    "No valid items found in batch for processing", "WARNING"
                )
                QMessageBox.warning(
                    self,
                    "No Valid Items",
                    "No valid tilt series found in batch for processing.",
                )
                return

            self.log_message(
                f"Starting batch processing of {len(valid_items)} valid items"
            )

            # Initialize batch processing state
            self.batch_queue = valid_items.copy()
            self.current_batch_index = 0
            self.is_processing = True

            # Update UI state
            self.status_bar.showMessage(
                f"Starting batch processing of {len(valid_items)} items..."
            )

            # Update monitoring widgets
            if hasattr(self, "processing_monitor"):
                for item in valid_items:
                    self.processing_monitor.add_job(item["position"], "Queued")

            # Start real-time monitoring for batch processing
            if hasattr(self, "realtime_analysis_tab"):
                for item in valid_items:
                    output_dir = item.get("output_dir")
                    if output_dir and os.path.exists(output_dir):
                        self.realtime_analysis_tab.add_monitoring_directory(output_dir)
                        logger.info(f"Added real-time monitoring for: {output_dir}")

            # Start processing the first batch item
            self._start_next_batch_item()

        except Exception as e:
            self.log_message(f"Error starting batch processing: {str(e)}", "ERROR")
            logger.error(f"Error in on_start_batch_processing: {str(e)}", exc_info=True)
            QMessageBox.critical(
                self,
                "Batch Processing Error",
                f"Error starting batch processing:\n{str(e)}",
            )

    def _start_next_batch_item(self) -> None:
        """Start processing the next item in the batch queue."""
        try:
            if not hasattr(self, "batch_queue") or not self.batch_queue:
                self.log_message("No more items in batch queue")
                self._finish_batch_processing()
                return

            if self.current_batch_index >= len(self.batch_queue):
                self.log_message("Batch processing completed - all items processed")
                self._finish_batch_processing()
                return

            # Get current item
            current_item = self.batch_queue[self.current_batch_index]
            position_name = current_item["position"]
            series = current_item["series"]
            input_dir = current_item["input_dir"]

            self.log_message(
                f"Starting batch item {self.current_batch_index + 1}/{len(self.batch_queue)}: {position_name}"
            )

            # Get output directory from batch item (auto-created)
            if "output_dir" in current_item and current_item["output_dir"]:
                output_base = current_item["output_dir"]
                self.log_message(f"Using auto-created output directory: {output_base}")
            else:
                self.log_message(
                    "No output directory in batch item, using input directory",
                    "WARNING",
                )
                output_base = input_dir

            # Get AreTomo3 path from Control Center
            aretomo_path = ""
            if hasattr(self, "main_tab") and hasattr(self.main_tab, "aretomo_path"):
                aretomo_path = self.main_tab.aretomo_path.text().strip()

            if not aretomo_path:
                self.log_message("AreTomo3 path not set in Control Center", "ERROR")
                self._handle_batch_item_error(
                    position_name, "AreTomo3 path not configured in Control Center"
                )
                return

            # Create output directory for this position
            position_output_dir = os.path.join(output_base, position_name)
            try:
                os.makedirs(position_output_dir, exist_ok=True)
            except OSError as e:
                self.log_message(
                    f"Could not create output directory {position_output_dir}: {str(e)}",
                    "ERROR",
                )
                self._handle_batch_item_error(
                    position_name, f"Could not create output directory: {str(e)}"
                )
                return

            # Prepare tilt series if needed (handle bracket notation, etc.)
            if hasattr(self, "batch_widget") and hasattr(
                self.batch_widget, "prepare_tilt_series"
            ):
                if not self.batch_widget.prepare_tilt_series(
                    series, input_dir, position_output_dir
                ):
                    self.log_message(
                        f"Failed to prepare tilt series for {position_name}", "ERROR"
                    )
                    self._handle_batch_item_error(
                        position_name, "Failed to prepare tilt series"
                    )
                    return

            # Construct AreTomo3 command
            try:
                command = self.construct_aretomo_command(series)
                self.log_message(f"Command for {position_name}: {command}")
            except Exception as e:
                self.log_message(
                    f"Error constructing command for {position_name}: {str(e)}", "ERROR"
                )
                self._handle_batch_item_error(
                    position_name, f"Error constructing command: {str(e)}"
                )
                return

            # Create and start worker
            worker = AreTomoWorker(command, position_name)
            worker.progress.connect(
                lambda msg, pos=position_name: self._handle_batch_progress(pos, msg)
            )
            worker.finished.connect(
                lambda success, msg, pos=position_name: self._handle_batch_item_finished(
                    pos, success, msg
                )
            )

            # Store worker reference
            if not hasattr(self, "batch_workers"):
                self.batch_workers = {}
            self.batch_workers[position_name] = worker

            # Update UI and monitoring
            self.status_bar.showMessage(
                f"Processing {position_name} ({self.current_batch_index + 1}/{len(self.batch_queue)})"
            )

            if hasattr(self, "processing_monitor"):
                self.processing_monitor.update_job_status(position_name, "Running")
                self.processing_monitor.start_processing(position_name)

            if hasattr(self, "batch_widget"):
                self.batch_widget.update_item_status(position_name, "Processing")

            # Start the worker
            worker.start()
            self.log_message(f"Started processing worker for {position_name}")

        except Exception as e:
            self.log_message(f"Error starting next batch item: {str(e)}", "ERROR")
            logger.error(f"Error in _start_next_batch_item: {str(e)}", exc_info=True)
            self._handle_batch_item_error("unknown", f"Internal error: {str(e)}")

    def _handle_batch_progress(self, position_name: str, message: str) -> None:
        """Handle progress updates from batch processing workers."""
        try:
            self.log_message(f"Batch progress [{position_name}]: {message}")

            # Update monitoring widgets
            if hasattr(self, "processing_monitor"):
                self.processing_monitor.update_job_status(
                    position_name, "Running", details=message[:50]
                )

            # Try to extract percentage from message
            try:
                percent = int(
                    [s for s in message.split() if s.endswith("%")][0].replace("%", "")
                )
                if hasattr(self, "processing_monitor"):
                    self.processing_monitor.update_processing_progress(
                        position_name, percent
                    )
            except (ValueError, IndexError):
                pass  # Not a percentage message

        except Exception as e:
            self.log_message(f"Error handling batch progress: {str(e)}", "ERROR")

    def _handle_batch_item_finished(
        self, position_name: str, success: bool, message: str
    ) -> None:
        """Handle completion of a batch processing item."""
        try:
            self.log_message(
                f"Batch item finished [{position_name}]: {'Success' if success else 'Failed'} - {message}"
            )

            # Update status in monitoring widgets
            status = "Completed" if success else "Failed"
            if hasattr(self, "processing_monitor"):
                self.processing_monitor.update_job_status(
                    position_name, status, details=message[:50]
                )

            if hasattr(self, "batch_widget"):
                self.batch_widget.update_item_status(position_name, status)

            # Clean up worker reference
            if hasattr(self, "batch_workers") and position_name in self.batch_workers:
                del self.batch_workers[position_name]

            # Move to next item
            self.current_batch_index += 1

            # Start next item or finish batch
            if self.current_batch_index < len(self.batch_queue):
                self.log_message(
                    f"Moving to next batch item ({self.current_batch_index + 1}/{len(self.batch_queue)})"
                )
                self._start_next_batch_item()
            else:
                self.log_message("All batch items completed")
                self._finish_batch_processing()

        except Exception as e:
            self.log_message(f"Error handling batch item completion: {str(e)}", "ERROR")
            logger.error(
                f"Error in _handle_batch_item_finished: {str(e)}", exc_info=True
            )

    def _handle_batch_item_error(self, position_name: str, error_message: str) -> None:
        """Handle errors during batch item processing."""
        try:
            self.log_message(
                f"Batch item error [{position_name}]: {error_message}", "ERROR"
            )

            # Update status in monitoring widgets
            if hasattr(self, "processing_monitor"):
                self.processing_monitor.update_job_status(
                    position_name, "Failed", details=error_message[:50]
                )

            if hasattr(self, "batch_widget"):
                self.batch_widget.update_item_status(position_name, "Failed")

            # Move to next item
            self.current_batch_index += 1

            # Continue with next item or finish batch
            if self.current_batch_index < len(self.batch_queue):
                self.log_message(
                    f"Continuing to next batch item after error ({self.current_batch_index + 1}/{len(self.batch_queue)})"
                )
                self._start_next_batch_item()
            else:
                self.log_message("Batch processing completed with errors")
                self._finish_batch_processing()

        except Exception as e:
            self.log_message(f"Error handling batch item error: {str(e)}", "ERROR")
            logger.error(f"Error in _handle_batch_item_error: {str(e)}", exc_info=True)

    def _finish_batch_processing(self) -> None:
        """Complete batch processing and reset state."""
        try:
            self.log_message("Finishing batch processing")

            # Reset batch processing state (don't delete, just reset)
            self.batch_queue = []
            self.current_batch_index = 0
            self.is_processing = False

            # Clean up any remaining workers
            if hasattr(self, "batch_workers"):
                for position_name, worker in list(self.batch_workers.items()):
                    if worker.isRunning():
                        self.log_message(
                            f"Terminating running worker for {position_name}"
                        )
                        worker.terminate()
                        worker.wait(5000)  # Wait up to 5 seconds
                self.batch_workers.clear()

            # Update UI
            self.status_bar.showMessage("Batch processing completed")

            # Re-enable batch processing controls
            if hasattr(self, "batch_widget"):
                self.batch_widget.start_batch_btn.setEnabled(True)
                self.batch_widget.stop_batch_btn.setEnabled(False)

            self.log_message("Batch processing finished successfully")

        except Exception as e:
            self.log_message(f"Error finishing batch processing: {str(e)}", "ERROR")
            logger.error(f"Error in _finish_batch_processing: {str(e)}", exc_info=True)

    def auto_start_web_server(self):
        """Automatically start the web server if not already running."""
        try:
            if hasattr(self, "web_server_widget") and self.web_server_widget:
                # Check if server is already running
                if not (
                    hasattr(self.web_server_widget, "is_running")
                    and self.web_server_widget.is_running
                ):
                    logger.info("Auto-starting web server...")
                    self.web_server_widget.start_server()

                    # Update Control Center web server button if it exists
                    if hasattr(self, "main_tab") and hasattr(
                        self.main_tab, "web_server_btn"
                    ):
                        self.main_tab.web_server_btn.setText("🌐 Stop Web Server")
                        self.main_tab.web_server_btn.setStyleSheet(
                            """
                            QPushButton {
                                font-size: 12px;
                                font-weight: bold;
                                background-color: #e74c3c;
                                color: white;
                                border: 2px solid #e74c3c;
                                border-radius: 6px;
                                padding: 8px;
                            }
                            QPushButton:hover {
                                background-color: #c0392b;
                                border-color: #c0392b;
                            }
                        """
                        )

                    logger.info("Web server auto-started successfully")
                else:
                    logger.info("Web server already running")
        except Exception as e:
            logger.error(f"Error auto-starting web server: {e}")

    def _preview_command(self) -> None:
        """Preview the AreTomo3 command that would be executed."""
        try:
            # Check if we have tilt series loaded
            if not hasattr(self, "tilt_series") or not self.tilt_series:
                self.command_preview.setPlainText("# Please load a tilt series first")
                return

            # Get the first series for preview
            series = next(iter(self.tilt_series.values()))

            # Construct the command
            try:
                command = self.construct_aretomo_command(series)

                # Format the command for better readability
                if len(command) > 80:
                    # Split long commands into multiple lines
                    parts = command.split(" -")
                    if len(parts) > 1:
                        formatted_parts = [parts[0]]  # First part (executable)
                        for part in parts[1:]:
                            formatted_parts.append(f"    -{part}")  # Indent parameters
                        formatted_command = " \\\n".join(formatted_parts)
                    else:
                        formatted_command = command
                else:
                    formatted_command = command

                self.command_preview.setPlainText(formatted_command)
                self.log_message(
                    f"Command preview generated for {series.position_name}"
                )

            except Exception as e:
                error_msg = f"# Error generating command: {str(e)}"
                self.command_preview.setPlainText(error_msg)
                self.log_message(f"Error generating command preview: {str(e)}", "ERROR")

        except Exception as e:
            error_msg = f"# Error in command preview: {str(e)}"
            self.command_preview.setPlainText(error_msg)
            self.log_message(f"Error in command preview: {str(e)}", "ERROR")
            logger.error(f"Error in _preview_command: {str(e)}", exc_info=True)

    def on_export(self) -> None:
        """Handle export functionality."""
        try:
            format_type = self.format_combo.currentText()
            use_compression = self.compression_check.isChecked()
            include_calibration = self.calibration_check.isChecked()

            # Check if we have data to export
            if not hasattr(self, "tilt_series") or not self.tilt_series:
                QMessageBox.warning(
                    self,
                    "Export Warning",
                    "No tilt series loaded. Please load data first.",
                )
                return

            # Get export file path
            file_dialog = QFileDialog()
            if format_type == "IMOD MRC":
                file_path, _ = file_dialog.getSaveFileName(
                    self, "Export as MRC", "", "MRC files (*.mrc)"
                )
            elif format_type == "TIFF Stack":
                file_path, _ = file_dialog.getSaveFileName(
                    self, "Export as TIFF", "", "TIFF files (*.tiff *.tif)"
                )
            else:
                file_path, _ = file_dialog.getSaveFileName(
                    self, f"Export as {format_type}", "", "All files (*)"
                )

            if file_path:
                self.log_message(f"Exporting as {format_type} to: {file_path}")
                # TODO: Implement actual export functionality
                QMessageBox.information(
                    self,
                    "Export",
                    f"Export functionality for {format_type} is not yet implemented.",
                )

        except Exception as e:
            logger.error(f"Error in export: {str(e)}", exc_info=True)
            QMessageBox.critical(
                self, "Export Error", f"Failed to export data: {str(e)}"
            )

    def on_save_log(self) -> None:
        """Save the current log to a file."""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getSaveFileName(
                self,
                "Save Log",
                f"aretomo3_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text files (*.txt);;All files (*)",
            )

            if file_path:
                with open(file_path, "w") as f:
                    f.write(self.log_text.toPlainText())

                self.log_message(f"Log saved to: {file_path}")
                QMessageBox.information(
                    self, "Log Saved", f"Log successfully saved to:\n{file_path}"
                )

        except Exception as e:
            logger.error(f"Error saving log: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "Save Error", f"Failed to save log: {str(e)}")

    def _connect_parameter_changes(self) -> None:
        """Connect parameter change signals for automatic updates."""
        try:
            if hasattr(self, "parameter_manager"):
                self.parameter_manager.connect_parameter_changes()
            logger.info("Parameter change signals connected successfully")
        except Exception as e:
            logger.error(f"Error connecting parameter changes: {e}")

    def _connect_tab_change_signal(self) -> None:
        """Connect tab change signal for auto-loading analysis files."""
        try:
            if hasattr(self, "tabs"):
                self.tabs.currentChanged.connect(self._on_tab_changed)
            logger.info("Tab change signal connected successfully")
        except Exception as e:
            logger.error(f"Error connecting tab change signal: {e}")

    def _on_tab_changed(self, index: int) -> None:
        """Handle tab change events."""
        try:
            # Auto-load analysis files when switching to analysis tab
            if hasattr(self, "tabs") and index < self.tabs.count():
                tab_name = self.tabs.tabText(index)
                if "Analysis" in tab_name:
                    self._auto_load_analysis_files()
        except Exception as e:
            logger.error(f"Error handling tab change: {e}")

    def _auto_load_analysis_files(self) -> None:
        """Auto-load analysis files when switching to analysis tab."""
        try:
            # Implementation for auto-loading analysis files
            logger.debug("Auto-loading analysis files")
        except Exception as e:
            logger.error(f"Error auto-loading analysis files: {e}")

    def closeEvent(self, event):
        """Handle application close event."""
        try:
            logger.info("Application closing - starting cleanup...")

            # Cleanup Napari viewer first to prevent crashes
            if hasattr(self, "napari_viewer") and self.napari_viewer:
                logger.info("Cleaning up Napari viewer...")
                try:
                    self.napari_viewer.cleanup()
                except Exception as e:
                    logger.warning(f"Error cleaning up Napari viewer: {e}")

            # Stop system monitoring
            if hasattr(self, "system_monitor") and self.system_monitor:
                try:
                    self.system_monitor.stop()
                    logger.info("System monitor stopped")
                except Exception as e:
                    logger.warning(f"Error stopping system monitor: {e}")

            # Stop any running processes
            if hasattr(self, "live_processor") and self.live_processor:
                try:
                    self.live_processor.stop()
                    logger.info("Live processor stopped")
                except Exception as e:
                    logger.warning(f"Error stopping live processor: {e}")

            # Stop batch processing workers
            if hasattr(self, "batch_workers"):
                for position_name, worker in list(self.batch_workers.items()):
                    try:
                        if worker.isRunning():
                            logger.info(f"Terminating worker for {position_name}")
                            worker.terminate()
                            worker.wait(3000)  # Wait up to 3 seconds
                    except Exception as e:
                        logger.warning(f"Error terminating worker {position_name}: {e}")
                self.batch_workers.clear()

            # Stop current worker
            if hasattr(self, "current_worker") and self.current_worker:
                try:
                    if self.current_worker.isRunning():
                        logger.info("Terminating current worker")
                        self.current_worker.terminate()
                        self.current_worker.wait(3000)
                except Exception as e:
                    logger.warning(f"Error terminating current worker: {e}")

            # Save current session
            try:
                self._save_current_session()
                logger.info("Session saved successfully")
            except Exception as e:
                logger.warning(f"Error saving session: {e}")

            logger.info("Application cleanup completed successfully")

            # Accept the close event
            event.accept()

        except Exception as e:
            logger.error(f"Error during application close: {e}")
            # Still close even if there's an error
            event.accept()

    def _setup_tab_integration(self):
        """Set up integration and data sharing between tabs."""
        try:
            logger.info("Setting up tab integration and data sharing")

            # Set up shared data attributes
            self.shared_data = {
                "input_dir": None,
                "output_dir": None,
                "current_series": None,
                "processing_status": "idle",
                "analysis_data": {},
                "ctf_data": {},
                "motion_data": {},
            }

            logger.info("Tab integration setup completed")

        except Exception as e:
            logger.error(f"Error setting up tab integration: {e}")

# Create alias for backward compatibility
AreTomo3MainWindow = AreTomoGUI