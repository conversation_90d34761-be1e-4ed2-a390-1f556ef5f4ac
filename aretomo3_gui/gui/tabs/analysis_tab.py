"""
Analysis tab manager for AreTomo3 GUI.
Handles the analysis tab with data visualization and analysis.
"""
import logging
from typing import Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QGroupBox, 
    QComboBox, QCheckBox, QSplitter
)
from PyQt6.QtCore import Qt

from ..viewers.analysis_viewer import AnalysisViewer

logger = logging.getLogger(__name__)


class AnalysisTabManager:
    """Manages the analysis tab setup and functionality."""
    
    def __init__(self, main_window):
        """Initialize the analysis tab manager.
        
        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        self.analysis_viewer: Optional[AnalysisViewer] = None
    
    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the analysis tab for data visualization and analysis.
        
        Args:
            tab_widget: The parent widget for the analysis tab
            layout: The layout to add widgets to
        """
        try:
            vsplitter = QSplitter(Qt.Orientation.Vertical)

            # Upper section - Visualization
            viz_widget = QWidget()
            viz_layout = QVBoxLayout()

            # View controls
            controls = QHBoxLayout()
            self._setup_view_controls(controls)
            self._setup_analysis_controls(controls)
            viz_layout.addLayout(controls)

            # Visualization area
            self.analysis_viewer = AnalysisViewer()
            viz_layout.addWidget(self.analysis_viewer)

            viz_widget.setLayout(viz_layout)
            vsplitter.addWidget(viz_widget)

            # Note: Metrics dashboard temporarily commented out due to scipy issue
            # Lower section - Metrics dashboard
            # self.metrics_dashboard = MetricsDashboard()
            # vsplitter.addWidget(self.metrics_dashboard)

            vsplitter.setStretchFactor(0, 2)
            vsplitter.setStretchFactor(1, 1)

            layout.addWidget(vsplitter)
            logger.info("Analysis tab setup completed successfully")
            
        except Exception as e:
            logger.error(f"Error setting up analysis tab: {str(e)}", exc_info=True)
            raise
    
    def _setup_view_controls(self, layout: QHBoxLayout) -> None:
        """Setup view type controls."""
        view_controls = QWidget()
        view_layout = QHBoxLayout()
        
        self.main_window.view_type = QComboBox()
        self.main_window.view_type.addItems(["Tomogram", "CTF", "Motion", "Statistics"])
        view_layout.addWidget(QLabel("View:"))
        view_layout.addWidget(self.main_window.view_type)
        
        view_controls.setLayout(view_layout)
        layout.addWidget(view_controls)
    
    def _setup_analysis_controls(self, layout: QHBoxLayout) -> None:
        """Setup analysis control buttons."""
        analysis_controls = QWidget()
        analysis_layout = QHBoxLayout()

        self.main_window.auto_update = QCheckBox("Auto Update")
        self.main_window.auto_update.setChecked(True)
        analysis_layout.addWidget(self.main_window.auto_update)

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.main_window._refresh_analysis)
        analysis_layout.addWidget(refresh_btn)

        analysis_controls.setLayout(analysis_layout)
        layout.addWidget(analysis_controls)
    
    def get_analysis_viewer(self) -> Optional[AnalysisViewer]:
        """Get the analysis viewer instance."""
        return self.analysis_viewer
