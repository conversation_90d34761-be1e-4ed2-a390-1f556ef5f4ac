"""
Export tab manager for AreTomo3 GUI.
Handles the export tab for various file format exports.
"""
import logging

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QGroupBox, QComboBox, QGridLayout, 
    QCheckBox, QPushButton
)

logger = logging.getLogger(__name__)


class ExportTabManager:
    """Manages the export tab setup and functionality."""
    
    def __init__(self, main_window):
        """Initialize the export tab manager.
        
        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
    
    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the export tab for various file format exports.
        
        Args:
            tab_widget: The parent widget for the export tab
            layout: The layout to add widgets to
        """
        try:
            # Export format selection
            self._setup_format_selection(tab_widget, layout)
            
            # Export options
            self._setup_export_options(tab_widget, layout)
            
            # Export button
            self._setup_export_button(tab_widget, layout)
            
            # Add stretch to push everything to top
            layout.addStretch()
            
            logger.info("Export tab setup completed successfully")
            
        except Exception as e:
            logger.error(f"Error setting up export tab: {str(e)}", exc_info=True)
            raise
    
    def _setup_format_selection(self, parent: QWidget, layout: QVBoxLayout) -> None:
        """Setup export format selection."""
        format_group = QGroupBox("Export Format", parent)
        format_layout = QVBoxLayout()
        
        self.main_window.format_combo = QComboBox()
        self.main_window.format_combo.addItems([
            "IMOD MRC",
            "TIFF Stack",
            "Relion",
            "EMAN2",
            "ImageJ"
        ])
        format_layout.addWidget(self.main_window.format_combo)
        
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)
    
    def _setup_export_options(self, parent: QWidget, layout: QVBoxLayout) -> None:
        """Setup export options."""
        options_group = QGroupBox("Export Options", parent)
        options_layout = QGridLayout()
        
        self.main_window.compression_check = QCheckBox("Use Compression")
        options_layout.addWidget(self.main_window.compression_check, 0, 0)
        
        self.main_window.calibration_check = QCheckBox("Include Calibration")
        options_layout.addWidget(self.main_window.calibration_check, 1, 0)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
    
    def _setup_export_button(self, parent: QWidget, layout: QVBoxLayout) -> None:
        """Setup export button."""
        export_btn = QPushButton("Export")
        export_btn.clicked.connect(self.main_window.on_export)
        layout.addWidget(export_btn)
