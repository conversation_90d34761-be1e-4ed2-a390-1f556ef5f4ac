"""
Analysis viewer component for AreTomo3 GUI.
"""
import os
import sys
from typing import Optional, List, Dict, Any
from pathlib import Path
import logging

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt, pyqtSlot

from ...utils.utils import ProgressDialog, export_plot
from ...core.file_watcher import FileWatcher
from ...core.error_handling import handle_exception, FileSystemError
from ...core.config.config import MICROSCOPE_SETTINGS, ANALYSIS_SETTINGS

import numpy as np
import logging
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QComboBox, QPushButton, QGroupBox, QTabWidget,
                           QTextEdit, QMessageBox, QFileDialog, QToolBar)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction
import mrcfile
import os
import re
from ...utils.utils import ProgressDialog, export_plot

# Set up logging
logger = logging.getLogger(__name__)

class MotionCorrectionTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        
        # Toolbar
        toolbar = QToolBar()
        export_action = QAction("Export Plot", self)
        export_action.setToolTip("Export the current plot")
        export_action.triggered.connect(self.export_current_plot)
        toolbar.addAction(export_action)
        layout.addWidget(toolbar)
        
        # Motion correction trajectory plot
        self.figure = Figure(figsize=(8, 8))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        layout.addWidget(self.canvas)
        
        # Stats display
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(100)
        layout.addWidget(self.stats_text)
        
        self.setLayout(layout)
        
    def export_current_plot(self):
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Plot",
                "",
                f"Images (*.{ANALYSIS_SETTINGS['plot_format']})"
            )
            if filename:
                if export_plot(self.figure, filename,
                           dpi=ANALYSIS_SETTINGS['plot_dpi'],
                           format=ANALYSIS_SETTINGS['plot_format']):
                    QMessageBox.information(self, "Success", "Plot exported successfully!")
                else:
                    QMessageBox.warning(self, "Error", "Failed to export plot")
        except Exception as e:
            logger.error(f"Error exporting plot: {e}")
            QMessageBox.warning(self, "Error", f"Failed to export plot: {str(e)}")

    def plot_motion(self, xf_file):
        if not os.path.exists(xf_file):
            logger.error(f"Motion correction file not found: {xf_file}")
            return False
            
        try:
            data = np.loadtxt(xf_file)
            if data.shape[1] < 6:
                logger.error(f"Invalid data format in {xf_file}")
                return False
                
            shifts_x = data[:, 4]  # Translation X
            shifts_y = data[:, 5]  # Translation Y
            
            # Calculate cumulative shifts
            cum_shifts_x = np.cumsum(shifts_x)
            cum_shifts_y = np.cumsum(shifts_y)
            
            self.ax.clear()
            
            # Plot trajectory with error handling for plotting
            try:
                # Plot trajectory
                self.ax.plot(cum_shifts_x, cum_shifts_y, '-o', label='Trajectory')
                
                # Add arrows to show direction
                for i in range(len(cum_shifts_x)-1):
                    self.ax.arrow(cum_shifts_x[i], cum_shifts_y[i],
                                shifts_x[i+1], shifts_y[i+1],
                                head_width=0.1, head_length=0.2,
                                fc='b', ec='b', alpha=0.5)
                
                # Add frame numbers
                for i, (x, y) in enumerate(zip(cum_shifts_x, cum_shifts_y)):
                    self.ax.annotate(f'{i+1}', (x, y),
                                   xytext=(5, 5), textcoords='offset points')
                
                self.ax.set_xlabel('Cumulative X Shift (pixels)')
                self.ax.set_ylabel('Cumulative Y Shift (pixels)')
                self.ax.set_title('Motion Correction Trajectory')
                self.ax.grid(True)
                self.ax.set_aspect('equal')
            except Exception as plot_error:
                logger.error(f"Error plotting trajectory: {plot_error}")
                return False
            
            # Calculate statistics
            try:
                total_motion = np.sqrt(np.sum(shifts_x**2 + shifts_y**2))
                max_shift = np.max(np.sqrt(shifts_x**2 + shifts_y**2))
                avg_shift = np.mean(np.sqrt(shifts_x**2 + shifts_y**2))
                
                stats = f"Total Motion: {total_motion:.2f} pixels\n"
                stats += f"Maximum Shift: {max_shift:.2f} pixels\n"
                stats += f"Average Shift: {avg_shift:.2f} pixels"
                self.stats_text.setText(stats)
                
                logger.info(f"Successfully plotted motion correction for {xf_file}")
                
                self.canvas.draw()
                return True
            except Exception as stats_error:
                logger.error(f"Error calculating statistics: {stats_error}")
                return False
                
        except Exception as e:
            logger.error(f"Error loading motion correction data: {e}")
            QMessageBox.warning(self, "Error", 
                              f"Failed to load motion correction data: {str(e)}")
            return False

class TiltSeriesTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        
        # Controls
        controls = QHBoxLayout()
        self.frame_select = QComboBox()
        self.frame_select.currentIndexChanged.connect(self.update_display)
        controls.addWidget(QLabel("Frame:"))
        controls.addWidget(self.frame_select)
        layout.addLayout(controls)
        
        # Tilt angle plot
        self.figure = Figure(figsize=(10, 4))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        layout.addWidget(self.canvas)
        
        # Status display
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(100)
        layout.addWidget(self.stats_text)
        
        self.setLayout(layout)
        
        self.angles = []
        self.current_frame = 0
        
    def plot_tilt_series(self, angles):
        self.angles = angles
        self.ax.clear()
        frame_numbers = np.arange(len(angles))
        
        # Plot all angles
        self.ax.plot(frame_numbers, angles, '-o', label='Tilt Angles')
        
        # Highlight current frame
        if self.current_frame < len(angles):
            self.ax.plot(self.current_frame, angles[self.current_frame], 'ro', 
                        markersize=10, label='Current Frame')
            
        self.ax.set_xlabel('Frame Number')
        self.ax.set_ylabel('Tilt Angle (°)')
        self.ax.set_title('Tilt Angle Distribution')
        self.ax.grid(True)
        self.ax.legend()
        
        # Calculate and display statistics
        angle_range = max(angles) - min(angles)
        angle_step = np.mean(np.abs(np.diff(sorted(angles))))
        stats = f"Angular Range: {angle_range:.1f}°\n"
        stats += f"Average Step Size: {angle_step:.1f}°\n"
        stats += f"Current Angle: {angles[self.current_frame]:.1f}°"
        self.stats_text.setText(stats)
        
        self.canvas.draw()
        
    def update_display(self, index):
        if 0 <= index < len(self.angles):
            self.current_frame = index
            self.plot_tilt_series(self.angles)

class CTFTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        
        # Frame selection
        frame_layout = QHBoxLayout()
        self.frame_select = QComboBox()
        self.frame_select.currentIndexChanged.connect(self.update_display)
        frame_layout.addWidget(QLabel("Frame:"))
        frame_layout.addWidget(self.frame_select)
        layout.addLayout(frame_layout)
        
        # CTF plot
        plot_group = QGroupBox("CTF Fit")
        plot_layout = QVBoxLayout()
        self.figure = Figure(figsize=(8, 6))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        plot_layout.addWidget(self.canvas)
        plot_group.setLayout(plot_layout)
        layout.addWidget(plot_group)
        
        # CTF parameters display
        params_group = QGroupBox("CTF Parameters")
        params_layout = QVBoxLayout()
        self.ctf_params = QTextEdit()
        self.ctf_params.setReadOnly(True)
        self.ctf_params.setMaximumHeight(150)
        params_layout.addWidget(self.ctf_params)
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        self.setLayout(layout)
        self.data = None
        
    def load_ctf_results(self, ctf_file):
        progress = ProgressDialog(
            "Loading CTF Results",
            "Reading CTF data...",
            0, 100,
            self
        )
        
        try:
            progress.update(10, "Reading CTF data...")
            self.data = np.loadtxt(ctf_file)
            if self.data.ndim == 1:
                self.data = self.data.reshape(1, -1)
            
            progress.update(40, "Updating frame selector...")
            self.frame_select.clear()
            self.frame_select.addItems([f"Frame {i+1}" for i in range(len(self.data))])
            
            progress.update(70, "Processing first frame...")
            self.update_display(0)
            
            progress.update(100, "Complete!")
            logger.info(f"Successfully loaded CTF results from {ctf_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading CTF results: {e}")
            QMessageBox.warning(self, "Error", 
                              f"Failed to load CTF results: {str(e)}")
            return False
        finally:
            progress.close()
    
    def update_display(self, index):
        if self.data is None or index < 0 or index >= len(self.data):
            return
            
        try:
            # Extract parameters for current frame
            df1 = self.data[index, 0]  # Defocus 1
            df2 = self.data[index, 1]  # Defocus 2
            ast_angle = self.data[index, 2]  # Astigmatism angle
            max_res = self.data[index, 3]  # Maximum resolution
            fit_res = self.data[index, 4]  # Fit resolution
            cc = self.data[index, 5]  # Cross correlation
            
            # Calculate derived parameters
            avg_defocus = (df1 + df2) / 2
            astigmatism = abs(df1 - df2)
            
            # Update parameter display
            params_text = [
                f"Average Defocus: {avg_defocus/1e4:.2f} µm",
                f"Defocus Range: {df1/1e4:.2f} - {df2/1e4:.2f} µm",
                f"Astigmatism: {astigmatism/1e4:.2f} µm",
                f"Astig. Angle: {ast_angle:.1f}°",
                f"Resolution Range: {fit_res:.1f} - {max_res:.1f} Å",
                f"Fit Quality (CC): {cc:.3f}"
            ]
            self.ctf_params.setText('\n'.join(params_text))
            
            # Plot 1D CTF profile
            self.plot_ctf_profile(avg_defocus, max_res)
            
        except Exception as e:
            logger.error(f"Error updating CTF display: {e}")
            
    def plot_ctf_profile(self, defocus, max_res):
        """Plot theoretical 1D CTF profile"""
        try:
            # Calculate theoretical CTF
            s = np.linspace(0, 1/max_res, 1000)  # spatial frequency
            gamma = 0.07  # amplitude contrast
            voltage = MICROSCOPE_SETTINGS['voltage']
            lambda_ = 12.3986 / np.sqrt(voltage * (1022.0 + voltage))  # wavelength calculation
            Cs = MICROSCOPE_SETTINGS['cs']  # spherical aberration
            
            chi = np.pi * lambda_ * defocus * s**2 + 0.5 * np.pi * Cs * lambda_**3 * s**4
            ctf = -np.sqrt(1 - gamma**2) * np.sin(chi) - gamma * np.cos(chi)
            
            self.ax.clear()
            self.ax.plot(s, ctf)
            self.ax.set_xlabel('Spatial Frequency (1/Å)')
            self.ax.set_ylabel('CTF Amplitude')
            self.ax.set_title(f'Theoretical CTF Profile (Defocus: {defocus/1e4:.2f} µm)')
            self.ax.grid(True)
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"Error plotting CTF profile: {e}")
            QMessageBox.warning(self, "Error", f"Failed to plot CTF profile: {str(e)}")

class AnalysisViewer(QWidget):
    """
    Widget for viewing and analyzing tilt series reconstruction results.
    """
    def __init__(self, parent: Optional[QWidget] = None) -> None:
        super().__init__(parent)
        
        # Track current file
        self.current_file: Optional[str] = None
        
        # Setup the UI with tabs
        self.setup_ui()

    def setup_ui(self) -> None:
        """Set up the user interface for the analysis viewer."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create individual tabs
        self.motion_tab = MotionCorrectionTab(self)
        self.ctf_tab = CTFTab(self)
        self.tilt_tab = TiltSeriesTab(self)
        
        # Add tabs to widget
        self.tab_widget.addTab(self.motion_tab, "Motion Correction")
        self.tab_widget.addTab(self.ctf_tab, "CTF")
        self.tab_widget.addTab(self.tilt_tab, "Tilt Series")

    def load_file(self, file_path: str) -> None:
        """
        Load a data file for analysis.

        Args:
            file_path: Path to the data file to load
        """
        try:
            self.current_file = str(file_path)
            
            # Determine what type of file this is and load into appropriate tab
            file_path_obj = Path(file_path)
            
            if file_path_obj.is_dir():
                # If it's a directory, look for analysis files
                self.load_directory_analysis(file_path_obj)
            else:
                # If it's a specific file, determine type and load
                self.load_specific_file(file_path_obj)
                
        except Exception as e:
            logging.error(f"Failed to load file {file_path}: {e}")
            raise FileSystemError(f"Failed to load file: {e}") from e
    
    def load_directory_analysis(self, directory: Path) -> None:
        """Load analysis data from a directory containing AreTomo3 output."""
        try:
            # Look for motion correction files (.xf files)
            xf_files = list(directory.glob("*.xf"))
            if xf_files:
                logger.info(f"Found motion correction file: {xf_files[0]}")
                self.load_motion_correction(str(xf_files[0]))
                # Switch to motion correction tab
                self.tab_widget.setCurrentWidget(self.motion_tab)
            
            # Look for CTF files
            ctf_files = list(directory.glob("*_ctf.txt")) or list(directory.glob("*ctf*.txt"))
            if ctf_files:
                logger.info(f"Found CTF file: {ctf_files[0]}")
                self.load_ctf_results(str(ctf_files[0]))
            
            # Look for tilt angle information
            tlt_files = list(directory.glob("*.tlt"))
            if tlt_files:
                logger.info(f"Found tilt file: {tlt_files[0]}")
                self.load_tilt_angles(str(tlt_files[0]))
            
            logger.info(f"Successfully loaded analysis data from {directory}")
            
        except Exception as e:
            logger.error(f"Error loading directory analysis: {e}")
            raise
    
    def load_specific_file(self, file_path: Path) -> None:
        """Load a specific analysis file."""
        try:
            if file_path.suffix == '.xf':
                self.load_motion_correction(str(file_path))
                self.tab_widget.setCurrentWidget(self.motion_tab)
            elif 'ctf' in file_path.name.lower():
                self.load_ctf_results(str(file_path))
                self.tab_widget.setCurrentWidget(self.ctf_tab)
            elif file_path.suffix == '.tlt':
                self.load_tilt_angles(str(file_path))
                self.tab_widget.setCurrentWidget(self.tilt_tab)
            else:
                logger.warning(f"Unknown file type: {file_path}")
                
        except Exception as e:
            logger.error(f"Error loading specific file: {e}")
            raise
    
    def load_tilt_angles(self, tlt_file: str) -> None:
        """Load tilt angles from .tlt file."""
        try:
            angles = np.loadtxt(tlt_file)
            self.update_tilt_series(angles)
            logger.info(f"Successfully loaded tilt angles from {tlt_file}")
        except Exception as e:
            logger.error(f"Error loading tilt angles: {e}")
            raise

    def load_motion_correction(self, xf_file):
        """Load and display motion correction data"""
        return self.motion_tab.plot_motion(xf_file)
        
    def load_ctf_results(self, ctf_file):
        """Load and display CTF results"""
        return self.ctf_tab.load_ctf_results(ctf_file)
        
    def update_tilt_series(self, angles):
        """Update tilt series display"""
        self.tilt_tab.plot_tilt_series(angles)
        
    @property
    def frame_select(self):
        return self.tilt_tab.frame_select
