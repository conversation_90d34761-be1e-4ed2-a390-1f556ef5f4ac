import mrcfile
import numpy as np
import os
import glob
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas, NavigationToolbar2QT
from matplotlib.figure import Figure
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.pyplot as plt
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox,
                          QSlider, QPushButton, QFileDialog, QGroupBox, QToolBar,
                          QMessageBox, QComboBox, QSplitter, QTextEdit, QTabWidget,
                          QGridLayout, QCheckBox, QProgressBar, QListWidget, QListWidgetItem,
                          QDialog, QDialogButtonBox, QButtonGroup, QFrame)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QFont
import logging

# Try to import 3D visualization libraries
try:
    from skimage import measure
    from mpl_toolkits.mplot3d.art3d import Poly3DCollection
    HAS_3D_SUPPORT = True
except ImportError:
    HAS_3D_SUPPORT = False
    logging.warning("3D visualization libraries not available. Install scikit-image for 3D support.")

logger = logging.getLogger(__name__)

class ProcessingMonitor(QWidget):
    """Real-time processing monitor for auto-loading results."""

    new_file_detected = pyqtSignal(str)  # Signal when new processed file is found

    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitoring_dir = None
        self.known_files = set()
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_for_new_files)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Monitoring controls
        monitor_group = QGroupBox("🔴 Live Processing Monitor")
        monitor_layout = QVBoxLayout(monitor_group)

        # Directory selection
        dir_layout = QHBoxLayout()
        self.dir_label = QLabel("No directory selected")
        self.dir_label.setStyleSheet("color: #6c757d; font-style: italic;")
        self.browse_btn = QPushButton("📁 Select Output Directory")
        self.browse_btn.clicked.connect(self.select_directory)

        dir_layout.addWidget(self.dir_label, 1)
        dir_layout.addWidget(self.browse_btn)
        monitor_layout.addLayout(dir_layout)

        # Monitoring toggle
        self.monitor_checkbox = QCheckBox("🔍 Auto-detect new files")
        self.monitor_checkbox.toggled.connect(self.toggle_monitoring)
        monitor_layout.addWidget(self.monitor_checkbox)

        # File list
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(100)
        self.file_list.itemDoubleClicked.connect(self.load_selected_file)
        monitor_layout.addWidget(QLabel("Recent files:"))
        monitor_layout.addWidget(self.file_list)

        layout.addWidget(monitor_group)

    def select_directory(self):
        """Select directory to monitor for new files."""
        directory = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if directory:
            self.monitoring_dir = directory
            self.dir_label.setText(f"Monitoring: {os.path.basename(directory)}")
            self.dir_label.setStyleSheet("color: #28a745; font-weight: bold;")
            self.scan_existing_files()

    def toggle_monitoring(self, enabled):
        """Start or stop monitoring."""
        if enabled and self.monitoring_dir:
            self.timer.start(2000)  # Check every 2 seconds
            logger.info(f"Started monitoring {self.monitoring_dir}")
        else:
            self.timer.stop()
            logger.info("Stopped monitoring")

    def scan_existing_files(self):
        """Scan for existing MRC files in the directory."""
        if not self.monitoring_dir:
            return

        pattern = os.path.join(self.monitoring_dir, "**/*.mrc")
        files = glob.glob(pattern, recursive=True)
        self.known_files = set(files)

        # Update file list
        self.file_list.clear()
        for file_path in sorted(files, key=os.path.getmtime, reverse=True)[:10]:
            item = QListWidgetItem(f"📄 {os.path.basename(file_path)}")
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            self.file_list.addItem(item)

    def check_for_new_files(self):
        """Check for new MRC files."""
        if not self.monitoring_dir:
            return

        pattern = os.path.join(self.monitoring_dir, "**/*.mrc")
        current_files = set(glob.glob(pattern, recursive=True))
        new_files = current_files - self.known_files

        for new_file in new_files:
            logger.info(f"New file detected: {new_file}")
            self.new_file_detected.emit(new_file)

            # Add to file list
            item = QListWidgetItem(f"🆕 {os.path.basename(new_file)}")
            item.setData(Qt.ItemDataRole.UserRole, new_file)
            self.file_list.insertItem(0, item)

            # Keep only recent 10 files
            while self.file_list.count() > 10:
                self.file_list.takeItem(self.file_list.count() - 1)

        self.known_files = current_files

    def load_selected_file(self, item):
        """Load the selected file."""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if file_path:
            self.new_file_detected.emit(file_path)

class ElegantSlider(QSlider):
    """Custom slider with napari-inspired styling."""

    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }

            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #b4b4b4, stop:1 #8f8f8f);
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }

            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #d4d4d4, stop:1 #afafaf);
            }

            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #66e, stop:1 #44c);
                border: 1px solid #777;
                height: 8px;
                border-radius: 4px;
            }
        """)

class HistogramWidget(QWidget):
    """Napari-inspired histogram widget for contrast adjustment."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data = None
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Create matplotlib figure for histogram
        self.figure = Figure(figsize=(4, 2))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # Style the histogram plot
        self.figure.patch.set_facecolor('none')
        self.ax.set_facecolor('none')

        layout.addWidget(self.canvas)

    def update_histogram(self, data):
        """Update histogram with new data."""
        if data is None:
            return

        self.data = data
        self.ax.clear()

        # Calculate histogram
        hist, bins = np.histogram(data.flatten(), bins=100, density=True)

        # Plot histogram with elegant styling
        self.ax.fill_between(bins[:-1], hist, alpha=0.7, color='#4CAF50')
        self.ax.plot(bins[:-1], hist, color='#2E7D32', linewidth=1.5)

        # Style the plot
        self.ax.set_xlabel('Intensity', fontsize=8)
        self.ax.set_ylabel('Density', fontsize=8)
        self.ax.tick_params(labelsize=7)
        self.ax.grid(True, alpha=0.3)

        # Remove top and right spines
        self.ax.spines['top'].set_visible(False)
        self.ax.spines['right'].set_visible(False)

        self.figure.tight_layout()
        self.canvas.draw()

class MeasurementTool(QWidget):
    """Napari-inspired measurement tools."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.measurements = []
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Tool selection
        tool_group = QGroupBox("Measurement Tools")
        tool_layout = QHBoxLayout(tool_group)

        self.distance_btn = QPushButton("📏 Distance")
        self.angle_btn = QPushButton("📐 Angle")
        self.area_btn = QPushButton("⬜ Area")
        self.clear_btn = QPushButton("🗑️ Clear")

        for btn in [self.distance_btn, self.angle_btn, self.area_btn, self.clear_btn]:
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-weight: 500;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                    color: white;
                }
            """)
            tool_layout.addWidget(btn)

        layout.addWidget(tool_group)

        # Measurements display
        self.measurements_text = QTextEdit()
        self.measurements_text.setMaximumHeight(100)
        self.measurements_text.setPlaceholderText("Measurements will appear here...")
        layout.addWidget(self.measurements_text)

class IntegratedViewer(QWidget):
    """Integrated 2D/3D Viewer with slice and slab viewing capabilities."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.volume = None
        self.current_slice = 0
        self.current_view = 'xy'
        self.view_mode = 'slice'  # 'slice', 'slab', '3d'
        self.slab_thickness = 5
        self.iso_value = 0.5
        self.alpha = 0.7
        self.current_cmap = 'gray'
        self.contrast_limits = [0, 255]
        self.setup_ui()

    def setup_ui(self):
        """Setup integrated viewer UI with unified controls."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # Unified control panel at top
        controls_frame = QFrame()
        controls_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        controls_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(248, 249, 250, 0.95);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        controls_layout = QVBoxLayout(controls_frame)
        controls_layout.setContentsMargins(10, 8, 10, 8)
        controls_layout.setSpacing(8)

        # First row: View mode and orientation
        mode_row = QHBoxLayout()

        # View mode selection
        mode_group = QGroupBox("View Mode")
        mode_layout = QHBoxLayout(mode_group)

        self.slice_btn = QPushButton("📊 Slice")
        self.slab_btn = QPushButton("📚 Slab")
        self.volume_btn = QPushButton("🧊 3D Volume")

        for btn in [self.slice_btn, self.slab_btn, self.volume_btn]:
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 80px;
                    border: 1px solid #ced4da;
                }
                QPushButton:checked {
                    background-color: #007bff;
                    color: white;
                    border-color: #007bff;
                }
                QPushButton:hover:!checked {
                    background-color: #e9ecef;
                }
            """)
            mode_layout.addWidget(btn)

        self.slice_btn.setChecked(True)
        self.slice_btn.clicked.connect(lambda: self.set_view_mode('slice'))
        self.slab_btn.clicked.connect(lambda: self.set_view_mode('slab'))
        self.volume_btn.clicked.connect(lambda: self.set_view_mode('3d'))

        mode_row.addWidget(mode_group)

        # Orientation selection
        orient_group = QGroupBox("Orientation")
        orient_layout = QHBoxLayout(orient_group)

        self.xy_btn = QPushButton("XY")
        self.xz_btn = QPushButton("XZ")
        self.yz_btn = QPushButton("YZ")

        for btn in [self.xy_btn, self.xz_btn, self.yz_btn]:
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 40px;
                    border: 1px solid #ced4da;
                }
                QPushButton:checked {
                    background-color: #28a745;
                    color: white;
                    border-color: #28a745;
                }
                QPushButton:hover:!checked {
                    background-color: #e9ecef;
                }
            """)
            orient_layout.addWidget(btn)

        self.xy_btn.setChecked(True)
        self.xy_btn.clicked.connect(lambda: self.set_orientation('xy'))
        self.xz_btn.clicked.connect(lambda: self.set_orientation('xz'))
        self.yz_btn.clicked.connect(lambda: self.set_orientation('yz'))

        mode_row.addWidget(orient_group)
        mode_row.addStretch()

        controls_layout.addLayout(mode_row)

        # Second row: Navigation and parameters
        nav_row = QHBoxLayout()

        # Slice/Slab navigation
        nav_row.addWidget(QLabel("Position:"))

        self.slice_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.update_position)
        self.slice_slider.setMinimumWidth(200)
        nav_row.addWidget(self.slice_slider)

        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(0)
        self.slice_spinbox.valueChanged.connect(self.update_position_from_spinbox)
        self.slice_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 4px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        nav_row.addWidget(self.slice_spinbox)

        self.total_label = QLabel("/ 0")
        self.total_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        nav_row.addWidget(self.total_label)

        # Slab thickness (shown when in slab mode)
        nav_row.addWidget(QLabel("  Thickness:"))
        self.thickness_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.thickness_slider.setRange(1, 20)
        self.thickness_slider.setValue(5)
        self.thickness_slider.valueChanged.connect(self.update_slab_thickness)
        self.thickness_slider.setMaximumWidth(100)
        nav_row.addWidget(self.thickness_slider)

        self.thickness_label = QLabel("5")
        self.thickness_label.setMinimumWidth(20)
        nav_row.addWidget(self.thickness_label)

        nav_row.addStretch()
        controls_layout.addLayout(nav_row)

        # Third row: 3D parameters (shown when in 3D mode)
        volume_row = QHBoxLayout()

        volume_row.addWidget(QLabel("Iso-value:"))
        self.iso_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.iso_slider.setRange(0, 100)
        self.iso_slider.setValue(50)
        self.iso_slider.valueChanged.connect(self.update_iso_value)
        self.iso_slider.setMaximumWidth(150)
        volume_row.addWidget(self.iso_slider)

        self.iso_label = QLabel("0.50")
        self.iso_label.setMinimumWidth(40)
        volume_row.addWidget(self.iso_label)

        volume_row.addWidget(QLabel("  Opacity:"))
        self.alpha_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.alpha_slider.setRange(10, 100)
        self.alpha_slider.setValue(70)
        self.alpha_slider.valueChanged.connect(self.update_alpha)
        self.alpha_slider.setMaximumWidth(150)
        volume_row.addWidget(self.alpha_slider)

        self.alpha_label = QLabel("0.70")
        self.alpha_label.setMinimumWidth(40)
        volume_row.addWidget(self.alpha_label)

        self.render_btn = QPushButton("🔄 Render")
        self.render_btn.clicked.connect(self.update_display)
        self.render_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        volume_row.addWidget(self.render_btn)
        volume_row.addStretch()

        # Store volume controls for show/hide
        self.volume_controls = [
            self.iso_slider, self.iso_label, self.alpha_slider,
            self.alpha_label, self.render_btn
        ]

        controls_layout.addLayout(volume_row)
        layout.addWidget(controls_frame)

        # Matplotlib figure for both 2D and 3D
        self.figure = Figure(figsize=(10, 8))
        self.figure.patch.set_facecolor('#ffffff')
        self.canvas = FigureCanvas(self.figure)

        # Will be set based on view mode
        self.ax_2d = None
        self.ax_3d = None

        layout.addWidget(self.canvas)

        # Initialize display mode
        self.update_control_visibility()

    def set_view_mode(self, mode):
        """Set the viewing mode: slice, slab, or 3d."""
        # Update button states
        self.slice_btn.setChecked(mode == 'slice')
        self.slab_btn.setChecked(mode == 'slab')
        self.volume_btn.setChecked(mode == '3d')

        self.view_mode = mode
        self.update_control_visibility()
        self.update_display()

    def set_orientation(self, orientation):
        """Set the viewing orientation: xy, xz, or yz."""
        # Update button states
        self.xy_btn.setChecked(orientation == 'xy')
        self.xz_btn.setChecked(orientation == 'xz')
        self.yz_btn.setChecked(orientation == 'yz')

        self.current_view = orientation
        self.update_slice_range()
        self.update_display()

    def update_control_visibility(self):
        """Update visibility of controls based on view mode."""
        # Slab thickness controls
        slab_controls = [self.thickness_slider, self.thickness_label]
        for control in slab_controls:
            control.setVisible(self.view_mode == 'slab')

        # 3D volume controls
        for control in self.volume_controls:
            control.setVisible(self.view_mode == '3d')

        # Update labels
        if self.view_mode == 'slice':
            self.slice_slider.setToolTip("Navigate through individual slices")
        elif self.view_mode == 'slab':
            self.slice_slider.setToolTip("Navigate through slab positions")
        else:
            self.slice_slider.setToolTip("Not applicable in 3D mode")

    def update_position(self, value):
        """Update current position (slice or slab center)."""
        self.current_slice = value
        self.slice_spinbox.blockSignals(True)
        self.slice_spinbox.setValue(value)
        self.slice_spinbox.blockSignals(False)
        self.update_display()

    def update_position_from_spinbox(self, value):
        """Update position from spinbox."""
        self.current_slice = value
        self.slice_slider.blockSignals(True)
        self.slice_slider.setValue(value)
        self.slice_slider.blockSignals(False)
        self.update_display()

    def update_slab_thickness(self, value):
        """Update slab thickness."""
        self.slab_thickness = value
        self.thickness_label.setText(str(value))
        if self.view_mode == 'slab':
            self.update_display()

    def update_iso_value(self, value):
        """Update iso-value for 3D rendering."""
        self.iso_value = value / 100.0
        self.iso_label.setText(f"{self.iso_value:.2f}")

    def update_alpha(self, value):
        """Update alpha (opacity) for 3D rendering."""
        self.alpha = value / 100.0
        self.alpha_label.setText(f"{self.alpha:.2f}")

    def set_volume(self, volume):
        """Set the volume data."""
        self.volume = volume
        if volume is not None:
            self.update_slice_range()
            # Auto-adjust iso-value based on data range
            data_min = np.min(volume)
            data_max = np.max(volume)
            if data_max > data_min:
                mid_value = (data_min + data_max) / 2
                iso_percent = int(((mid_value - data_min) / (data_max - data_min)) * 100)
                self.iso_slider.setValue(iso_percent)
                self.update_iso_value(iso_percent)
            self.update_display()

    def update_slice_range(self):
        """Update slice range based on current orientation and volume."""
        if self.volume is None:
            return

        if self.current_view == 'xy':
            max_slice = self.volume.shape[0] - 1
        elif self.current_view == 'xz':
            max_slice = self.volume.shape[1] - 1
        else:  # yz
            max_slice = self.volume.shape[2] - 1

        self.slice_slider.setMaximum(max_slice)
        self.slice_spinbox.setMaximum(max_slice)
        self.total_label.setText(f"/ {max_slice}")

        # Reset to middle slice
        middle_slice = max_slice // 2
        self.current_slice = middle_slice
        self.slice_slider.setValue(middle_slice)
        self.slice_spinbox.setValue(middle_slice)

    def update_display(self):
        """Update the display based on current view mode."""
        if self.volume is None:
            return

        # Clear previous plot
        self.figure.clear()

        if self.view_mode == '3d':
            self.render_3d_volume()
        else:
            self.render_2d_view()

        self.canvas.draw()

    def render_2d_view(self):
        """Render 2D slice or slab view."""
        self.ax_2d = self.figure.add_subplot(111)

        if self.view_mode == 'slice':
            # Single slice
            slice_data = self.get_slice_data()
            if slice_data is not None:
                im = self.ax_2d.imshow(slice_data, cmap=self.current_cmap,
                                     vmin=self.contrast_limits[0],
                                     vmax=self.contrast_limits[1])
                self.ax_2d.set_title(f'{self.current_view.upper()} Slice {self.current_slice}')

        elif self.view_mode == 'slab':
            # Slab (thick slice) - average or maximum intensity projection
            slab_data = self.get_slab_data()
            if slab_data is not None:
                im = self.ax_2d.imshow(slab_data, cmap=self.current_cmap,
                                     vmin=self.contrast_limits[0],
                                     vmax=self.contrast_limits[1])
                self.ax_2d.set_title(f'{self.current_view.upper()} Slab (thickness: {self.slab_thickness})')

        # Style the axes
        self.ax_2d.set_facecolor('#f8f9fa')
        for spine in self.ax_2d.spines.values():
            spine.set_color('#dee2e6')
            spine.set_linewidth(1)

    def get_slice_data(self):
        """Get data for a single slice."""
        if self.volume is None:
            return None

        try:
            if self.current_view == 'xy':
                return self.volume[self.current_slice, :, :]
            elif self.current_view == 'xz':
                return self.volume[:, self.current_slice, :]
            else:  # yz
                return self.volume[:, :, self.current_slice]
        except IndexError:
            return None

    def get_slab_data(self):
        """Get data for a slab (thick slice)."""
        if self.volume is None:
            return None

        try:
            half_thickness = self.slab_thickness // 2
            start_idx = max(0, self.current_slice - half_thickness)

            if self.current_view == 'xy':
                end_idx = min(self.volume.shape[0], self.current_slice + half_thickness + 1)
                slab = self.volume[start_idx:end_idx, :, :]
                return np.mean(slab, axis=0)  # Average projection
            elif self.current_view == 'xz':
                end_idx = min(self.volume.shape[1], self.current_slice + half_thickness + 1)
                slab = self.volume[:, start_idx:end_idx, :]
                return np.mean(slab, axis=1)
            else:  # yz
                end_idx = min(self.volume.shape[2], self.current_slice + half_thickness + 1)
                slab = self.volume[:, :, start_idx:end_idx]
                return np.mean(slab, axis=2)
        except IndexError:
            return None

    def render_3d_volume(self):
        """Render 3D volume using direct volume visualization (ArtiaX-inspired)."""
        try:
            self.ax_3d = self.figure.add_subplot(111, projection='3d')

            # Downsample for performance if volume is large
            volume = self.volume
            original_shape = volume.shape
            if volume.size > 128**3:
                step = max(1, int(np.cbrt(volume.size / 128**3)))
                volume = volume[::step, ::step, ::step]
                logger.info(f"Downsampled volume from {original_shape} to {volume.shape} for performance")

            # Normalize volume data
            data_min = np.min(volume)
            data_max = np.max(volume)
            if data_max > data_min:
                volume_norm = (volume - data_min) / (data_max - data_min)
            else:
                volume_norm = volume

            # Create direct volume visualization using multiple orthogonal slices
            # This approach is inspired by ArtiaX's volume rendering
            self.render_volume_slices(volume_norm)

        except Exception as e:
            logger.error(f"Error in 3D volume rendering: {e}")
            self.ax_2d = self.figure.add_subplot(111)
            self.ax_2d.text(0.5, 0.5, f"3D volume rendering error:\n{str(e)}",
                          ha='center', va='center', transform=self.ax_2d.transAxes, fontsize=12)
            self.ax_2d.set_xlim(0, 1)
            self.ax_2d.set_ylim(0, 1)

    def render_volume_slices(self, volume):
        """Render volume using orthogonal slices for direct volume visualization."""
        nz, ny, nx = volume.shape

        # Create coordinate grids
        x = np.arange(nx)
        y = np.arange(ny)
        z = np.arange(nz)

        # Apply intensity threshold for transparency
        threshold = self.iso_value

        # Sample multiple slices in each direction for volume effect
        num_slices = min(20, max(5, min(nx, ny, nz) // 4))  # Adaptive number of slices

        # XY slices (varying Z)
        z_indices = np.linspace(0, nz-1, num_slices, dtype=int)
        for i, zi in enumerate(z_indices):
            slice_data = volume[zi, :, :]

            # Apply threshold and alpha based on intensity
            mask = slice_data > threshold
            if np.any(mask):
                # Create meshgrid for this slice
                X, Y = np.meshgrid(x, y)
                Z = np.full_like(X, zi)

                # Apply colormap and alpha based on intensity
                colors = plt.cm.viridis(slice_data)
                colors[:, :, 3] = (slice_data - threshold) * self.alpha * mask  # Alpha based on intensity

                self.ax_3d.plot_surface(X, Y, Z, facecolors=colors,
                                      shade=False, alpha=self.alpha * 0.3)

        # XZ slices (varying Y) - fewer slices to avoid clutter
        y_indices = np.linspace(0, ny-1, max(3, num_slices//3), dtype=int)
        for i, yi in enumerate(y_indices):
            slice_data = volume[:, yi, :]

            mask = slice_data > threshold
            if np.any(mask):
                X, Z = np.meshgrid(x, z)
                Y = np.full_like(X, yi)

                colors = plt.cm.viridis(slice_data)
                colors[:, :, 3] = (slice_data - threshold) * self.alpha * 0.5 * mask

                self.ax_3d.plot_surface(X, Y, Z, facecolors=colors,
                                      shade=False, alpha=self.alpha * 0.2)

        # YZ slices (varying X) - fewer slices to avoid clutter
        x_indices = np.linspace(0, nx-1, max(3, num_slices//3), dtype=int)
        for i, xi in enumerate(x_indices):
            slice_data = volume[:, :, xi]

            mask = slice_data > threshold
            if np.any(mask):
                Y, Z = np.meshgrid(y, z)
                X = np.full_like(Y, xi)

                colors = plt.cm.viridis(slice_data)
                colors[:, :, 3] = (slice_data - threshold) * self.alpha * 0.5 * mask

                self.ax_3d.plot_surface(X, Y, Z, facecolors=colors,
                                      shade=False, alpha=self.alpha * 0.2)

        # Set equal aspect ratio and limits
        self.ax_3d.set_xlim(0, nx-1)
        self.ax_3d.set_ylim(0, ny-1)
        self.ax_3d.set_zlim(0, nz-1)

        # Labels and title
        self.ax_3d.set_xlabel('X')
        self.ax_3d.set_ylabel('Y')
        self.ax_3d.set_zlabel('Z')
        self.ax_3d.set_title(f'Direct Volume Visualization (Threshold: {self.iso_value:.2f}, α: {self.alpha:.2f})')

        # Set viewing angle for better 3D perception
        self.ax_3d.view_init(elev=20, azim=45)

        # Style the 3D plot
        self.ax_3d.set_facecolor('#f8f9fa')
        self.ax_3d.grid(True, alpha=0.3)

    def set_volume(self, volume):
        """Set the volume data for 3D visualization."""
        self.volume = volume
        if volume is not None:
            # Auto-adjust iso-value based on data range
            data_min = np.min(volume)
            data_max = np.max(volume)
            mid_value = (data_min + data_max) / 2
            # Set iso-value to middle of data range
            iso_percent = int(((mid_value - data_min) / (data_max - data_min)) * 100)
            self.iso_slider.setValue(iso_percent)
            self.update_iso_value(iso_percent)

    def render_3d(self):
        """Render 3D volume using marching cubes."""
        if self.volume is None:
            QMessageBox.warning(self, "No Data", "No volume loaded for 3D rendering.")
            return

        if not HAS_3D_SUPPORT:
            QMessageBox.warning(self, "3D Support",
                              "3D visualization requires scikit-image.\n"
                              "Install with: pip install scikit-image")
            return

        try:
            self.render_btn.setText("🔄 Rendering...")
            self.render_btn.setEnabled(False)

            # Clear previous plot
            self.ax_3d.clear()

            # Downsample for performance if volume is large
            volume = self.volume
            if volume.size > 64**3:
                # Downsample to manageable size
                step = max(1, int(np.cbrt(volume.size / 64**3)))
                volume = volume[::step, ::step, ::step]

            # Normalize volume data
            data_min = np.min(volume)
            data_max = np.max(volume)
            if data_max > data_min:
                volume_norm = (volume - data_min) / (data_max - data_min)
            else:
                volume_norm = volume

            # Calculate iso-surface using marching cubes
            iso_val = self.iso_value
            try:
                verts, faces, _, _ = measure.marching_cubes(volume_norm, iso_val)

                # Create 3D mesh
                mesh = Poly3DCollection(verts[faces])
                mesh.set_alpha(self.alpha)
                mesh.set_facecolor('lightblue')
                mesh.set_edgecolor('darkblue')
                mesh.set_linewidth(0.1)

                self.ax_3d.add_collection3d(mesh)

                # Set equal aspect ratio and limits
                max_range = np.array([verts[:,0].max()-verts[:,0].min(),
                                    verts[:,1].max()-verts[:,1].min(),
                                    verts[:,2].max()-verts[:,2].min()]).max() / 2.0

                mid_x = (verts[:,0].max()+verts[:,0].min()) * 0.5
                mid_y = (verts[:,1].max()+verts[:,1].min()) * 0.5
                mid_z = (verts[:,2].max()+verts[:,2].min()) * 0.5

                self.ax_3d.set_xlim(mid_x - max_range, mid_x + max_range)
                self.ax_3d.set_ylim(mid_y - max_range, mid_y + max_range)
                self.ax_3d.set_zlim(mid_z - max_range, mid_z + max_range)

                # Labels and title
                self.ax_3d.set_xlabel('X')
                self.ax_3d.set_ylabel('Y')
                self.ax_3d.set_zlabel('Z')
                self.ax_3d.set_title(f'3D Volume Rendering (Iso-value: {iso_val:.2f})')

                # Set viewing angle
                self.ax_3d.view_init(elev=20, azim=45)

                self.canvas_3d.draw()

            except ValueError as e:
                if "No surface found" in str(e):
                    QMessageBox.information(self, "No Surface",
                                          f"No surface found at iso-value {iso_val:.2f}.\n"
                                          "Try adjusting the iso-value slider.")
                else:
                    raise e

        except Exception as e:
            logger.error(f"Error in 3D rendering: {e}")
            QMessageBox.critical(self, "3D Rendering Error", f"Failed to render 3D volume:\n{str(e)}")
        finally:
            self.render_btn.setText("🔄 Render 3D")
            self.render_btn.setEnabled(True)

class MRCViewer(QWidget):
    """Enhanced MRC Viewer with 2D/3D modes and integrated sliders."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Core data
        self.volume = None
        self.current_slice = 0
        self.current_file = None
        self.current_view = 'xy'
        self.current_cmap = 'gray'
        self.view_mode = '2D'  # '2D' or '3D'

        # Display properties
        self.zoom_factor = 1.0
        self.contrast_limits = [0, 255]
        self.auto_contrast = True

        # Measurement state
        self.measurement_mode = None
        self.measurement_points = []

        # Layer system (napari-inspired)
        self.layers = {}
        self.active_layer = None

        self.setup_ui()

    def setup_ui(self):
        """Set up the enhanced UI with 2D/3D mode switching."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(8)

        # Create enhanced toolbar with 2D/3D mode switching
        self.create_toolbar(main_layout)

        # Create main content area with splitter
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Viewer area with mode switching
        viewer_area = self.create_viewer_area()
        content_splitter.addWidget(viewer_area)

        # Right side - Control panel
        control_panel = self.create_control_panel()
        content_splitter.addWidget(control_panel)

        # Set splitter proportions (80% viewer, 20% controls)
        content_splitter.setStretchFactor(0, 8)
        content_splitter.setStretchFactor(1, 2)
        content_splitter.setSizes([800, 200])

        main_layout.addWidget(content_splitter)

        # Initialize state
        self.im = None

    def create_toolbar(self, layout):
        """Create enhanced toolbar with 2D/3D mode switching."""
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 4px;
                spacing: 8px;
            }
            QToolBar::separator {
                background: #adb5bd;
                width: 1px;
                margin: 4px 8px;
            }
        """)

        # File operations with icons
        open_action = QAction("📁 Open MRC", self)
        open_action.setToolTip("Open MRC file (Ctrl+O)")
        open_action.triggered.connect(self.open_mrc)
        toolbar.addAction(open_action)

        save_action = QAction("💾 Save As", self)
        save_action.setToolTip("Save current view")
        save_action.triggered.connect(self.save_mrc)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # 2D/3D Mode switching buttons
        self.mode_2d_action = QAction("📊 2D Mode", self)
        self.mode_2d_action.setCheckable(True)
        self.mode_2d_action.setChecked(True)
        self.mode_2d_action.setToolTip("Switch to 2D slice viewing")
        self.mode_2d_action.triggered.connect(lambda: self.switch_mode('2D'))
        toolbar.addAction(self.mode_2d_action)

        self.mode_3d_action = QAction("🧊 3D Mode", self)
        self.mode_3d_action.setCheckable(True)
        self.mode_3d_action.setToolTip("Switch to 3D volume rendering")
        self.mode_3d_action.triggered.connect(lambda: self.switch_mode('3D'))
        toolbar.addAction(self.mode_3d_action)

        # Create button group for exclusive selection
        self.mode_group = QButtonGroup()
        self.mode_group.addButton(toolbar.widgetForAction(self.mode_2d_action) if toolbar.widgetForAction(self.mode_2d_action) else QPushButton())
        self.mode_group.addButton(toolbar.widgetForAction(self.mode_3d_action) if toolbar.widgetForAction(self.mode_3d_action) else QPushButton())

        toolbar.addSeparator()

        # View controls
        reset_action = QAction("🔄 Reset View", self)
        reset_action.setToolTip("Reset zoom and pan")
        reset_action.triggered.connect(self.reset_view)
        toolbar.addAction(reset_action)

        auto_contrast_action = QAction("🎨 Auto Contrast", self)
        auto_contrast_action.setToolTip("Auto-adjust contrast")
        auto_contrast_action.triggered.connect(self.apply_auto_contrast)
        toolbar.addAction(auto_contrast_action)

        layout.addWidget(toolbar)

    def create_viewer_area(self):
        """Create the integrated viewer area with slice/slab/3D modes."""
        # Use the new IntegratedViewer instead of separate tabs
        self.integrated_viewer = IntegratedViewer()
        return self.integrated_viewer

    def create_2d_viewer(self):
        """Create the 2D viewer widget with integrated slice controls."""
        viewer_widget = QWidget()
        viewer_layout = QVBoxLayout(viewer_widget)
        viewer_layout.setContentsMargins(0, 0, 0, 0)
        viewer_layout.setSpacing(5)

        # Integrated slice navigation at top
        slice_nav = self.create_integrated_slice_navigation()
        viewer_layout.addWidget(slice_nav)

        # Create matplotlib figure with elegant styling
        self.figure = Figure(figsize=(10, 8))
        self.figure.patch.set_facecolor('#ffffff')

        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # Style the axes
        self.ax.set_facecolor('#f8f9fa')
        for spine in self.ax.spines.values():
            spine.set_color('#dee2e6')
            spine.set_linewidth(1)

        # Add custom navigation toolbar
        self.nav_toolbar = NavigationToolbar2QT(self.canvas, self)
        self.nav_toolbar.setStyleSheet("""
            QToolBar {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 2px;
            }
        """)

        viewer_layout.addWidget(self.nav_toolbar)
        viewer_layout.addWidget(self.canvas)

        return viewer_widget

    def create_integrated_slice_navigation(self):
        """Create integrated slice navigation controls with better styling."""
        nav_frame = QFrame()
        nav_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        nav_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(248, 249, 250, 0.95);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        nav_layout = QHBoxLayout(nav_frame)
        nav_layout.setContentsMargins(10, 8, 10, 8)

        # View direction buttons
        view_group = QGroupBox("View Direction")
        view_layout = QHBoxLayout(view_group)

        self.xy_button = QPushButton("XY")
        self.xz_button = QPushButton("XZ")
        self.yz_button = QPushButton("YZ")

        for btn in [self.xy_button, self.xz_button, self.yz_button]:
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-weight: 500;
                    min-width: 40px;
                }
                QPushButton:checked {
                    background-color: #007bff;
                    color: white;
                }
            """)
            view_layout.addWidget(btn)

        self.xy_button.setChecked(True)
        self.xy_button.clicked.connect(lambda: self.change_view('xy'))
        self.xz_button.clicked.connect(lambda: self.change_view('xz'))
        self.yz_button.clicked.connect(lambda: self.change_view('yz'))

        nav_layout.addWidget(view_group)

        # Slice controls
        nav_layout.addWidget(QLabel("Slice:"))

        # Elegant slice slider
        self.slice_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.update_slice)
        self.slice_slider.setMinimumWidth(200)
        nav_layout.addWidget(self.slice_slider)

        # Slice spinbox
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(0)
        self.slice_spinbox.valueChanged.connect(self.update_slice_from_spinbox)
        self.slice_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 4px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        nav_layout.addWidget(self.slice_spinbox)

        # Total slices label
        self.total_slices_label = QLabel("/ 0")
        self.total_slices_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        nav_layout.addWidget(self.total_slices_label)

        nav_layout.addStretch()
        return nav_frame

    def switch_mode(self, mode):
        """Switch between 2D and 3D viewing modes."""
        self.view_mode = mode

        # Update toolbar button states
        self.mode_2d_action.setChecked(mode == '2D')
        self.mode_3d_action.setChecked(mode == '3D')

        # Switch to appropriate tab
        if mode == '2D':
            self.viewer_tabs.setCurrentIndex(0)
        else:
            self.viewer_tabs.setCurrentIndex(1)
            # Update 3D viewer with current volume
            if self.volume is not None:
                self.viewer_3d.set_volume(self.volume)

    def on_tab_changed(self, index):
        """Handle tab changes to update mode."""
        if index == 0:
            self.switch_mode('2D')
        else:
            self.switch_mode('3D')

    def create_slice_navigation(self):
        """Create elegant slice navigation controls."""
        nav_widget = QWidget()
        nav_layout = QHBoxLayout(nav_widget)
        nav_layout.setContentsMargins(10, 5, 10, 5)

        # Slice label
        nav_layout.addWidget(QLabel("Slice:"))

        # Elegant slice slider
        self.slice_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.update_slice)
        nav_layout.addWidget(self.slice_slider)

        # Slice spinbox
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(0)
        self.slice_spinbox.valueChanged.connect(self.update_slice_from_spinbox)
        self.slice_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 4px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        nav_layout.addWidget(self.slice_spinbox)

        # Total slices label
        self.total_slices_label = QLabel("/ 0")
        self.total_slices_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        nav_layout.addWidget(self.total_slices_label)

        return nav_widget

    def create_control_panel(self):
        """Create the right-side control panel."""
        panel = QWidget()
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(5, 5, 5, 5)
        panel_layout.setSpacing(10)

        # Create tabbed control panel
        control_tabs = QTabWidget()
        control_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background: #ffffff;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: #ffffff;
                border-bottom: none;
            }
        """)

        # View Controls Tab
        view_tab = self.create_view_controls()
        control_tabs.addTab(view_tab, "🔍 View")

        # Contrast Tab
        contrast_tab = self.create_contrast_controls()
        control_tabs.addTab(contrast_tab, "🎨 Contrast")

        # Measurements Tab
        measurement_tab = MeasurementTool()
        control_tabs.addTab(measurement_tab, "📏 Measure")

        # Info Tab
        info_tab = self.create_info_panel()
        control_tabs.addTab(info_tab, "ℹ️ Info")

        panel_layout.addWidget(control_tabs)

        return panel

    def create_view_controls(self):
        """Create view control widgets."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)

        # View direction controls
        view_group = QGroupBox("View Direction")
        view_layout = QGridLayout(view_group)

        self.xy_button = QPushButton("XY View")
        self.xz_button = QPushButton("XZ View")
        self.yz_button = QPushButton("YZ View")

        for i, btn in enumerate([self.xy_button, self.xz_button, self.yz_button]):
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-weight: 500;
                    min-height: 30px;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                    color: white;
                }
            """)
            view_layout.addWidget(btn, i // 2, i % 2)

        self.xy_button.setChecked(True)
        self.xy_button.clicked.connect(lambda: self.change_view('xy'))
        self.xz_button.clicked.connect(lambda: self.change_view('xz'))
        self.yz_button.clicked.connect(lambda: self.change_view('yz'))

        layout.addWidget(view_group)

        # Zoom controls
        zoom_group = QGroupBox("Zoom")
        zoom_layout = QGridLayout(zoom_group)

        self.zoom_in_btn = QPushButton("🔍+ Zoom In")
        self.zoom_out_btn = QPushButton("🔍- Zoom Out")
        self.zoom_reset_btn = QPushButton("🔄 Reset")

        for btn in [self.zoom_in_btn, self.zoom_out_btn, self.zoom_reset_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 11px;
                }
            """)

        self.zoom_in_btn.clicked.connect(lambda: self.zoom(1.2))
        self.zoom_out_btn.clicked.connect(lambda: self.zoom(0.8))
        self.zoom_reset_btn.clicked.connect(self.reset_view)

        zoom_layout.addWidget(self.zoom_in_btn, 0, 0)
        zoom_layout.addWidget(self.zoom_out_btn, 0, 1)
        zoom_layout.addWidget(self.zoom_reset_btn, 1, 0, 1, 2)

        layout.addWidget(zoom_group)
        layout.addStretch()

        return widget

    def create_contrast_controls(self):
        """Create contrast control widgets."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)

        # Contrast adjustment
        contrast_group = QGroupBox("Contrast Limits")
        contrast_layout = QGridLayout(contrast_group)

        # Min/Max spinboxes
        self.vmin_spinbox = QSpinBox()
        self.vmin_spinbox.setRange(-32768, 32767)
        self.vmin_spinbox.valueChanged.connect(self.update_display)

        self.vmax_spinbox = QSpinBox()
        self.vmax_spinbox.setRange(-32768, 32767)
        self.vmax_spinbox.valueChanged.connect(self.update_display)

        contrast_layout.addWidget(QLabel("Min:"), 0, 0)
        contrast_layout.addWidget(self.vmin_spinbox, 0, 1)
        contrast_layout.addWidget(QLabel("Max:"), 1, 0)
        contrast_layout.addWidget(self.vmax_spinbox, 1, 1)

        # Auto contrast button
        self.auto_contrast_btn = QPushButton("🎨 Auto Contrast")
        self.auto_contrast_btn.clicked.connect(self.apply_auto_contrast)
        self.auto_contrast_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: 500;
                background-color: #4CAF50;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        contrast_layout.addWidget(self.auto_contrast_btn, 2, 0, 1, 2)

        layout.addWidget(contrast_group)

        # Colormap selection
        cmap_group = QGroupBox("Colormap")
        cmap_layout = QVBoxLayout(cmap_group)

        self.cmap_combo = QComboBox()
        self.cmap_combo.addItems([
            'gray', 'viridis', 'plasma', 'inferno', 'magma',
            'hot', 'cool', 'coolwarm', 'RdYlBu', 'jet'
        ])
        self.cmap_combo.setCurrentText('gray')
        self.cmap_combo.currentTextChanged.connect(self.change_colormap)
        self.cmap_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
            }
        """)
        cmap_layout.addWidget(self.cmap_combo)

        layout.addWidget(cmap_group)

        # Histogram
        histogram_group = QGroupBox("Histogram")
        histogram_layout = QVBoxLayout(histogram_group)

        self.histogram_widget = HistogramWidget()
        histogram_layout.addWidget(self.histogram_widget)

        layout.addWidget(histogram_group)
        layout.addStretch()

        return widget

    def create_info_panel(self):
        """Create information panel."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)

        # File info
        info_group = QGroupBox("File Information")
        info_layout = QVBoxLayout(info_group)

        self.info_label = QLabel("No file loaded")
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(self.info_label)

        layout.addWidget(info_group)

        # Statistics
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_label = QLabel("No data")
        self.stats_label.setWordWrap(True)
        self.stats_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
            }
        """)
        stats_layout.addWidget(self.stats_label)

        layout.addWidget(stats_group)
        layout.addStretch()

        return widget

    @property
    def view(self):
        """Property for test compatibility: returns the main matplotlib axes."""
        return getattr(self, 'ax', None)

    def zoom(self, factor):
        """Adjust zoom level"""
        self.zoom_factor *= factor
        self.update_display()

    def reset_view(self):
        """Reset zoom and pan"""
        self.zoom_factor = 1.0
        self.ax.set_xlim(auto=True)
        self.ax.set_ylim(auto=True)
        self.update_display()

    def change_colormap(self, cmap_name):
        """Change the colormap"""
        self.current_cmap = cmap_name
        self.update_display()

    def open_mrc(self):
        """Open an MRC file via file dialog"""
        filepath, _ = QFileDialog.getOpenFileName(
            self,
            "Open MRC File",
            "",
            "MRC Files (*.mrc *.mrcs *.rec);;All Files (*.*)"
        )

        if filepath:
            if self.load_mrc(filepath):
                self.current_file = filepath
                self.update_info_label()

    def save_mrc(self):
        """Save current volume to a new MRC file"""
        if self.volume is None:
            QMessageBox.warning(self, "No Data", "No volume loaded to save.")
            return

        filepath, _ = QFileDialog.getSaveFileName(
            self,
            "Save MRC File",
            "",
            "MRC Files (*.mrc);;All Files (*.*)"
        )

        if filepath:
            try:
                with mrcfile.new(filepath, overwrite=True) as mrc:
                    mrc.set_data(self.volume)
                logger.info(f"Successfully saved MRC file to {filepath}")
                QMessageBox.information(self, "Success", "File saved successfully.")
            except Exception as e:
                logger.error(f"Error saving MRC file: {e}")
                QMessageBox.critical(self, "Error", f"Failed to save file:\n{str(e)}")

    def load_mrc(self, filepath):
        """Load an MRC file and display its first slice"""
        try:
            with mrcfile.open(filepath, permissive=True) as mrc:
                self.volume = mrc.data.copy()

            logger.info(f"Loaded MRC file: {filepath}")
            logger.debug(f"Volume shape: {self.volume.shape}, dtype: {self.volume.dtype}")

            # Validate the loaded data
            if self.volume is None:
                raise ValueError("No data found in MRC file")

            if self.volume.size == 0:
                raise ValueError("Empty data in MRC file")

            # Check for problematic data
            if np.all(np.isnan(self.volume)):
                raise ValueError("All data is NaN")

            if np.all(np.isinf(self.volume)):
                raise ValueError("All data is infinite")

            # Store current file path
            self.current_file = filepath

            self.update_slice_range()
            self.current_slice = 0

            # Update integrated viewer controls if available
            if hasattr(self, 'integrated_viewer') and self.integrated_viewer:
                self.integrated_viewer.position_slider.setValue(0)
                self.integrated_viewer.position_spinbox.setValue(0)

            # Update legacy controls if they exist
            if hasattr(self, 'slice_slider') and self.slice_slider:
                self.slice_slider.setValue(0)
            if hasattr(self, 'slice_spinbox') and self.slice_spinbox:
                self.slice_spinbox.setValue(0)

            self.apply_auto_contrast()  # Set initial contrast automatically
            self.update_info_label()

            # Update integrated viewer with volume data
            if hasattr(self, 'integrated_viewer') and self.integrated_viewer:
                self.integrated_viewer.set_volume(self.volume)

            return True
        except Exception as e:
            logger.error(f"Error loading MRC file: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load file:\n{str(e)}")
            return False

    def apply_auto_contrast(self):
        """Automatically set contrast based on data percentiles"""
        if self.volume is not None:
            try:
                # Use current slice for better contrast
                current_slice_data = self.get_current_slice_data()
                if current_slice_data is not None:
                    # Remove NaN and infinite values
                    valid_data = current_slice_data[np.isfinite(current_slice_data)]
                    if len(valid_data) > 0:
                        vmin = np.percentile(valid_data, 1)
                        vmax = np.percentile(valid_data, 99)

                        # Ensure vmin < vmax
                        if vmin >= vmax:
                            data_min = np.min(valid_data)
                            data_max = np.max(valid_data)
                            if data_min == data_max:
                                vmin = data_min - 0.5
                                vmax = data_max + 0.5
                            else:
                                vmin = data_min
                                vmax = data_max

                        self.vmin_spinbox.setValue(int(vmin))
                        self.vmax_spinbox.setValue(int(vmax))
                        self.update_display()
                    else:
                        logger.warning("No valid data found for auto-contrast")
                else:
                    logger.warning("No slice data available for auto-contrast")
            except Exception as e:
                logger.error(f"Error in auto-contrast: {e}")
                # Fallback to simple min/max
                try:
                    vmin = float(np.min(self.volume))
                    vmax = float(np.max(self.volume))
                    if vmin < vmax:
                        self.vmin_spinbox.setValue(int(vmin))
                        self.vmax_spinbox.setValue(int(vmax))
                        self.update_display()
                except Exception:
                    logger.error("Failed to apply any contrast adjustment")

    def update_info_label(self):
        """Update the information labels with current file details"""
        if self.volume is not None:
            # File information
            file_info = f"📁 File: {os.path.basename(self.current_file) if self.current_file else 'Unknown'}\n"
            file_info += f"📐 Dimensions: {self.volume.shape}\n"
            file_info += f"🔢 Data Type: {self.volume.dtype}\n"
            file_info += f"💾 Size: {self.volume.nbytes / (1024**2):.1f} MB\n"
            file_info += f"👁️ Current View: {self.current_view.upper()}\n"
            # Get max slices from volume or integrated viewer
            max_slices = 1
            if self.volume is not None:
                if self.current_view == 'xy':
                    max_slices = self.volume.shape[0]
                elif self.current_view == 'xz':
                    max_slices = self.volume.shape[1]
                else:  # yz
                    max_slices = self.volume.shape[2]

            file_info += f"🎯 Slice: {self.current_slice + 1} / {max_slices}"

            self.info_label.setText(file_info)

            # Statistics
            current_slice_data = self.get_current_slice_data()
            if current_slice_data is not None:
                stats_info = f"📊 Current Slice Statistics:\n"
                stats_info += f"Min: {np.min(current_slice_data):.2f}\n"
                stats_info += f"Max: {np.max(current_slice_data):.2f}\n"
                stats_info += f"Mean: {np.mean(current_slice_data):.2f}\n"
                stats_info += f"Std: {np.std(current_slice_data):.2f}\n"
                stats_info += f"Median: {np.median(current_slice_data):.2f}"

                self.stats_label.setText(stats_info)

                # Update histogram
                self.histogram_widget.update_histogram(current_slice_data)
        else:
            self.info_label.setText("No file loaded")
            self.stats_label.setText("No data")

    def get_current_slice_data(self):
        """Get the current slice data based on view direction."""
        if self.volume is None:
            return None

        if self.current_view == 'xy':
            return self.volume[self.current_slice, :, :]
        elif self.current_view == 'xz':
            return self.volume[:, self.current_slice, :]
        else:  # yz
            return self.volume[:, :, self.current_slice]

    def update_slice_range(self):
        """Update the range of the slice slider based on volume dimensions"""
        if self.volume is not None:
            max_slice = self.volume.shape[0] - 1 if self.current_view == 'xy' else \
                       self.volume.shape[1] - 1 if self.current_view == 'xz' else \
                       self.volume.shape[2] - 1

            # Update integrated viewer controls if available
            if hasattr(self, 'integrated_viewer') and self.integrated_viewer:
                self.integrated_viewer.position_slider.setMaximum(max_slice)
                self.integrated_viewer.position_spinbox.setMaximum(max_slice)

            # Update legacy controls if they exist
            if hasattr(self, 'slice_slider') and self.slice_slider:
                self.slice_slider.setMaximum(max_slice)
            if hasattr(self, 'slice_spinbox') and self.slice_spinbox:
                self.slice_spinbox.setMaximum(max_slice)

            # Update total slices label if it exists
            if hasattr(self, 'total_slices_label') and self.total_slices_label:
                self.total_slices_label.setText(f"/ {max_slice + 1}")

            # Update info display
            self.update_info_label()

    def update_slice(self, value):
        """Update the displayed slice when slider changes"""
        self.current_slice = value

        # Update integrated viewer controls if available
        if hasattr(self, 'integrated_viewer') and self.integrated_viewer:
            self.integrated_viewer.position_spinbox.setValue(value)

        # Update legacy controls if they exist
        if hasattr(self, 'slice_spinbox') and self.slice_spinbox:
            self.slice_spinbox.setValue(value)

        self.update_display()

    def update_slice_from_spinbox(self, value):
        """Update the displayed slice when spinbox changes"""
        self.current_slice = value

        # Update integrated viewer controls if available
        if hasattr(self, 'integrated_viewer') and self.integrated_viewer:
            self.integrated_viewer.position_slider.setValue(value)

        # Update legacy controls if they exist
        if hasattr(self, 'slice_slider') and self.slice_slider:
            self.slice_slider.setValue(value)

        self.update_display()

    def change_view(self, view):
        """Change the viewing direction (xy, xz, or yz)"""
        self.current_view = view

        # Update button states if they exist
        if hasattr(self, 'xy_button') and self.xy_button:
            self.xy_button.setChecked(view == 'xy')
        if hasattr(self, 'xz_button') and self.xz_button:
            self.xz_button.setChecked(view == 'xz')
        if hasattr(self, 'yz_button') and self.yz_button:
            self.yz_button.setChecked(view == 'yz')

        self.update_slice_range()

        # Get maximum slice for current view
        max_slice = 0
        if self.volume is not None:
            max_slice = self.volume.shape[0] - 1 if view == 'xy' else \
                       self.volume.shape[1] - 1 if view == 'xz' else \
                       self.volume.shape[2] - 1

        self.current_slice = min(self.current_slice, max_slice)

        # Update integrated viewer controls if available
        if hasattr(self, 'integrated_viewer') and self.integrated_viewer:
            self.integrated_viewer.position_slider.setValue(self.current_slice)

        # Update legacy controls if they exist
        if hasattr(self, 'slice_slider') and self.slice_slider:
            self.slice_slider.setValue(self.current_slice)

        self.update_display()

    def update_display(self):
        """Update the displayed image with elegant styling."""
        if self.volume is None:
            return

        # Get the appropriate slice based on view direction
        slice_data = self.get_current_slice_data()
        if slice_data is None:
            return

        # Clear the current axis
        self.ax.clear()

        # Get and validate contrast limits
        vmin = self.vmin_spinbox.value()
        vmax = self.vmax_spinbox.value()

        # Ensure vmin < vmax, if not, use data range
        if vmin >= vmax:
            logger.warning(f"Invalid contrast limits: vmin={vmin}, vmax={vmax}. Using data range.")
            data_min = np.min(slice_data)
            data_max = np.max(slice_data)
            if data_min == data_max:
                # Handle constant data
                vmin = data_min - 0.5
                vmax = data_max + 0.5
            else:
                vmin = data_min
                vmax = data_max
            # Update spinboxes with corrected values
            self.vmin_spinbox.setValue(int(vmin))
            self.vmax_spinbox.setValue(int(vmax))

        # Display the new slice with current colormap
        try:
            self.im = self.ax.imshow(slice_data,
                                    cmap=self.current_cmap,
                                    vmin=vmin,
                                    vmax=vmax,
                                    interpolation='nearest',
                                    aspect='equal')
        except ValueError as e:
            logger.error(f"Error displaying image: {e}")
            # Fallback to auto-scaling
            self.im = self.ax.imshow(slice_data,
                                    cmap=self.current_cmap,
                                    interpolation='nearest',
                                    aspect='equal')

        # Style the axes with elegant appearance
        self.ax.set_title(f"{self.current_view.upper()} View - Slice {self.current_slice + 1}",
                         fontsize=12, fontweight='bold', pad=10)

        # Add scale information
        if hasattr(self, 'pixel_size'):
            self.ax.set_xlabel(f"X (pixels)", fontsize=10)
            self.ax.set_ylabel(f"Y (pixels)", fontsize=10)
        else:
            self.ax.set_xlabel("X", fontsize=10)
            self.ax.set_ylabel("Y", fontsize=10)

        # Apply zoom with smooth transitions
        if hasattr(self, 'zoom_factor'):
            xmin, xmax = self.ax.get_xlim()
            ymin, ymax = self.ax.get_ylim()
            xcenter = (xmax + xmin) / 2
            ycenter = (ymax + ymin) / 2
            width = (xmax - xmin) / self.zoom_factor
            height = (ymax - ymin) / self.zoom_factor
            self.ax.set_xlim(xcenter - width/2, xcenter + width/2)
            self.ax.set_ylim(ycenter - height/2, ycenter + height/2)

        # Style the plot
        self.ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)

        # Update the canvas
        self.canvas.draw()

        # Update information panels
        self.update_info_label()
