#!/usr/bin/env python3
"""
Embedded Napari MRC viewer for AreTomo3 GUI.
Integrates <PERSON><PERSON><PERSON> directly as a widget within the GUI.
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any
import warnings

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QPushButton, QFileDialog, QMessageBox, QTextEdit,
    QCheckBox, QSpinBox, QSlider, QComboBox, QFrame, QSplitter
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap, QIcon
import numpy as np
import mrcfile

# Napari imports with error handling
try:
    import napari
    from napari.qt import QtViewer
    NAPARI_AVAILABLE = True
except ImportError as e:
    NAPARI_AVAILABLE = False
    napari = None
    QtViewer = None
    warnings.warn(f"Napari not available: {e}")

logger = logging.getLogger(__name__)

class EmbeddedNapariWidget(QWidget):
    """Embedded Napari viewer widget."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.napari_viewer = None
        self.napari_widget = None
        self.setup_napari()

    def setup_napari(self):
        """Setup the embedded Napari viewer."""
        if not NAPARI_AVAILABLE:
            # Create fallback widget
            layout = QVBoxLayout(self)
            error_label = QLabel("""
            <h3>⚠️ Napari Not Available</h3>
            <p>Napari is not installed or not compatible with this system.</p>
            <p>Please install napari:</p>
            <code>pip install napari[all]</code>
            """)
            error_label.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    padding: 20px;
                    color: #856404;
                }
            """)
            layout.addWidget(error_label)
            return

        try:
            # Create Napari viewer
            self.napari_viewer = napari.Viewer(show=False)

            # Get the Qt widget from Napari
            self.napari_widget = self.napari_viewer.window._qt_window

            # Embed in our layout
            layout = QVBoxLayout(self)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(self.napari_widget)

            logger.info("Embedded Napari viewer created successfully")

        except Exception as e:
            logger.error(f"Error creating embedded Napari viewer: {e}")
            # Create fallback widget
            layout = QVBoxLayout(self)
            error_label = QLabel(f"""
            <h3>⚠️ Napari Error</h3>
            <p>Failed to create Napari viewer:</p>
            <code>{str(e)}</code>
            """)
            error_label.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 8px;
                    padding: 20px;
                    color: #721c24;
                }
            """)
            layout.addWidget(error_label)

    def load_mrc_file(self, file_path: Path):
        """Load MRC file into Napari viewer."""
        if not self.napari_viewer:
            return False

        try:
            # Clear existing layers
            self.napari_viewer.layers.clear()

            # Load MRC file with memory mapping for efficiency
            with mrcfile.mmap(str(file_path), permissive=True) as mrc:
                data = mrc.data

                # Add to Napari viewer
                if len(data.shape) == 2:
                    # 2D image
                    self.napari_viewer.add_image(data, name=file_path.stem)
                elif len(data.shape) == 3:
                    # 3D volume or stack
                    self.napari_viewer.add_image(data, name=file_path.stem)
                else:
                    # Handle other dimensions
                    self.napari_viewer.add_image(data, name=file_path.stem)

                logger.info(f"Loaded MRC file into Napari: {file_path}")
                return True

        except Exception as e:
            logger.error(f"Error loading MRC file into Napari: {e}")
            return False

    def closeEvent(self, event):
        """Handle widget close event."""
        self.cleanup_napari()
        event.accept()

    def cleanup_napari(self):
        """Safely cleanup Napari viewer."""
        try:
            if self.napari_viewer:
                # Clear all layers first to free memory
                self.napari_viewer.layers.clear()
                # Close the viewer
                self.napari_viewer.close()
                self.napari_viewer = None
                logger.info("Napari viewer cleaned up successfully")
        except Exception as e:
            logger.warning(f"Error during Napari cleanup: {e}")

        try:
            if self.napari_widget:
                self.napari_widget.close()
                self.napari_widget = None
        except Exception as e:
            logger.warning(f"Error closing Napari widget: {e}")

class NapariMRCViewer(QWidget):
    """Napari-based MRC viewer widget for AreTomo3 GUI."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_file: Optional[Path] = None
        self.embedded_napari: Optional[EmbeddedNapariWidget] = None

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)

        # Create splitter for controls and viewer
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Controls
        controls_widget = QWidget()
        controls_layout = QVBoxLayout(controls_widget)
        controls_widget.setMaximumWidth(350)

        # Header with title and info
        header_group = QGroupBox("🔬 Embedded Napari Viewer")
        header_layout = QVBoxLayout(header_group)

        info_label = QLabel("""
        <b>Professional Scientific Imaging</b><br>
        • Embedded Napari viewer<br>
        • 3D volume visualization<br>
        • Interactive analysis tools<br>
        • MRC/MRCS/MAP support
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 6px;
                border: 1px solid #dee2e6;
                color: #495057;
                font-size: 11pt;
            }
        """)
        header_layout.addWidget(info_label)
        controls_layout.addWidget(header_group)

        # File selection and controls
        file_group = QGroupBox("📁 File Controls")
        file_layout = QVBoxLayout(file_group)

        # File selection
        self.file_label = QLabel("No file selected")
        self.file_label.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #ced4da;
                font-family: monospace;
                font-size: 10pt;
            }
        """)

        self.browse_button = QPushButton("📂 Browse MRC File")
        self.browse_button.clicked.connect(self.browse_file)
        self.browse_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 10px 15px;
                border-radius: 6px;
                border: none;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        file_layout.addWidget(QLabel("Selected File:"))
        file_layout.addWidget(self.file_label)
        file_layout.addWidget(self.browse_button)
        controls_layout.addWidget(file_group)

        # File information display
        info_group = QGroupBox("📊 File Information")
        info_layout = QVBoxLayout(info_group)

        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(120)
        self.info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 9pt;
                color: #495057;
            }
        """)
        self.info_text.setPlainText("Select an MRC file to view information...")

        info_layout.addWidget(self.info_text)
        controls_layout.addWidget(info_group)

        # Status
        self.status_label = QLabel("🔴 Ready - No file loaded")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
                background-color: #f8d7da;
                color: #721c24;
            }
        """)
        controls_layout.addWidget(self.status_label)

        # Add stretch to push controls to top
        controls_layout.addStretch()

        # Add controls to splitter
        splitter.addWidget(controls_widget)

        # Right side - Embedded Napari Viewer
        self.embedded_napari = EmbeddedNapariWidget()
        splitter.addWidget(self.embedded_napari)

        # Set splitter proportions (30% controls, 70% viewer)
        splitter.setStretchFactor(0, 3)
        splitter.setStretchFactor(1, 7)

        layout.addWidget(splitter)

    def browse_file(self):
        """Browse for MRC file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select MRC File",
            str(Path.home()),
            "MRC Files (*.mrc *.mrcs *.map *.MRC *.MRCS *.MAP);;All Files (*)"
        )

        if file_path:
            self.load_file(Path(file_path))

    def load_file(self, file_path: Path):
        """Load and analyze MRC file."""
        try:
            self.current_file = file_path
            self.file_label.setText(str(file_path.name))

            # Update status
            self.status_label.setText("🟡 Loading file...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 12pt;
                    font-weight: bold;
                    padding: 8px;
                    border-radius: 4px;
                    background-color: #fff3cd;
                    color: #856404;
                }
            """)

            # Analyze file and display info
            self.analyze_mrc_file(file_path)

            # Load into embedded Napari viewer
            if self.embedded_napari and self.embedded_napari.load_mrc_file(file_path):
                # Update status - success
                self.status_label.setText("🟢 File loaded in Napari viewer")
                self.status_label.setStyleSheet("""
                    QLabel {
                        font-size: 12pt;
                        font-weight: bold;
                        padding: 8px;
                        border-radius: 4px;
                        background-color: #d4edda;
                        color: #155724;
                    }
                """)
                logger.info(f"Successfully loaded MRC file: {file_path}")
            else:
                # Update status - error
                self.status_label.setText("🔴 Error loading file")
                self.status_label.setStyleSheet("""
                    QLabel {
                        font-size: 12pt;
                        font-weight: bold;
                        padding: 8px;
                        border-radius: 4px;
                        background-color: #f8d7da;
                        color: #721c24;
                    }
                """)

        except Exception as e:
            error_msg = f"Error loading file: {str(e)}"
            logger.error(error_msg)
            QMessageBox.critical(self, "Error", error_msg)

            # Update status - error
            self.status_label.setText("🔴 Error loading file")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 12pt;
                    font-weight: bold;
                    padding: 8px;
                    border-radius: 4px;
                    background-color: #f8d7da;
                    color: #721c24;
                }
            """)

    def analyze_mrc_file(self, file_path: Path):
        """Analyze MRC file and display information."""
        try:
            with mrcfile.open(file_path, permissive=True) as mrc:
                info_text = f"""📁 File: {file_path.name}
📏 Size: {file_path.stat().st_size / (1024*1024):.1f} MB
🔢 Shape: {mrc.data.shape}
📊 Data Type: {mrc.data.dtype}
🎯 Min: {np.min(mrc.data):.3f}
🎯 Max: {np.max(mrc.data):.3f}
📈 Mean: {np.mean(mrc.data):.3f}
📉 Std: {np.std(mrc.data):.3f}

📋 Header Info:
• Voxel Size: {mrc.voxel_size}
• Origin: {mrc.header.origin}
"""

                if len(mrc.data.shape) == 3:
                    info_text += f"🧊 3D Volume: {mrc.data.shape[2]} x {mrc.data.shape[1]} x {mrc.data.shape[0]}\n"
                elif len(mrc.data.shape) == 2:
                    info_text += f"🖼️ 2D Image: {mrc.data.shape[1]} x {mrc.data.shape[0]}\n"

                self.info_text.setPlainText(info_text)

        except Exception as e:
            self.info_text.setPlainText(f"Error analyzing file: {str(e)}")

    def closeEvent(self, event):
        """Handle widget close event."""
        try:
            if self.embedded_napari:
                self.embedded_napari.close()
        except Exception as e:
            logger.warning(f"Error closing embedded Napari: {e}")
        event.accept()

    def cleanup(self):
        """Cleanup resources before closing."""
        try:
            if self.embedded_napari and self.embedded_napari.napari_viewer:
                # Clear all layers first
                self.embedded_napari.napari_viewer.layers.clear()
                # Close the viewer
                self.embedded_napari.napari_viewer.close()
                self.embedded_napari.napari_viewer = None
        except Exception as e:
            logger.warning(f"Error during Napari cleanup: {e}")


