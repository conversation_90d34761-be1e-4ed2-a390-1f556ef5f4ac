#!/usr/bin/env python3
"""
Motion Correction Visualizer for AreTomo3 GUI
Similar to CTF visualizer implementation, displays motion corrected images and plots.
"""

import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QSlider, QComboBox, QCheckBox, QGroupBox, QSplitter,
    QTabWidget, QTextEdit, QProgressBar, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.patches as patches
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import mrcfile
    MRCFILE_AVAILABLE = True
except ImportError:
    MRCFILE_AVAILABLE = False

logger = logging.getLogger(__name__)

class MotionCorrectionVisualizer(QWidget):
    """
    Motion correction visualizer with themed interface matching CTF visualizer.
    Displays motion corrected images, drift plots, and motion metrics.
    """
    
    # Signals
    image_changed = pyqtSignal(int)  # tilt_index
    analysis_updated = pyqtSignal(dict)  # motion_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        
        # Data storage
        self.motion_data = {}
        self.current_series = None
        self.current_tilt_index = 0
        self.tilt_angles = []
        self.motion_images = {}
        self.drift_data = {}
        
        # Theme colors (matching CTF visualizer)
        self.theme_colors = {
            'background': '#2b2b2b',
            'surface': '#3c3c3c',
            'primary': '#bb86fc',
            'secondary': '#03dac6',
            'accent': '#cf6679',
            'text': '#ffffff',
            'text_secondary': '#b3b3b3'
        }
        
        self.setup_ui()
        self.apply_theme()
        
        logger.info("Motion Correction Visualizer initialized")
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Header
        header = self.create_header()
        layout.addWidget(header)
        
        # Main content splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel: Controls and info (30%)
        left_panel = self.create_control_panel()
        main_splitter.addWidget(left_panel)
        
        # Right panel: Visualization (70%)
        right_panel = self.create_visualization_panel()
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        main_splitter.setSizes([300, 700])
        
        layout.addWidget(main_splitter)
        
        # Status bar
        self.status_bar = QLabel("Ready - Load motion correction data to begin")
        self.status_bar.setStyleSheet("padding: 5px; border-top: 1px solid #555;")
        layout.addWidget(self.status_bar)
    
    def create_header(self):
        """Create header with title and controls."""
        header = QWidget()
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🎯 Motion Correction Analysis")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Load data button
        self.load_btn = QPushButton("📁 Load Motion Data")
        self.load_btn.clicked.connect(self.load_motion_data)
        layout.addWidget(self.load_btn)
        
        # Export button
        self.export_btn = QPushButton("💾 Export Analysis")
        self.export_btn.clicked.connect(self.export_analysis)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)
        
        return header
    
    def create_control_panel(self):
        """Create left control panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Series selection
        series_group = QGroupBox("📊 Series Selection")
        series_layout = QVBoxLayout(series_group)
        
        self.series_combo = QComboBox()
        self.series_combo.currentTextChanged.connect(self.on_series_changed)
        series_layout.addWidget(QLabel("Tilt Series:"))
        series_layout.addWidget(self.series_combo)
        
        layout.addWidget(series_group)
        
        # Tilt navigation
        nav_group = QGroupBox("🎚️ Tilt Navigation")
        nav_layout = QVBoxLayout(nav_group)
        
        # Tilt slider
        self.tilt_slider = QSlider(Qt.Orientation.Horizontal)
        self.tilt_slider.valueChanged.connect(self.on_tilt_changed)
        nav_layout.addWidget(QLabel("Tilt Angle:"))
        nav_layout.addWidget(self.tilt_slider)
        
        # Tilt info
        self.tilt_info = QLabel("Tilt: 0° (1/1)")
        nav_layout.addWidget(self.tilt_info)
        
        # Navigation buttons
        nav_buttons = QHBoxLayout()
        self.prev_btn = QPushButton("◀ Previous")
        self.next_btn = QPushButton("Next ▶")
        self.prev_btn.clicked.connect(self.previous_tilt)
        self.next_btn.clicked.connect(self.next_tilt)
        nav_buttons.addWidget(self.prev_btn)
        nav_buttons.addWidget(self.next_btn)
        nav_layout.addLayout(nav_buttons)
        
        layout.addWidget(nav_group)
        
        # Display options
        display_group = QGroupBox("🎨 Display Options")
        display_layout = QVBoxLayout(display_group)
        
        self.show_drift_vectors = QCheckBox("Show drift vectors")
        self.show_drift_vectors.setChecked(True)
        self.show_drift_vectors.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_drift_vectors)
        
        self.show_motion_patches = QCheckBox("Show motion patches")
        self.show_motion_patches.setChecked(True)
        self.show_motion_patches.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_motion_patches)
        
        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(['viridis', 'plasma', 'inferno', 'magma', 'gray'])
        self.colormap_combo.currentTextChanged.connect(self.update_display)
        display_layout.addWidget(QLabel("Colormap:"))
        display_layout.addWidget(self.colormap_combo)
        
        layout.addWidget(display_group)
        
        # Motion statistics
        stats_group = QGroupBox("📈 Motion Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(150)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        return panel
    
    def create_visualization_panel(self):
        """Create right visualization panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Visualization tabs
        self.viz_tabs = QTabWidget()
        
        # Motion corrected image tab
        self.image_tab = self.create_image_tab()
        self.viz_tabs.addTab(self.image_tab, "🖼️ Corrected Image")
        
        # Drift plots tab
        self.drift_tab = self.create_drift_tab()
        self.viz_tabs.addTab(self.drift_tab, "📊 Drift Analysis")
        
        # Motion summary tab
        self.summary_tab = self.create_summary_tab()
        self.viz_tabs.addTab(self.summary_tab, "📋 Motion Summary")
        
        layout.addWidget(self.viz_tabs)
        return panel
    
    def create_image_tab(self):
        """Create motion corrected image display tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        if MATPLOTLIB_AVAILABLE:
            # Create matplotlib figure
            self.image_figure = Figure(figsize=(8, 6), facecolor='#2b2b2b')
            self.image_canvas = FigureCanvas(self.image_figure)
            self.image_ax = self.image_figure.add_subplot(111)
            
            layout.addWidget(self.image_canvas)
        else:
            layout.addWidget(QLabel("Matplotlib not available for image display"))
        
        return tab
    
    def create_drift_tab(self):
        """Create drift analysis plots tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        if MATPLOTLIB_AVAILABLE:
            # Create matplotlib figure for drift plots
            self.drift_figure = Figure(figsize=(10, 8), facecolor='#2b2b2b')
            self.drift_canvas = FigureCanvas(self.drift_figure)
            
            layout.addWidget(self.drift_canvas)
        else:
            layout.addWidget(QLabel("Matplotlib not available for drift plots"))
        
        return tab
    
    def create_summary_tab(self):
        """Create motion summary tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        if MATPLOTLIB_AVAILABLE:
            # Create matplotlib figure for summary plots
            self.summary_figure = Figure(figsize=(10, 8), facecolor='#2b2b2b')
            self.summary_canvas = FigureCanvas(self.summary_figure)
            
            layout.addWidget(self.summary_canvas)
        else:
            layout.addWidget(QLabel("Matplotlib not available for summary plots"))
        
        return tab
    
    def apply_theme(self):
        """Apply dark theme matching CTF visualizer."""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.theme_colors['background']};
                color: {self.theme_colors['text']};
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {self.theme_colors['surface']};
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {self.theme_colors['primary']};
            }}
            
            QPushButton {{
                background-color: {self.theme_colors['surface']};
                border: 1px solid {self.theme_colors['primary']};
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: {self.theme_colors['primary']};
                color: {self.theme_colors['background']};
            }}
            
            QPushButton:pressed {{
                background-color: {self.theme_colors['accent']};
            }}
            
            QPushButton:disabled {{
                background-color: #555;
                color: #888;
                border-color: #555;
            }}
            
            QSlider::groove:horizontal {{
                border: 1px solid {self.theme_colors['surface']};
                height: 8px;
                background: {self.theme_colors['surface']};
                border-radius: 4px;
            }}
            
            QSlider::handle:horizontal {{
                background: {self.theme_colors['primary']};
                border: 1px solid {self.theme_colors['primary']};
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }}
            
            QSlider::sub-page:horizontal {{
                background: {self.theme_colors['secondary']};
                border-radius: 4px;
            }}
            
            QComboBox, QSpinBox {{
                background-color: {self.theme_colors['surface']};
                border: 1px solid {self.theme_colors['primary']};
                border-radius: 4px;
                padding: 5px;
                min-width: 100px;
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {self.theme_colors['text']};
            }}
            
            QCheckBox {{
                spacing: 8px;
            }}
            
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid {self.theme_colors['primary']};
                border-radius: 3px;
                background-color: {self.theme_colors['surface']};
            }}
            
            QCheckBox::indicator:checked {{
                background-color: {self.theme_colors['primary']};
                image: none;
            }}
            
            QTextEdit {{
                background-color: {self.theme_colors['surface']};
                border: 1px solid {self.theme_colors['primary']};
                border-radius: 4px;
                padding: 5px;
                font-family: 'Courier New', monospace;
            }}
            
            QTabWidget::pane {{
                border: 1px solid {self.theme_colors['surface']};
                background-color: {self.theme_colors['background']};
            }}
            
            QTabBar::tab {{
                background-color: {self.theme_colors['surface']};
                border: 1px solid {self.theme_colors['surface']};
                padding: 8px 16px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {self.theme_colors['primary']};
                color: {self.theme_colors['background']};
            }}
            
            QTabBar::tab:hover {{
                background-color: {self.theme_colors['accent']};
            }}
        """)
    
    def load_motion_data(self):
        """Load motion correction data from AreTomo3 output."""
        # This would integrate with file dialog and data loading
        self.status_bar.setText("Loading motion correction data...")
        
        # Placeholder for actual implementation
        logger.info("Motion correction data loading requested")
        
        # Enable export button when data is loaded
        self.export_btn.setEnabled(True)
        self.status_bar.setText("Motion correction data loaded successfully")
    
    def on_series_changed(self, series_name):
        """Handle series selection change."""
        self.current_series = series_name
        self.update_display()
        logger.debug(f"Series changed to: {series_name}")
    
    def on_tilt_changed(self, value):
        """Handle tilt slider change."""
        self.current_tilt_index = value
        self.update_tilt_info()
        self.update_display()
        self.image_changed.emit(value)
    
    def previous_tilt(self):
        """Navigate to previous tilt."""
        if self.current_tilt_index > 0:
            self.tilt_slider.setValue(self.current_tilt_index - 1)
    
    def next_tilt(self):
        """Navigate to next tilt."""
        if self.current_tilt_index < self.tilt_slider.maximum():
            self.tilt_slider.setValue(self.current_tilt_index + 1)
    
    def update_tilt_info(self):
        """Update tilt information display."""
        if self.tilt_angles and self.current_tilt_index < len(self.tilt_angles):
            angle = self.tilt_angles[self.current_tilt_index]
            total = len(self.tilt_angles)
            self.tilt_info.setText(f"Tilt: {angle:.1f}° ({self.current_tilt_index + 1}/{total})")
    
    def update_display(self):
        """Update all visualization displays."""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        current_tab = self.viz_tabs.currentIndex()
        
        if current_tab == 0:  # Image tab
            self.update_image_display()
        elif current_tab == 1:  # Drift tab
            self.update_drift_display()
        elif current_tab == 2:  # Summary tab
            self.update_summary_display()
    
    def update_image_display(self):
        """Update motion corrected image display."""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        self.image_ax.clear()
        
        # Placeholder for actual image display
        # This would load and display the motion corrected image
        self.image_ax.text(0.5, 0.5, 'Motion Corrected Image\n(Implementation pending)', 
                          ha='center', va='center', transform=self.image_ax.transAxes,
                          color=self.theme_colors['text'], fontsize=14)
        
        self.image_ax.set_facecolor(self.theme_colors['background'])
        self.image_figure.tight_layout()
        self.image_canvas.draw()
    
    def update_drift_display(self):
        """Update drift analysis plots."""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        self.drift_figure.clear()
        
        # Create subplots for drift analysis
        gs = self.drift_figure.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
        
        # Drift trajectory plot
        ax1 = self.drift_figure.add_subplot(gs[0, :])
        ax1.text(0.5, 0.5, 'Drift Trajectory Plot\n(Implementation pending)', 
                ha='center', va='center', transform=ax1.transAxes,
                color=self.theme_colors['text'])
        ax1.set_facecolor(self.theme_colors['background'])
        ax1.set_title('Drift Trajectory', color=self.theme_colors['text'])
        
        # X drift vs tilt
        ax2 = self.drift_figure.add_subplot(gs[1, 0])
        ax2.text(0.5, 0.5, 'X Drift vs Tilt\n(Implementation pending)', 
                ha='center', va='center', transform=ax2.transAxes,
                color=self.theme_colors['text'])
        ax2.set_facecolor(self.theme_colors['background'])
        ax2.set_title('X Drift', color=self.theme_colors['text'])
        
        # Y drift vs tilt
        ax3 = self.drift_figure.add_subplot(gs[1, 1])
        ax3.text(0.5, 0.5, 'Y Drift vs Tilt\n(Implementation pending)', 
                ha='center', va='center', transform=ax3.transAxes,
                color=self.theme_colors['text'])
        ax3.set_facecolor(self.theme_colors['background'])
        ax3.set_title('Y Drift', color=self.theme_colors['text'])
        
        self.drift_figure.patch.set_facecolor(self.theme_colors['background'])
        self.drift_canvas.draw()
    
    def update_summary_display(self):
        """Update motion summary plots."""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        self.summary_figure.clear()
        
        # Placeholder for summary plots
        ax = self.summary_figure.add_subplot(111)
        ax.text(0.5, 0.5, 'Motion Correction Summary\n(Implementation pending)', 
               ha='center', va='center', transform=ax.transAxes,
               color=self.theme_colors['text'], fontsize=14)
        ax.set_facecolor(self.theme_colors['background'])
        
        self.summary_figure.patch.set_facecolor(self.theme_colors['background'])
        self.summary_canvas.draw()
    
    def export_analysis(self):
        """Export motion correction analysis."""
        logger.info("Motion correction analysis export requested")
        self.status_bar.setText("Exporting motion correction analysis...")
