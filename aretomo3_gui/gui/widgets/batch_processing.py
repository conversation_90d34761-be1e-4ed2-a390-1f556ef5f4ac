#!/usr/bin/env python3

import json
import os
from pathlib import Path

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QMessageBox,
    QPushButton,
    QSpinBox,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
)


# TODO: Refactor class - Class 'BatchProcessingWidget' too long (1184 lines)
class BatchProcessingWidget(QWidget):
    """Widget for managing batch processing of multiple tilt series"""

    start_batch = pyqtSignal(list)

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.main_window = parent  # Store direct reference to main window
        logger.info(
            f"DEBUG: BatchProcessingWidget created with parent class: {type(parent).__name__}"
        )

        self.setup_ui()
        self.batch_items = []

        # Verify parent has the expected methods
        if parent:
            # Try to find the parent window with the find_tilt_series method
            has_find_method = hasattr(parent, "find_tilt_series") and callable(
                getattr(parent, "find_tilt_series")
            )
            has_log_method = hasattr(parent, "log_message") and callable(
                getattr(parent, "log_message")
            )

            if not has_find_method:
                logger.info(
                    f"WARNING: Parent does not have a callable find_tilt_series method"
                )
                # Print parent class for debugging
                logger.info(f"Parent class: {type(parent).__name__}")

            if not has_log_method:
                logger.info(
                    f"WARNING: Parent does not have a callable log_message method"
                )

    def safe_log(self, message):
        """Safely log a message using the parent's log_message method if available"""
        # Try to log through the main window reference
        if hasattr(self.main_window, "log_message") and callable(
            getattr(self.main_window, "log_message")
        ):
            self.main_window.log_message(message)
        else:
            logger.info(f"[Batch Processing] {message}")  # Fallback to console output

    def log_message(self, message, level="INFO"):
        """Log a message, compatible with test expectations."""
        self.safe_log(f"[{level}] {message}")

    def auto_create_output_directory(self, input_dir):
        """Auto-create output directory based on input directory (equivalent to mkdir -p)."""
        from pathlib import Path

        try:
            # Create output directory inside input directory using standard naming
            output_path = os.path.join(input_dir, "aretomo_output")
            Path(output_path).mkdir(parents=True, exist_ok=True)
            self.safe_log(f"Auto-created output directory: {output_path}")
            return output_path

        except Exception as e:
            self.safe_log(f"WARNING: Failed to auto-create output directory: {e}")
            # Fallback: suggest output directory path without creating it
            output_path = os.path.join(input_dir, "aretomo_output")
            self.safe_log(f"Suggested output directory (not created): {output_path}")
            return output_path

    # TODO: Refactor function - Function 'setup_ui' too long (125 lines)
    def setup_ui(self):
        """Execute setup_ui operation."""
        layout = QVBoxLayout(self)

        # Batch control buttons
        control_group = QGroupBox("Batch Controls")
        control_layout = QHBoxLayout()

        self.add_folder_btn = QPushButton("Add Folder")
        self.add_folder_btn.clicked.connect(self.add_folder)

        self.add_series_btn = QPushButton("Add Selected Series")
        self.add_series_btn.clicked.connect(self.add_selected_series)

        self.remove_btn = QPushButton("Remove Selected")
        self.remove_btn.clicked.connect(self.remove_selected)

        self.clear_btn = QPushButton("Clear All")
        self.clear_btn.clicked.connect(self.clear_batch)

        self.save_batch_btn = QPushButton("Save Batch")
        self.save_batch_btn.clicked.connect(self.save_batch)

        self.load_batch_btn = QPushButton("Load Batch")
        self.load_batch_btn.clicked.connect(self.load_batch)

        control_layout.addWidget(self.add_folder_btn)
        control_layout.addWidget(self.add_series_btn)
        control_layout.addWidget(self.remove_btn)
        control_layout.addWidget(self.clear_btn)
        control_layout.addWidget(self.save_batch_btn)
        control_layout.addWidget(self.load_batch_btn)
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # Batch processing options
        options_group = QGroupBox("Batch Processing Options")
        options_layout = QHBoxLayout()

        self.output_dir_override_chk = QCheckBox("Override Output Directory")

        self.gpu_select = QComboBox()
        self.gpu_select.addItems(["Auto", "GPU 0", "GPU 1", "GPU 2", "GPU 3"])

        self.max_parallel = QSpinBox()
        self.max_parallel.setRange(1, 8)
        self.max_parallel.setValue(1)
        self.max_parallel.setToolTip("Maximum number of parallel processes")

        options_layout.addWidget(self.output_dir_override_chk)
        options_layout.addWidget(QLabel("GPU:"))
        options_layout.addWidget(self.gpu_select)
        options_layout.addWidget(QLabel("Max Parallel:"))
        options_layout.addWidget(self.max_parallel)
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # Tomogram selection section
        tomogram_group = QGroupBox("Tomogram Selection")
        tomogram_layout = QVBoxLayout()

        # Selection controls
        selection_controls = QHBoxLayout()

        self.load_tomograms_btn = QPushButton("Load Tomograms")
        self.load_tomograms_btn.clicked.connect(self.load_tomograms_for_selection)

        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_tomograms)

        self.deselect_all_btn = QPushButton("Deselect All")
        self.deselect_all_btn.clicked.connect(self.deselect_all_tomograms)

        self.load_analysis_btn = QPushButton("Load Analysis for Selected")
        self.load_analysis_btn.clicked.connect(self.load_analysis_for_selected)

        selection_controls.addWidget(self.load_tomograms_btn)
        selection_controls.addWidget(self.select_all_btn)
        selection_controls.addWidget(self.deselect_all_btn)
        selection_controls.addWidget(self.load_analysis_btn)
        selection_controls.addStretch()

        tomogram_layout.addLayout(selection_controls)
        tomogram_group.setLayout(tomogram_layout)
        layout.addWidget(tomogram_group)

        # Batch table with additional columns for selection and tomogram info
        self.batch_table = QTableWidget(0, 8)
        self.batch_table.setHorizontalHeaderLabels(
            [
                "Select",
                "Position",
                "Input Directory",
                "Output Directory",
                "File Count",
                "Tomogram",
                "View",
                "Status",
            ]
        )
        self.batch_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.Stretch
        )
        self.batch_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.batch_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # Set column widths
        self.batch_table.setColumnWidth(0, 60)  # Select column
        self.batch_table.setColumnWidth(4, 80)  # File Count column
        self.batch_table.setColumnWidth(6, 60)  # View column
        self.batch_table.setColumnWidth(7, 100)  # Status column

        layout.addWidget(self.batch_table)

        # Execute buttons
        execute_layout = QHBoxLayout()
        self.start_batch_btn = QPushButton("Start Batch Processing")
        self.start_batch_btn.clicked.connect(self.start_processing)
        self.stop_batch_btn = QPushButton("Stop Batch Processing")
        self.stop_batch_btn.clicked.connect(self.stop_processing)
        self.stop_batch_btn.setEnabled(False)

        execute_layout.addWidget(self.start_batch_btn)
        execute_layout.addWidget(self.stop_batch_btn)
        layout.addLayout(execute_layout)

    # TODO: Refactor add_folder - complexity: 11 (target: <10)
    # TODO: Refactor function - Function 'add_folder' too long (58 lines)
    def add_folder(self):
        """Add all tilt series from a folder to the batch"""
        try:
            folder = QFileDialog.getExistingDirectory(
                self, "Select Directory with Tilt Series"
            )
            if not folder:
                return

            # Look for tilt series in the folder (delegate to main window)
            if hasattr(self.main_window, "find_tilt_series") and callable(
                getattr(self.main_window, "find_tilt_series")
            ):
                self.safe_log(f"Looking for tilt series in {folder}")
                series_found = self.main_window.find_tilt_series(folder)

                if series_found and len(series_found) > 0:
                    count = 0
                    for pos_name, series in series_found.items():
                        if self.add_series_to_batch(series, folder):
                            count += 1

                    QMessageBox.information(
                        self,
                        "Tilt Series Added",
                        f"Added {count} tilt series from {folder}",
                    )
                    return count
                else:
                    QMessageBox.warning(
                        self,
                        "No Tilt Series Found",
                        f"No valid tilt series found in {folder}",
                    )
            else:
                error_msg = "Unable to find tilt series - function not available"
                self.safe_log(f"ERROR: {error_msg}")
                if hasattr(self.main_window, "find_tilt_series"):
                    self.safe_log(f"find_tilt_series exists but is not callable")
                self.safe_log(
                    "Main window methods: "
                    + str(
                        [
                            m
                            for m in dir(self.main_window)
                            if not m.startswith("_")
                            and callable(getattr(self.main_window, m))
                        ]
                    )
                )
                QMessageBox.warning(self, "Function Not Available", error_msg)
        except Exception as e:
            error_msg = f"Error adding folder: {str(e)}"
            logger.info(f"ERROR: {error_msg}")  # Always print to console
            QMessageBox.critical(self, "Error", error_msg)
            import traceback

            logger.info(traceback.format_exc())  # Print stack trace to console

    # TODO: Refactor function - Function 'add_selected_series' too long (58 lines)
    def add_selected_series(self):
        """Add the currently selected tilt series to the batch"""
        try:
            if (
                hasattr(self.main_window, "current_position")
                and self.main_window.current_position
            ):
                series = self.main_window.current_position

                # Get input directory from parameters tab
                input_dir = ""
                if hasattr(self.main_window, "parameters_tab") and hasattr(
                    self.main_window.parameters_tab, "in_prefix"
                ):
                    input_dir = self.main_window.parameters_tab.in_prefix.text().strip()

                if not input_dir:
                    QMessageBox.warning(
                        self,
                        "No Input Directory",
                        "Please configure input directory in Parameters tab first",
                    )
                    return

                if series:
                    self.safe_log(
                        f"Adding selected series {series.position_name} to batch"
                    )
                    if self.add_series_to_batch(series, input_dir):
                        QMessageBox.information(
                            self,
                            "Series Added",
                            f"Added {series.position_name} to batch processing",
                        )
                    else:
                        QMessageBox.information(
                            self,
                            "Series Already Added",
                            f"{series.position_name} is already in the batch",
                        )
                else:
                    self.safe_log("No series data available for selected position")
                    QMessageBox.warning(
                        self,
                        "No Series Selected",
                        "No tilt series is currently selected",
                    )
            else:
                QMessageBox.warning(
                    self, "No Series Selected", "No tilt series is currently selected"
                )
        except Exception as e:
            error_msg = f"Error adding selected series: {str(e)}"
            logger.info(f"ERROR: {error_msg}")  # Always print to console
            QMessageBox.critical(self, "Error", error_msg)
            import traceback

            logger.info(traceback.format_exc())  # Print stack trace to console

    # TODO: Refactor function - Function 'add_series_to_batch' too long (60 lines)
    def add_series_to_batch(self, series, input_dir):
        """Add a single tilt series to the batch table"""
        # Check if already in batch
        for item in self.batch_items:
            if (
                item["position"] == series.position_name
                and item["input_dir"] == input_dir
            ):
                return False  # Already in batch

        # Auto-create output directory using the same logic as Control Center
        output_dir = self.auto_create_output_directory(input_dir)

        # Add to batch items list
        batch_item = {
            "position": series.position_name,
            "input_dir": input_dir,
            "output_dir": output_dir,  # Store the auto-created output directory
            "file_count": len(series.files),
            "status": "Pending",
            "series": series,
            "selected": False,
            "tomogram": "",  # Will be populated when tomograms are loaded
            "tomogram_path": "",  # Full path to tomogram file
        }
        self.batch_items.append(batch_item)

        # Add to table with updated column structure: Select, Position, Input Directory, Output Directory, File Count, Tomogram, View, Status
        row = self.batch_table.rowCount()
        self.batch_table.insertRow(row)

        # Column 0: Select checkbox
        checkbox = QCheckBox()
        checkbox.stateChanged.connect(
            lambda state, r=row: self._on_checkbox_changed(r, state)
        )
        self.batch_table.setCellWidget(row, 0, checkbox)

        # Column 1: Position
        self.batch_table.setItem(row, 1, QTableWidgetItem(series.position_name))

        # Column 2: Input Directory
        self.batch_table.setItem(row, 2, QTableWidgetItem(input_dir))

        # Column 3: Output Directory
        self.batch_table.setItem(row, 3, QTableWidgetItem(output_dir))

        # Column 4: File Count
        self.batch_table.setItem(row, 4, QTableWidgetItem(str(len(series.files))))

        # Column 5: Tomogram (empty for now)
        self.batch_table.setItem(row, 5, QTableWidgetItem(""))

        # Column 6: View icon (empty for now, will be populated when tomogram is found)
        self.batch_table.setItem(row, 6, QTableWidgetItem(""))

        # Column 7: Status
        self.batch_table.setItem(row, 7, QTableWidgetItem("Pending"))

        return True

    def _on_checkbox_changed(self, row, state):
        """Handle checkbox state change"""
        if 0 <= row < len(self.batch_items):
            self.batch_items[row]["selected"] = state == Qt.CheckState.Checked.value

    # TODO: Refactor function - Function 'load_tomograms_for_selection' too long (66 lines)
    def load_tomograms_for_selection(self):
        """Load available tomograms for selection in batch processing"""
        import glob

        if not self.batch_items:
            QMessageBox.warning(
                self, "No Items", "No items in batch to load tomograms for"
            )
            return

        tomogram_count = 0

        for i, item in enumerate(self.batch_items):
            input_dir = item["input_dir"]
            output_dir = item["output_dir"]
            position = item["position"]

            # Look for tomogram files in both input and output directories
            tomogram_patterns = [
                # Output directory patterns (AreTomo3 results)
                os.path.join(output_dir, f"*{position}*_Vol.mrc"),
                os.path.join(output_dir, f"*{position}*_Rec.mrc"),
                os.path.join(output_dir, f"*{position}*.mrc"),
                os.path.join(output_dir, f"*{position}*.rec"),
                # Input directory patterns (original tomograms)
                os.path.join(input_dir, f"*{position}*.mrc"),
                os.path.join(input_dir, f"*{position}*.rec"),
                os.path.join(input_dir, f"*{position}*.ali"),
                os.path.join(input_dir, f"{position}*.mrc"),
                os.path.join(input_dir, f"{position}*.rec"),
                os.path.join(input_dir, f"{position}*.ali"),
            ]

            tomogram_files = []
            for pattern in tomogram_patterns:
                tomogram_files.extend(glob.glob(pattern))

            if tomogram_files:
                # Use the first found tomogram (prefer output directory results)
                tomogram_file_path = tomogram_files[0]
                tomogram_file = os.path.basename(tomogram_file_path)

                # Store both filename and full path
                item["tomogram"] = tomogram_file
                item["tomogram_path"] = tomogram_file_path

                # Update table
                self.batch_table.setItem(i, 5, QTableWidgetItem(tomogram_file))

                # Add view icon button
                self.add_view_icon(i, tomogram_file_path)

                tomogram_count += 1
                self.safe_log(f"Found tomogram for {position}: {tomogram_file}")
            else:
                item["tomogram"] = "Not found"
                item["tomogram_path"] = ""
                self.batch_table.setItem(i, 5, QTableWidgetItem("Not found"))
                self.batch_table.setItem(i, 6, QTableWidgetItem(""))
                self.safe_log(f"No tomogram found for {position}")

        QMessageBox.information(
            self,
            "Tomograms Loaded",
            f"Found tomograms for {tomogram_count} out of {len(self.batch_items)} items",
        )

    def add_view_icon(self, row: int, tomogram_path: str):
        """Add enhanced view icon buttons for tomogram with multiple viewing options."""
        view_widget = QWidget()
        view_layout = QHBoxLayout(view_widget)
        view_layout.setContentsMargins(2, 2, 2, 2)
        view_layout.setSpacing(2)

        # Main view button (Napari)
        view_btn = QPushButton("👁️")
        view_btn.setToolTip(
            f"View tomogram in Napari: {os.path.basename(tomogram_path)}"
        )
        view_btn.setMaximumWidth(30)
        view_btn.clicked.connect(lambda: self.view_tomogram(tomogram_path))
        view_layout.addWidget(view_btn)

        # 3D volume visualization button
        view_3d_btn = QPushButton("🧊")
        view_3d_btn.setToolTip(
            f"3D volume visualization: {os.path.basename(tomogram_path)}"
        )
        view_3d_btn.setMaximumWidth(30)
        view_3d_btn.clicked.connect(lambda: self.view_tomogram_3d(tomogram_path))
        view_layout.addWidget(view_3d_btn)

        # Side-by-side comparison button
        compare_btn = QPushButton("⚖️")
        compare_btn.setToolTip(
            f"Side-by-side comparison: {os.path.basename(tomogram_path)}"
        )
        compare_btn.setMaximumWidth(30)
        compare_btn.clicked.connect(lambda: self.compare_tomogram(tomogram_path))
        view_layout.addWidget(compare_btn)

        # Animation generation button
        anim_btn = QPushButton("🎬")
        anim_btn.setToolTip(f"Generate animation: {os.path.basename(tomogram_path)}")
        anim_btn.setMaximumWidth(30)
        anim_btn.clicked.connect(lambda: self.generate_animation(tomogram_path))
        view_layout.addWidget(anim_btn)

        self.batch_table.setCellWidget(row, 6, view_widget)

    # TODO: Refactor view_tomogram - complexity: 22 (target: <10)

    # TODO: Refactor function - Function 'view_tomogram' too long (156 lines)
    def view_tomogram(self, tomogram_path: str):
        """Open tomogram in the Napari viewer tab."""
        try:
            if not os.path.exists(tomogram_path):
                QMessageBox.warning(
                    self, "File Not Found", f"Tomogram file not found:\n{tomogram_path}"
                )
                return

            self.safe_log(f"Attempting to view tomogram: {tomogram_path}")

            # Check if main window has viewer tab
            if hasattr(self.main_window, "tab_widget") and hasattr(
                self.main_window, "viewer_tab"
            ):
                # Find Napari Viewer tab index by checking tab text
                viewer_tab_index = None
                for i in range(self.main_window.tab_widget.count()):
                    tab_text = self.main_window.tab_widget.tabText(i)
                    if "Napari Viewer" in tab_text or "🔬 Napari Viewer" in tab_text:
                        viewer_tab_index = i
                        break

                if viewer_tab_index is not None:
                    # Switch to viewer tab
                    self.main_window.tab_widget.setCurrentIndex(viewer_tab_index)
                    self.safe_log(f"Switched to viewer tab at index {viewer_tab_index}")

                    # Load tomogram in Napari viewer
                    if (
                        hasattr(self.main_window.viewer_tab, "layout")
                        and self.main_window.viewer_tab.layout()
                    ):
                        # Get the NapariMRCViewer widget from the viewer tab
                        napari_viewer = None
                        for i in range(self.main_window.viewer_tab.layout().count()):
                            widget = (
                                self.main_window.viewer_tab.layout().itemAt(i).widget()
                            )
                            if hasattr(widget, "load_mrc_file") or hasattr(
                                widget, "load_file"
                            ):
                                napari_viewer = widget
                                break

                        if napari_viewer:
                            # Try different methods to load the MRC file
                            from pathlib import Path

                            tomogram_path_obj = Path(tomogram_path)

                            if hasattr(napari_viewer, "load_file"):
                                napari_viewer.load_file(tomogram_path_obj)
                                self.safe_log(
                                    f"Loaded tomogram using load_file: {os.path.basename(tomogram_path)}"
                                )
                            elif hasattr(napari_viewer, "load_mrc_file"):
                                napari_viewer.load_mrc_file(tomogram_path_obj)
                                self.safe_log(
                                    f"Loaded tomogram using load_mrc_file: {os.path.basename(tomogram_path)}"
                                )
                            elif (
                                hasattr(napari_viewer, "embedded_napari")
                                and napari_viewer.embedded_napari
                            ):
                                # Access embedded napari viewer
                                if hasattr(
                                    napari_viewer.embedded_napari, "load_mrc_file"
                                ):
                                    napari_viewer.embedded_napari.load_mrc_file(
                                        tomogram_path_obj
                                    )
                                    self.safe_log(
                                        f"Loaded tomogram using embedded napari: {os.path.basename(tomogram_path)}"
                                    )
                                elif hasattr(
                                    napari_viewer.embedded_napari, "napari_viewer"
                                ):
                                    # Direct napari viewer access
                                    napari_viewer.embedded_napari.napari_viewer.open(
                                        tomogram_path
                                    )
                                    self.safe_log(
                                        f"Loaded tomogram using direct napari.open: {os.path.basename(tomogram_path)}"
                                    )
                                else:
                                    self.safe_log(
                                        "ERROR: Embedded napari viewer not properly initialized"
                                    )
                                    QMessageBox.warning(
                                        self,
                                        "Viewer Error",
                                        "Embedded Napari viewer not properly initialized",
                                    )
                                    return
                            else:
                                self.safe_log(
                                    "ERROR: No suitable method found to load MRC file in Napari viewer"
                                )
                                self.safe_log(
                                    f"Available methods: {[attr for attr in dir(napari_viewer) if not attr.startswith('_')]}"
                                )
                                QMessageBox.warning(
                                    self,
                                    "Viewer Error",
                                    "Napari viewer does not have a compatible file loading method",
                                )
                                return

                            QMessageBox.information(
                                self,
                                "Tomogram Loaded",
                                f"Tomogram loaded in Napari viewer:\n{os.path.basename(tomogram_path)}",
                            )
                        else:
                            self.safe_log(
                                "ERROR: Could not find NapariMRCViewer widget in viewer tab"
                            )
                            QMessageBox.warning(
                                self,
                                "Viewer Error",
                                "Could not find Napari viewer widget in viewer tab",
                            )
                    else:
                        self.safe_log("ERROR: Viewer tab has no layout or widgets")
                        QMessageBox.warning(
                            self,
                            "Viewer Error",
                            "Viewer tab is not properly initialized",
                        )
                else:
                    self.safe_log("ERROR: Could not find Napari Viewer tab")
                    # List available tabs for debugging
                    available_tabs = []
                    for i in range(self.main_window.tab_widget.count()):
                        available_tabs.append(self.main_window.tab_widget.tabText(i))
                    self.safe_log(f"Available tabs: {available_tabs}")
                    QMessageBox.warning(
                        self,
                        "Viewer Error",
                        f"Could not find Napari Viewer tab.\nAvailable tabs: {', '.join(available_tabs)}",
                    )
            else:
                self.safe_log(
                    "ERROR: Main window does not have tab_widget or viewer_tab attribute"
                )
                QMessageBox.warning(
                    self, "Viewer Error", "Viewer tab not available in main window"
                )

        except Exception as e:
            self.safe_log(f"ERROR: Exception viewing tomogram: {str(e)}")
            import traceback

            self.safe_log(f"Traceback: {traceback.format_exc()}")
            QMessageBox.critical(self, "Error", f"Failed to view tomogram:\n{str(e)}")

    def select_all_tomograms(self):
        """Select all tomograms in the batch"""
        for row in range(self.batch_table.rowCount()):
            checkbox = self.batch_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_tomograms(self):
        """Deselect all tomograms in the batch"""
        for row in range(self.batch_table.rowCount()):
            checkbox = self.batch_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def load_analysis_for_selected(self):
        """Load analysis files for selected tomograms"""
        selected_items = [
            item for item in self.batch_items if item.get("selected", False)
        ]

        if not selected_items:
            QMessageBox.warning(self, "No Selection", "No tomograms are selected")
            return

        # Get analysis files for selected items
        analysis_files = []
        for item in selected_items:
            input_dir = item["input_dir"]
            position = item["position"]

            # Find analysis files in the input directory
            item_analysis_files = self._find_analysis_files_for_position(
                input_dir, position
            )
            analysis_files.extend(item_analysis_files)

            self.safe_log(
                f"Found {len(item_analysis_files)} analysis files for {position}"
            )

        if analysis_files:
            # Load analysis files into the viewer
            self._load_analysis_files_into_viewer(analysis_files)

            # Switch to analysis tab
            if hasattr(self.main_window, "tabs"):
                self.main_window.tabs.setCurrentIndex(6)  # Analysis tab is at index 6

            QMessageBox.information(
                self,
                "Analysis Loaded",
                f"Loaded {len(analysis_files)} analysis files for {len(selected_items)} selected tomograms",
            )
        else:
            QMessageBox.warning(
                self,
                "No Analysis Files",
                "No analysis files found for selected tomograms",
            )

    def _find_analysis_files_for_position(self, input_dir, position):
        """Find analysis files for a specific position in directory and subdirectories"""
        import glob

        analysis_files = []

        # Define patterns for analysis files
        patterns = [
            f"*{position}*.xf",  # Transform files
            f"*{position}*_mc.log",  # Motion correction logs
            f"*{position}*_ctf.txt",  # CTF estimation results
            f"*{position}*.tlt",  # Tilt angle files
            f"*{position}*.log",  # General log files
            f"*{position}*_dose.txt",  # Dose weighting files
            f"{position}*.xf",  # Transform files (alternative naming)
            f"{position}*_mc.log",  # Motion correction logs (alternative naming)
            f"{position}*_ctf.txt",  # CTF estimation results (alternative naming)
            f"{position}*.tlt",  # Tilt angle files (alternative naming)
            f"{position}*.log",  # General log files (alternative naming)
            f"{position}*_dose.txt",  # Dose weighting files (alternative naming)
        ]

        # Search recursively in subdirectories
        for pattern in patterns:
            full_pattern = os.path.join(input_dir, "**", pattern)
            matching_files = glob.glob(full_pattern, recursive=True)
            analysis_files.extend(matching_files)

        # Remove duplicates while preserving order
        seen = set()
        unique_files = []
        for f in analysis_files:
            if f not in seen:
                seen.add(f)
                unique_files.append(f)

        # TODO: Refactor _load_analysis_files_into_viewer - complexity: 14 (target: <10)
        return unique_files

    def _load_analysis_files_into_viewer(self, analysis_files):
        """Load analysis files into the analysis viewer"""
        try:
            # Get the analysis viewer from the main window
            if hasattr(self.main_window, "analysis_viewer"):
                analysis_viewer = self.main_window.analysis_viewer

                # Load files into appropriate tabs based on file type
                for file_path in analysis_files:
                    filename = os.path.basename(file_path).lower()

                    if filename.endswith(".xf"):
                        # Load transform files into motion correction tab
                        if hasattr(analysis_viewer, "load_motion_correction_file"):
                            analysis_viewer.load_motion_correction_file(file_path)
                    elif "_mc.log" in filename:
                        # Load motion correction logs
                        if hasattr(analysis_viewer, "load_motion_correction_file"):
                            analysis_viewer.load_motion_correction_file(file_path)
                    elif "_ctf.txt" in filename:
                        # Load CTF estimation results
                        if hasattr(analysis_viewer, "load_ctf_file"):
                            analysis_viewer.load_ctf_file(file_path)
                    elif filename.endswith(".tlt"):
                        # Load tilt angle files
                        if hasattr(analysis_viewer, "load_tilt_series_file"):
                            analysis_viewer.load_tilt_series_file(file_path)
                    elif filename.endswith(".log"):
                        # Load general log files
                        if hasattr(analysis_viewer, "load_log_file"):
                            analysis_viewer.load_log_file(file_path)

                self.safe_log(
                    f"Loaded {len(analysis_files)} analysis files into viewer"
                )
            else:
                self.safe_log("ERROR: Analysis viewer not found in main window")

        except Exception as e:
            self.safe_log(f"ERROR: Failed to load analysis files into viewer: {str(e)}")
            import traceback

            logger.info(traceback.format_exc())

    def remove_selected(self):
        """Remove selected items from the batch"""
        selected_rows = sorted(
            set(index.row() for index in self.batch_table.selectedIndexes()),
            reverse=True,
        )

        for row in selected_rows:
            if 0 <= row < len(self.batch_items):
                self.batch_items.pop(row)
                self.batch_table.removeRow(row)

    def clear_batch(self):
        """Clear all items from the batch"""
        self.batch_items.clear()
        self.batch_table.setRowCount(0)

    def save_batch(self):
        """Save current batch to a JSON file"""
        if not self.batch_items:
            QMessageBox.warning(self, "Empty Batch", "No items in batch to save")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Batch", "", "JSON Files (*.json)"
        )
        if not file_path:
            return

        # Create serializable data
        save_data = []
        for item in self.batch_items:
            save_item = {
                "position": item["position"],
                "input_dir": item["input_dir"],
                "file_count": item["file_count"],
                "status": item["status"],
            }
            save_data.append(save_item)

        try:
            with open(file_path, "w") as f:
                json.dump(save_data, f, indent=2)
            QMessageBox.information(self, "Batch Saved", f"Batch saved to {file_path}")
        # TODO: Refactor load_batch - complexity: 11 (target: <10)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving batch: {str(e)}")

    # TODO: Refactor function - Function 'load_batch' too long (106 lines)
    def load_batch(self):
        """Load batch from a JSON file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Batch", "", "JSON Files (*.json)"
        )
        if not file_path:
            return

        try:
            with open(file_path, "r") as f:
                save_data = json.load(f)

            # Clear current batch
            self.clear_batch()

            self.safe_log(f"Loading batch from {file_path} with {len(save_data)} items")

            # Add items from file
            loaded_count = 0
            not_found_count = 0

            for item in save_data:
                input_dir = item["input_dir"]

                # Try to locate the series in the input directory
                found = False
                if hasattr(self.main_window, "find_tilt_series") and os.path.exists(
                    input_dir
                ):
                    self.safe_log(f"Searching for tilt series at {input_dir}")
                    series_dict = self.main_window.find_tilt_series(input_dir)
                    if series_dict and item["position"] in series_dict:
                        series = series_dict[item["position"]]
                        if self.add_series_to_batch(series, input_dir):
                            self.safe_log(
                                f"Loaded series {item['position']} from {input_dir}"
                            )
                            loaded_count += 1
                            found = True

                if not found:
                    self.safe_log(
                        f"Could not find series {item['position']} at {input_dir}"
                    )
                    not_found_count += 1
                    # Add as placeholder with warning - updated for new table structure
                    row = self.batch_table.rowCount()
                    self.batch_table.insertRow(row)

                    # Column 0: Select checkbox
                    checkbox = QCheckBox()
                    checkbox.stateChanged.connect(
                        lambda state, r=row: self._on_checkbox_changed(r, state)
                    )
                    self.batch_table.setCellWidget(row, 0, checkbox)

                    # Column 1: Position
                    self.batch_table.setItem(row, 1, QTableWidgetItem(item["position"]))

                    # Column 2: Input Directory
                    self.batch_table.setItem(row, 2, QTableWidgetItem(input_dir))

                    # Column 3: Output Directory (auto-create for placeholder)
                    output_dir = self.auto_create_output_directory(input_dir)
                    self.batch_table.setItem(row, 3, QTableWidgetItem(output_dir))

                    # Column 4: File Count
                    self.batch_table.setItem(
                        row, 4, QTableWidgetItem(str(item["file_count"]))
                    )

                    # Column 5: Tomogram
                    self.batch_table.setItem(row, 5, QTableWidgetItem("Not found"))

                    # Column 6: View (empty for not found items)
                    self.batch_table.setItem(row, 6, QTableWidgetItem(""))

                    # Column 7: Status
                    self.batch_table.setItem(row, 7, QTableWidgetItem("Not Found"))

                    # Add placeholder to items
                    self.batch_items.append(
                        {
                            "position": item["position"],
                            "input_dir": input_dir,
                            "output_dir": output_dir,  # Include auto-created output directory
                            "file_count": item["file_count"],
                            "status": "Not Found",
                            "series": None,
                            "selected": False,
                            "tomogram": "Not found",
                            "tomogram_path": "",
                        }
                    )

            message = f"Loaded batch from {file_path}: {loaded_count} series loaded, {not_found_count} not found"
            self.safe_log(message)
            QMessageBox.information(self, "Batch Loaded", message)

        except Exception as e:
            error_msg = f"Error loading batch: {str(e)}"
            self.safe_log(f"ERROR: {error_msg}")
            QMessageBox.critical(self, "Error", error_msg)
            import traceback

            logger.info(traceback.format_exc())  # Print stack trace to console

    def start_processing(self):
        """Start batch processing"""
        if not self.batch_items:
            QMessageBox.warning(self, "Empty Batch", "No items in batch to process")
            return

        # Check for items with missing series
        missing_series = [
            item for item in self.batch_items if item["status"] == "Not Found"
        ]
        if missing_series:
            result = QMessageBox.question(
                self,
                "Missing Series",
                f"There are {len(missing_series)} series that can't be found. Continue anyway?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )
            if result == QMessageBox.StandardButton.No:
                return

        # Log start of batch processing
        self.safe_log(f"Starting batch processing of {len(self.batch_items)} items")

        # Auto-populate parameters tab with I/O paths from first batch item
        if self.batch_items and hasattr(self.main_window, "parameters_tab"):
            first_item = self.batch_items[0]
            input_path = first_item.get("input_dir", "")
            output_path = first_item.get("output_dir", "")
            if hasattr(self.main_window.parameters_tab, "set_io_paths"):
                self.main_window.parameters_tab.set_io_paths(input_path, output_path)
                self.safe_log(
                    f"Auto-populated Parameters tab with I/O paths: {input_path} -> {output_path}"
                )

        # Emit signal to start batch processing
        self.start_batch.emit(self.batch_items)

        # Update UI
        self.start_batch_btn.setEnabled(False)
        self.stop_batch_btn.setEnabled(True)

    def stop_processing(self):
        """Stop batch processing"""
        self.safe_log("Stopping batch processing")

        if hasattr(self.parent(), "stop_batch_processing"):
            self.parent().stop_batch_processing()
        else:
            self.safe_log("WARNING: Parent does not have stop_batch_processing method")

        # Update UI
        self.start_batch_btn.setEnabled(True)
        self.stop_batch_btn.setEnabled(False)

    def update_item_status(self, position, status):
        """Update status of batch item"""
        for i, item in enumerate(self.batch_items):
            if item["position"] == position:
                item["status"] = status
                self.batch_table.item(i, 7).setText(status)  # Status is now column 7
                self.safe_log(f"Updated {position} status to: {status}")

                # If status is completed, try to find and add view icon for tomogram
                if status.lower() in ["completed", "success", "finished"]:
                    self.update_tomogram_after_processing(i, item)

                return True

        self.safe_log(
            f"WARNING: Could not find batch item with position {position} to update status"
        )
        return False

    def update_tomogram_after_processing(self, row: int, item: dict):
        """Update tomogram information after processing is completed."""
        try:
            import glob

            output_dir = item["output_dir"]
            position = item["position"]

            # Look for newly created tomograms in output directory
            tomogram_patterns = [
                os.path.join(output_dir, f"*{position}*_Vol.mrc"),
                os.path.join(output_dir, f"*{position}*_Rec.mrc"),
                os.path.join(output_dir, f"*{position}*.mrc"),
                os.path.join(output_dir, f"*{position}*.rec"),
            ]

            tomogram_files = []
            for pattern in tomogram_patterns:
                tomogram_files.extend(glob.glob(pattern))

            if tomogram_files:
                # Use the first found tomogram
                tomogram_file_path = tomogram_files[0]
                tomogram_file = os.path.basename(tomogram_file_path)

                # Update item data
                item["tomogram"] = tomogram_file
                item["tomogram_path"] = tomogram_file_path

                # Update table
                self.batch_table.setItem(row, 5, QTableWidgetItem(tomogram_file))

                # Add view icon if not already present
                if not self.batch_table.cellWidget(row, 6):
                    self.add_view_icon(row, tomogram_file_path)

                self.safe_log(
                    f"Updated tomogram for {position} after processing: {tomogram_file}"
                )

        except Exception as e:
            self.safe_log(f"Error updating tomogram after processing: {str(e)}")

    def prepare_tilt_series(self, series, input_dir, output_dir):
        """Prepare tilt series for processing - AreTomo3 handles mdoc files natively"""
        try:
            # Check if any filename uses bracket notation
            has_bracket_format = False
            for file in series.files:
                if "[" in os.path.basename(file) and "]" in os.path.basename(file):
                    has_bracket_format = True
                    break

            if has_bracket_format:
                self.safe_log(
                    f"Series {series.position_name} has bracket notation in filenames"
                )
                self.safe_log(
                    "AreTomo3 will handle bracket notation natively - no preprocessing needed"
                )
            else:
                self.safe_log(
                    f"Series {series.position_name} uses standard filename format"
                )

            # Ensure output directory exists
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                self.safe_log(f"Created output directory: {output_dir}")

            # AreTomo3 handles mdoc files and bracket notation natively
            self.safe_log(f"Tilt series {series.position_name} prepared successfully")
            return True

        except Exception as e:
            self.safe_log(f"ERROR: Failed to prepare tilt series: {str(e)}")
            return False

    def view_tomogram_3d(self, tomogram_path: str):
        """Open tomogram in 3D volume visualization."""
        try:
            self.safe_log(f"Opening 3D volume visualization for: {tomogram_path}")

            # Try to use Napari for 3D visualization
            try:
                # Load the tomogram
                import mrcfile
                import napari

                with mrcfile.open(tomogram_path, mode="r") as mrc:
                    data = mrc.data

                # Create new Napari viewer for 3D visualization
                viewer = napari.Viewer(
                    title=f"3D Volume: {os.path.basename(tomogram_path)}"
                )
                viewer.add_image(
                    data,
                    name=os.path.basename(tomogram_path),
                    rendering="iso",
                    iso_threshold=0.5,
                )

                # Set 3D view
                viewer.dims.ndisplay = 3

                QMessageBox.information(
                    self,
                    "3D Viewer",
                    f"3D volume visualization opened for:\n{os.path.basename(tomogram_path)}",
                )

            except ImportError:
                QMessageBox.warning(
                    self, "3D Viewer", "Napari not available for 3D visualization"
                )

        except Exception as e:
            self.safe_log(
                f"ERROR: Failed to open 3D visualization for {tomogram_path}: {e}"
            )
            QMessageBox.critical(
                self, "3D Viewer Error", f"Failed to open 3D visualization:\n{str(e)}"
            )

    def compare_tomogram(self, tomogram_path: str):
        """Open side-by-side comparison tool."""
        try:
            self.safe_log(f"Opening comparison tool for: {tomogram_path}")

            # For now, show a placeholder message
            QMessageBox.information(
                self,
                "Comparison Tool",
                f"Side-by-side comparison tool will open for:\n{os.path.basename(tomogram_path)}\n\n"
                "This feature will allow comparison with other tomograms.",
            )

        except Exception as e:
            self.safe_log(
                f"ERROR: Failed to open comparison tool for {tomogram_path}: {e}"
            )
            QMessageBox.critical(
                self, "Comparison Error", f"Failed to open comparison tool:\n{str(e)}"
            )

    def generate_animation(self, tomogram_path: str):
        """Generate tilt series animation."""
        try:
            self.safe_log(f"Generating animation for: {tomogram_path}")

            # For now, show a placeholder message
            QMessageBox.information(
                self,
                "Animation Generator",
                f"Animation generation will start for:\n{os.path.basename(tomogram_path)}\n\n"
                "This will create a rotating 3D animation of the tomogram.",
            )

        except Exception as e:
            self.safe_log(
                f"ERROR: Failed to generate animation for {tomogram_path}: {e}"
            )
            QMessageBox.critical(
                self, "Animation Error", f"Failed to generate animation:\n{str(e)}"
            )
