#!/usr/bin/env python3
"""
AreTomo3 GUI Machine Learning Quality Predictor
AI-powered quality assessment and prediction for tomographic reconstructions.
"""

import logging
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
from datetime import datetime

# ML imports with fallbacks
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import joblib
    JOBLIB_AVAILABLE = True
except ImportError:
    JOBLIB_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class QualityMetrics:
    """Quality metrics for tomographic reconstruction."""
    resolution_score: float
    ctf_quality: float
    motion_correction_score: float
    alignment_quality: float
    overall_score: float
    confidence: float
    prediction_timestamp: datetime


@dataclass
class FeatureSet:
    """Feature set for ML quality prediction."""
    # CTF features
    avg_resolution: float
    resolution_std: float
    avg_defocus: float
    defocus_range: float
    avg_cross_correlation: float
    ctf_fit_quality: float
    
    # Motion features
    avg_motion_magnitude: float
    motion_std: float
    max_motion: float
    motion_trend: float
    
    # Alignment features
    alignment_rmse: float
    tilt_angle_coverage: float
    num_tilts: int
    
    # Acquisition features
    pixel_size: float
    voltage: float
    dose_rate: float
    total_dose: float


class QualityPredictor:
    """
    Machine learning-based quality predictor for tomographic reconstructions.
    Uses ensemble methods to predict reconstruction quality from processing metrics.
    """
    
    def __init__(self, model_dir: Path = None):
        """Initialize the quality predictor."""
        self.model_dir = model_dir or Path.home() / ".aretomo3_gui" / "ml_models"
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # ML models
        self.resolution_model = None
        self.overall_quality_model = None
        self.scaler = None
        
        # Training data
        self.training_data = []
        self.feature_names = []
        
        # Model performance metrics
        self.model_metrics = {
            'resolution_r2': 0.0,
            'overall_r2': 0.0,
            'last_training': None,
            'training_samples': 0
        }
        
        # Initialize models if available
        if SKLEARN_AVAILABLE:
            self._initialize_models()
            self._load_models()
        
        logger.info(f"Quality Predictor initialized - ML available: {SKLEARN_AVAILABLE}")
    
    def _initialize_models(self):
        """Initialize ML models."""
        try:
            # Resolution prediction model
            self.resolution_model = GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            
            # Overall quality prediction model
            self.overall_quality_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=8,
                random_state=42
            )
            
            # Feature scaler
            self.scaler = StandardScaler()
            
            # Define feature names
            self.feature_names = [
                'avg_resolution', 'resolution_std', 'avg_defocus', 'defocus_range',
                'avg_cross_correlation', 'ctf_fit_quality', 'avg_motion_magnitude',
                'motion_std', 'max_motion', 'motion_trend', 'alignment_rmse',
                'tilt_angle_coverage', 'num_tilts', 'pixel_size', 'voltage',
                'dose_rate', 'total_dose'
            ]
            
            logger.info("ML models initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing ML models: {e}")
    
    def extract_features(self, analysis_data: Dict[str, Any]) -> Optional[FeatureSet]:
        """Extract features from analysis data for ML prediction."""
        try:
            features = {}
            
            # Extract CTF features
            if 'aretomo3_data' in analysis_data and 'ctf_data' in analysis_data['aretomo3_data']:
                ctf_data = analysis_data['aretomo3_data']['ctf_data']
                
                if 'parameters' in ctf_data and not ctf_data['parameters'].empty:
                    df = ctf_data['parameters']
                    
                    features.update({
                        'avg_resolution': df['resolution_limit_A'].mean(),
                        'resolution_std': df['resolution_limit_A'].std(),
                        'avg_defocus': df['defocus1_A'].mean() / 10000,  # Convert to μm
                        'defocus_range': (df['defocus1_A'].max() - df['defocus1_A'].min()) / 10000,
                        'avg_cross_correlation': df['cross_correlation'].mean(),
                        'ctf_fit_quality': df['cross_correlation'].std()  # Lower std = better fit
                    })
            
            # Extract motion features
            if 'aretomo3_data' in analysis_data and 'motion_data' in analysis_data['aretomo3_data']:
                motion_data = analysis_data['aretomo3_data']['motion_data']
                
                if 'motion_vectors' in motion_data:
                    motion_vectors = motion_data['motion_vectors']
                    magnitudes = [np.sqrt(mv['x']**2 + mv['y']**2) for mv in motion_vectors]
                    
                    features.update({
                        'avg_motion_magnitude': np.mean(magnitudes),
                        'motion_std': np.std(magnitudes),
                        'max_motion': np.max(magnitudes),
                        'motion_trend': self._calculate_motion_trend(magnitudes)
                    })
            
            # Extract alignment features
            if 'aretomo3_data' in analysis_data and 'alignment_data' in analysis_data['aretomo3_data']:
                alignment_data = analysis_data['aretomo3_data']['alignment_data']
                
                if 'parameters' in alignment_data and not alignment_data['parameters'].empty:
                    df = alignment_data['parameters']
                    
                    features.update({
                        'alignment_rmse': np.sqrt(df['trans_x']**2 + df['trans_y']**2).mean(),
                        'tilt_angle_coverage': df['tilt_angle'].max() - df['tilt_angle'].min(),
                        'num_tilts': len(df)
                    })
            
            # Extract acquisition features (from metadata or defaults)
            metadata = analysis_data.get('metadata', {})
            features.update({
                'pixel_size': metadata.get('pixel_size', 1.0),
                'voltage': metadata.get('voltage', 300),
                'dose_rate': metadata.get('dose_rate', 1.0),
                'total_dose': metadata.get('total_dose', 50.0)
            })
            
            # Fill missing features with defaults
            for feature_name in self.feature_names:
                if feature_name not in features:
                    features[feature_name] = 0.0
            
            return FeatureSet(**features)
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return None
    
    def _calculate_motion_trend(self, magnitudes: List[float]) -> float:
        """Calculate motion trend (increasing/decreasing over time)."""
        if len(magnitudes) < 2:
            return 0.0
        
        # Simple linear trend calculation
        x = np.arange(len(magnitudes))
        y = np.array(magnitudes)
        
        # Calculate slope
        slope = np.polyfit(x, y, 1)[0]
        return slope
    
    def predict_quality(self, analysis_data: Dict[str, Any]) -> Optional[QualityMetrics]:
        """Predict quality metrics from analysis data."""
        if not SKLEARN_AVAILABLE or not self.resolution_model:
            logger.warning("ML models not available for quality prediction")
            return self._fallback_quality_assessment(analysis_data)
        
        try:
            # Extract features
            features = self.extract_features(analysis_data)
            if not features:
                return None
            
            # Convert to feature vector
            feature_vector = np.array([getattr(features, name) for name in self.feature_names]).reshape(1, -1)
            
            # Scale features
            if self.scaler:
                feature_vector = self.scaler.transform(feature_vector)
            
            # Predict resolution
            resolution_score = self.resolution_model.predict(feature_vector)[0]
            
            # Predict overall quality
            overall_score = self.overall_quality_model.predict(feature_vector)[0]
            
            # Calculate component scores
            ctf_quality = self._calculate_ctf_quality(features)
            motion_score = self._calculate_motion_score(features)
            alignment_quality = self._calculate_alignment_quality(features)
            
            # Calculate confidence based on model performance
            confidence = min(self.model_metrics['resolution_r2'], self.model_metrics['overall_r2'])
            
            return QualityMetrics(
                resolution_score=max(0, min(100, resolution_score)),
                ctf_quality=max(0, min(100, ctf_quality)),
                motion_correction_score=max(0, min(100, motion_score)),
                alignment_quality=max(0, min(100, alignment_quality)),
                overall_score=max(0, min(100, overall_score)),
                confidence=max(0, min(1, confidence)),
                prediction_timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error predicting quality: {e}")
            return self._fallback_quality_assessment(analysis_data)
    
    def _fallback_quality_assessment(self, analysis_data: Dict[str, Any]) -> QualityMetrics:
        """Fallback quality assessment when ML is not available."""
        try:
            # Simple rule-based quality assessment
            resolution_score = 75.0  # Default
            ctf_quality = 75.0
            motion_score = 75.0
            alignment_quality = 75.0
            
            # Analyze CTF data if available
            if 'aretomo3_data' in analysis_data and 'ctf_data' in analysis_data['aretomo3_data']:
                ctf_data = analysis_data['aretomo3_data']['ctf_data']
                if 'parameters' in ctf_data and not ctf_data['parameters'].empty:
                    df = ctf_data['parameters']
                    avg_resolution = df['resolution_limit_A'].mean()
                    avg_cc = df['cross_correlation'].mean()
                    
                    # Simple scoring
                    resolution_score = max(0, min(100, 100 - (avg_resolution - 3) * 10))
                    ctf_quality = avg_cc * 100
            
            overall_score = np.mean([resolution_score, ctf_quality, motion_score, alignment_quality])
            
            return QualityMetrics(
                resolution_score=resolution_score,
                ctf_quality=ctf_quality,
                motion_correction_score=motion_score,
                alignment_quality=alignment_quality,
                overall_score=overall_score,
                confidence=0.5,  # Lower confidence for rule-based
                prediction_timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error in fallback quality assessment: {e}")
            return QualityMetrics(
                resolution_score=50.0,
                ctf_quality=50.0,
                motion_correction_score=50.0,
                alignment_quality=50.0,
                overall_score=50.0,
                confidence=0.3,
                prediction_timestamp=datetime.now()
            )
    
    def _calculate_ctf_quality(self, features: FeatureSet) -> float:
        """Calculate CTF quality score from features."""
        # Higher cross-correlation and lower resolution = better quality
        cc_score = features.avg_cross_correlation * 100
        resolution_score = max(0, 100 - (features.avg_resolution - 3) * 10)
        return (cc_score + resolution_score) / 2
    
    def _calculate_motion_score(self, features: FeatureSet) -> float:
        """Calculate motion correction score from features."""
        # Lower motion magnitude = better score
        motion_score = max(0, 100 - features.avg_motion_magnitude * 10)
        return motion_score
    
    def _calculate_alignment_quality(self, features: FeatureSet) -> float:
        """Calculate alignment quality score from features."""
        # Lower RMSE and good tilt coverage = better score
        rmse_score = max(0, 100 - features.alignment_rmse * 20)
        coverage_score = min(100, features.tilt_angle_coverage / 120 * 100)
        return (rmse_score + coverage_score) / 2
    
    def add_training_sample(self, features: FeatureSet, quality_scores: Dict[str, float]):
        """Add a training sample for model improvement."""
        try:
            sample = {
                'features': features,
                'resolution_score': quality_scores.get('resolution', 0),
                'overall_score': quality_scores.get('overall', 0),
                'timestamp': datetime.now()
            }
            
            self.training_data.append(sample)
            logger.info(f"Added training sample - total samples: {len(self.training_data)}")
            
        except Exception as e:
            logger.error(f"Error adding training sample: {e}")
    
    def retrain_models(self) -> bool:
        """Retrain models with accumulated training data."""
        if not SKLEARN_AVAILABLE or len(self.training_data) < 10:
            logger.warning("Insufficient data or ML not available for retraining")
            return False
        
        try:
            # Prepare training data
            X = []
            y_resolution = []
            y_overall = []
            
            for sample in self.training_data:
                features = sample['features']
                feature_vector = [getattr(features, name) for name in self.feature_names]
                X.append(feature_vector)
                y_resolution.append(sample['resolution_score'])
                y_overall.append(sample['overall_score'])
            
            X = np.array(X)
            y_resolution = np.array(y_resolution)
            y_overall = np.array(y_overall)
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train models
            self.resolution_model.fit(X_scaled, y_resolution)
            self.overall_quality_model.fit(X_scaled, y_overall)
            
            # Evaluate models
            resolution_r2 = cross_val_score(self.resolution_model, X_scaled, y_resolution, cv=5).mean()
            overall_r2 = cross_val_score(self.overall_quality_model, X_scaled, y_overall, cv=5).mean()
            
            # Update metrics
            self.model_metrics.update({
                'resolution_r2': resolution_r2,
                'overall_r2': overall_r2,
                'last_training': datetime.now(),
                'training_samples': len(self.training_data)
            })
            
            # Save models
            self._save_models()
            
            logger.info(f"Models retrained - Resolution R²: {resolution_r2:.3f}, Overall R²: {overall_r2:.3f}")
            return True
            
        except Exception as e:
            logger.error(f"Error retraining models: {e}")
            return False
    
    def _save_models(self):
        """Save trained models to disk."""
        if not JOBLIB_AVAILABLE:
            return
        
        try:
            model_files = {
                'resolution_model.pkl': self.resolution_model,
                'overall_model.pkl': self.overall_quality_model,
                'scaler.pkl': self.scaler
            }
            
            for filename, model in model_files.items():
                if model:
                    joblib.dump(model, self.model_dir / filename)
            
            # Save metrics
            with open(self.model_dir / 'metrics.json', 'w') as f:
                metrics_serializable = self.model_metrics.copy()
                if metrics_serializable['last_training']:
                    metrics_serializable['last_training'] = metrics_serializable['last_training'].isoformat()
                json.dump(metrics_serializable, f, indent=2)
            
            logger.info("Models saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")
    
    def _load_models(self):
        """Load trained models from disk."""
        if not JOBLIB_AVAILABLE:
            return
        
        try:
            model_files = {
                'resolution_model.pkl': 'resolution_model',
                'overall_model.pkl': 'overall_quality_model',
                'scaler.pkl': 'scaler'
            }
            
            for filename, attr_name in model_files.items():
                model_path = self.model_dir / filename
                if model_path.exists():
                    setattr(self, attr_name, joblib.load(model_path))
            
            # Load metrics
            metrics_path = self.model_dir / 'metrics.json'
            if metrics_path.exists():
                with open(metrics_path, 'r') as f:
                    metrics = json.load(f)
                    if metrics.get('last_training'):
                        metrics['last_training'] = datetime.fromisoformat(metrics['last_training'])
                    self.model_metrics.update(metrics)
            
            logger.info("Models loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the ML models."""
        return {
            'ml_available': SKLEARN_AVAILABLE,
            'models_trained': self.resolution_model is not None,
            'training_samples': len(self.training_data),
            'model_metrics': self.model_metrics,
            'feature_names': self.feature_names
        }


# Global quality predictor instance
quality_predictor = QualityPredictor()


def predict_reconstruction_quality(analysis_data: Dict[str, Any]) -> Optional[QualityMetrics]:
    """Convenience function to predict reconstruction quality."""
    return quality_predictor.predict_quality(analysis_data)
