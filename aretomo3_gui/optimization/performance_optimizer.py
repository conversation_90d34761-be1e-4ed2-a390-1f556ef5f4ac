#!/usr/bin/env python3
"""
AreTomo3 GUI Performance Optimization Engine
Advanced performance monitoring, analysis, and optimization for tomographic processing.
"""

import logging
import psutil
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import queue
import numpy as np

# GPU monitoring imports with fallbacks
try:
    import GPUtil
    GPU_MONITORING_AVAILABLE = True
except ImportError:
    GPU_MONITORING_AVAILABLE = False

try:
    import nvidia_ml_py3 as nvml
    NVML_AVAILABLE = True
except ImportError:
    NVML_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics snapshot."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_available_gb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    gpu_utilization: List[float]
    gpu_memory_used: List[float]
    gpu_memory_total: List[float]
    gpu_temperature: List[float]
    process_count: int
    load_average: List[float]


@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation."""
    category: str
    priority: str  # low, medium, high, critical
    title: str
    description: str
    impact: str
    implementation: str
    estimated_improvement: str


@dataclass
class PerformanceProfile:
    """Performance profile for different workloads."""
    profile_name: str
    cpu_threads: int
    memory_limit_gb: float
    gpu_memory_fraction: float
    io_priority: str
    process_priority: str
    cache_size_mb: int
    batch_size: int
    optimization_flags: Dict[str, Any]


class PerformanceOptimizer:
    """
    Advanced performance optimization engine for AreTomo3 GUI.
    Monitors system performance and provides intelligent optimization recommendations.
    """
    
    def __init__(self, monitoring_interval: float = 1.0):
        """Initialize the performance optimizer."""
        self.monitoring_interval = monitoring_interval
        
        # Performance monitoring
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Optimization
        self.recommendations: List[OptimizationRecommendation] = []
        self.performance_profiles: Dict[str, PerformanceProfile] = {}
        self.active_profile: Optional[str] = None
        
        # Thresholds for alerts
        self.thresholds = {
            'cpu_high': 80.0,
            'memory_high': 85.0,
            'gpu_high': 90.0,
            'temperature_high': 80.0,
            'disk_io_high': 100.0  # MB/s
        }
        
        # Performance callbacks
        self.performance_callbacks: List[Callable] = []
        
        # Configuration
        self.config_dir = Path.home() / ".aretomo3_gui" / "performance"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.config_file = self.config_dir / "performance_config.json"
        
        # Initialize GPU monitoring
        self._initialize_gpu_monitoring()
        
        # Load default profiles
        self._create_default_profiles()
        
        # Load configuration
        self._load_config()
        
        logger.info("Performance Optimizer initialized")
    
    def _initialize_gpu_monitoring(self):
        """Initialize GPU monitoring capabilities."""
        try:
            if NVML_AVAILABLE:
                nvml.nvmlInit()
                self.gpu_count = nvml.nvmlDeviceGetCount()
                logger.info(f"NVML initialized - {self.gpu_count} GPUs detected")
            elif GPU_MONITORING_AVAILABLE:
                self.gpu_count = len(GPUtil.getGPUs())
                logger.info(f"GPUtil initialized - {self.gpu_count} GPUs detected")
            else:
                self.gpu_count = 0
                logger.info("No GPU monitoring available")
                
        except Exception as e:
            logger.error(f"Error initializing GPU monitoring: {e}")
            self.gpu_count = 0
    
    def start_monitoring(self):
        """Start performance monitoring."""
        if self.monitoring_active:
            logger.warning("Performance monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Limit history size
                if len(self.metrics_history) > 3600:  # Keep 1 hour at 1s intervals
                    self.metrics_history = self.metrics_history[-3600:]
                
                # Check for performance issues
                self._analyze_performance(metrics)
                
                # Notify callbacks
                for callback in self.performance_callbacks:
                    try:
                        callback(metrics)
                    except Exception as e:
                        logger.error(f"Error in performance callback: {e}")
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = disk_io.read_bytes / (1024 * 1024) if disk_io else 0
            disk_write_mb = disk_io.write_bytes / (1024 * 1024) if disk_io else 0
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_sent_mb = network_io.bytes_sent / (1024 * 1024) if network_io else 0
            network_recv_mb = network_io.bytes_recv / (1024 * 1024) if network_io else 0
            
            # Load average (Unix-like systems)
            try:
                load_avg = list(psutil.getloadavg())
            except AttributeError:
                load_avg = [0.0, 0.0, 0.0]
            
            # GPU metrics
            gpu_utilization = []
            gpu_memory_used = []
            gpu_memory_total = []
            gpu_temperature = []
            
            if self.gpu_count > 0:
                gpu_utilization, gpu_memory_used, gpu_memory_total, gpu_temperature = self._collect_gpu_metrics()
            
            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_gb=memory.used / (1024**3),
                memory_available_gb=memory.available / (1024**3),
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                gpu_utilization=gpu_utilization,
                gpu_memory_used=gpu_memory_used,
                gpu_memory_total=gpu_memory_total,
                gpu_temperature=gpu_temperature,
                process_count=len(psutil.pids()),
                load_average=load_avg
            )
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0, memory_percent=0.0, memory_used_gb=0.0,
                memory_available_gb=0.0, disk_io_read_mb=0.0, disk_io_write_mb=0.0,
                network_sent_mb=0.0, network_recv_mb=0.0, gpu_utilization=[],
                gpu_memory_used=[], gpu_memory_total=[], gpu_temperature=[],
                process_count=0, load_average=[0.0, 0.0, 0.0]
            )
    
    def _collect_gpu_metrics(self) -> tuple:
        """Collect GPU performance metrics."""
        utilization = []
        memory_used = []
        memory_total = []
        temperature = []
        
        try:
            if NVML_AVAILABLE:
                for i in range(self.gpu_count):
                    handle = nvml.nvmlDeviceGetHandleByIndex(i)
                    
                    # GPU utilization
                    util = nvml.nvmlDeviceGetUtilizationRates(handle)
                    utilization.append(util.gpu)
                    
                    # Memory info
                    mem_info = nvml.nvmlDeviceGetMemoryInfo(handle)
                    memory_used.append(mem_info.used / (1024**3))  # GB
                    memory_total.append(mem_info.total / (1024**3))  # GB
                    
                    # Temperature
                    temp = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)
                    temperature.append(temp)
                    
            elif GPU_MONITORING_AVAILABLE:
                gpus = GPUtil.getGPUs()
                for gpu in gpus:
                    utilization.append(gpu.load * 100)
                    memory_used.append(gpu.memoryUsed / 1024)  # GB
                    memory_total.append(gpu.memoryTotal / 1024)  # GB
                    temperature.append(gpu.temperature)
                    
        except Exception as e:
            logger.error(f"Error collecting GPU metrics: {e}")
        
        return utilization, memory_used, memory_total, temperature
    
    def _analyze_performance(self, metrics: PerformanceMetrics):
        """Analyze performance metrics and generate recommendations."""
        try:
            # Clear old recommendations
            self.recommendations.clear()
            
            # CPU analysis
            if metrics.cpu_percent > self.thresholds['cpu_high']:
                self.recommendations.append(OptimizationRecommendation(
                    category="CPU",
                    priority="high",
                    title="High CPU Usage Detected",
                    description=f"CPU usage is at {metrics.cpu_percent:.1f}%, which may slow down processing.",
                    impact="Processing speed may be reduced",
                    implementation="Consider reducing parallel processes or upgrading CPU",
                    estimated_improvement="20-40% speed increase"
                ))
            
            # Memory analysis
            if metrics.memory_percent > self.thresholds['memory_high']:
                self.recommendations.append(OptimizationRecommendation(
                    category="Memory",
                    priority="high",
                    title="High Memory Usage Detected",
                    description=f"Memory usage is at {metrics.memory_percent:.1f}%, risk of swapping.",
                    impact="System may become unresponsive",
                    implementation="Close unnecessary applications or add more RAM",
                    estimated_improvement="Prevent system slowdown"
                ))
            
            # GPU analysis
            for i, gpu_util in enumerate(metrics.gpu_utilization):
                if gpu_util > self.thresholds['gpu_high']:
                    self.recommendations.append(OptimizationRecommendation(
                        category="GPU",
                        priority="medium",
                        title=f"High GPU {i} Usage",
                        description=f"GPU {i} usage is at {gpu_util:.1f}%",
                        impact="GPU processing may be bottlenecked",
                        implementation="Optimize GPU workload distribution",
                        estimated_improvement="10-30% GPU efficiency"
                    ))
            
            # Temperature analysis
            for i, temp in enumerate(metrics.gpu_temperature):
                if temp > self.thresholds['temperature_high']:
                    self.recommendations.append(OptimizationRecommendation(
                        category="Thermal",
                        priority="critical",
                        title=f"High GPU {i} Temperature",
                        description=f"GPU {i} temperature is {temp}°C",
                        impact="Risk of thermal throttling or hardware damage",
                        implementation="Improve cooling or reduce workload",
                        estimated_improvement="Prevent performance degradation"
                    ))
            
            # Disk I/O analysis
            total_disk_io = metrics.disk_io_read_mb + metrics.disk_io_write_mb
            if total_disk_io > self.thresholds['disk_io_high']:
                self.recommendations.append(OptimizationRecommendation(
                    category="Storage",
                    priority="medium",
                    title="High Disk I/O Activity",
                    description=f"Disk I/O is at {total_disk_io:.1f} MB/s",
                    impact="Storage may be bottlenecking performance",
                    implementation="Consider SSD upgrade or optimize data access patterns",
                    estimated_improvement="2-10x I/O performance"
                ))
            
        except Exception as e:
            logger.error(f"Error analyzing performance: {e}")
    
    def _create_default_profiles(self):
        """Create default performance profiles."""
        self.performance_profiles = {
            'balanced': PerformanceProfile(
                profile_name="Balanced",
                cpu_threads=psutil.cpu_count(),
                memory_limit_gb=psutil.virtual_memory().total / (1024**3) * 0.8,
                gpu_memory_fraction=0.8,
                io_priority="normal",
                process_priority="normal",
                cache_size_mb=1024,
                batch_size=16,
                optimization_flags={'use_gpu': True, 'parallel_processing': True}
            ),
            'performance': PerformanceProfile(
                profile_name="High Performance",
                cpu_threads=psutil.cpu_count(),
                memory_limit_gb=psutil.virtual_memory().total / (1024**3) * 0.9,
                gpu_memory_fraction=0.95,
                io_priority="high",
                process_priority="high",
                cache_size_mb=2048,
                batch_size=32,
                optimization_flags={'use_gpu': True, 'parallel_processing': True, 'aggressive_caching': True}
            ),
            'memory_conservative': PerformanceProfile(
                profile_name="Memory Conservative",
                cpu_threads=max(1, psutil.cpu_count() // 2),
                memory_limit_gb=psutil.virtual_memory().total / (1024**3) * 0.6,
                gpu_memory_fraction=0.6,
                io_priority="normal",
                process_priority="normal",
                cache_size_mb=512,
                batch_size=8,
                optimization_flags={'use_gpu': True, 'parallel_processing': False}
            )
        }
    
    def apply_performance_profile(self, profile_name: str) -> bool:
        """Apply a performance profile."""
        if profile_name not in self.performance_profiles:
            logger.error(f"Performance profile not found: {profile_name}")
            return False
        
        try:
            profile = self.performance_profiles[profile_name]
            self.active_profile = profile_name
            
            # Apply profile settings (this would integrate with actual processing components)
            logger.info(f"Applied performance profile: {profile_name}")
            
            # Log the profile application
            self._log_performance_event(f"Applied performance profile: {profile_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying performance profile: {e}")
            return False
    
    def get_performance_summary(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """Get performance summary for the specified duration."""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
            recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
            
            if not recent_metrics:
                return {"error": "No recent metrics available"}
            
            # Calculate averages
            avg_cpu = np.mean([m.cpu_percent for m in recent_metrics])
            avg_memory = np.mean([m.memory_percent for m in recent_metrics])
            avg_gpu = np.mean([np.mean(m.gpu_utilization) for m in recent_metrics if m.gpu_utilization])
            
            # Calculate peaks
            peak_cpu = max([m.cpu_percent for m in recent_metrics])
            peak_memory = max([m.memory_percent for m in recent_metrics])
            peak_gpu = max([max(m.gpu_utilization) for m in recent_metrics if m.gpu_utilization] or [0])
            
            return {
                'duration_minutes': duration_minutes,
                'sample_count': len(recent_metrics),
                'averages': {
                    'cpu_percent': avg_cpu,
                    'memory_percent': avg_memory,
                    'gpu_utilization': avg_gpu
                },
                'peaks': {
                    'cpu_percent': peak_cpu,
                    'memory_percent': peak_memory,
                    'gpu_utilization': peak_gpu
                },
                'recommendations_count': len(self.recommendations),
                'active_profile': self.active_profile
            }
            
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {"error": str(e)}
    
    def get_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Get current optimization recommendations."""
        return self.recommendations.copy()
    
    def add_performance_callback(self, callback: Callable):
        """Add a callback for performance updates."""
        self.performance_callbacks.append(callback)
    
    def remove_performance_callback(self, callback: Callable):
        """Remove a performance callback."""
        if callback in self.performance_callbacks:
            self.performance_callbacks.remove(callback)
    
    def _log_performance_event(self, message: str):
        """Log performance-related events."""
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'event': 'performance',
                'message': message
            }
            
            log_file = self.config_dir / "performance.log"
            with open(log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
                
        except Exception as e:
            logger.error(f"Error logging performance event: {e}")
    
    def _load_config(self):
        """Load performance configuration."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                
                # Load thresholds
                if 'thresholds' in config:
                    self.thresholds.update(config['thresholds'])
                
                # Load active profile
                if 'active_profile' in config:
                    self.active_profile = config['active_profile']
                
        except Exception as e:
            logger.error(f"Error loading performance config: {e}")
    
    def _save_config(self):
        """Save performance configuration."""
        try:
            config = {
                'thresholds': self.thresholds,
                'active_profile': self.active_profile,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving performance config: {e}")


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()


def start_performance_monitoring():
    """Convenience function to start performance monitoring."""
    performance_optimizer.start_monitoring()


def get_current_performance() -> Optional[PerformanceMetrics]:
    """Convenience function to get current performance metrics."""
    if performance_optimizer.metrics_history:
        return performance_optimizer.metrics_history[-1]
    return None
