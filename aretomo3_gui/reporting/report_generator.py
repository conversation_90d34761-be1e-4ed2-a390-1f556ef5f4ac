#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Reporting System
Comprehensive report generation for tomographic processing results.
"""

import logging
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import base64
import io

# Report generation imports
try:
    from jinja2 import Template, Environment, FileSystemLoader
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ReportSection:
    """Report section structure."""
    title: str
    content: str
    plots: List[str] = None
    tables: List[Dict[str, Any]] = None
    subsections: List['ReportSection'] = None


@dataclass
class ProcessingReport:
    """Complete processing report structure."""
    title: str
    timestamp: datetime
    summary: Dict[str, Any]
    sections: List[ReportSection]
    metadata: Dict[str, Any]
    quality_metrics: Dict[str, Any] = None


class ReportGenerator:
    """
    Advanced report generator for AreTomo3 GUI.
    Creates comprehensive HTML and PDF reports from processing results.
    """
    
    def __init__(self, template_dir: Path = None, output_dir: Path = None):
        """Initialize the report generator."""
        self.template_dir = template_dir or Path(__file__).parent / "templates"
        self.output_dir = output_dir or Path.cwd() / "reports"
        
        # Create directories
        self.template_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize Jinja2 environment
        if JINJA2_AVAILABLE:
            self.jinja_env = Environment(
                loader=FileSystemLoader(str(self.template_dir)),
                autoescape=True
            )
        else:
            self.jinja_env = None
        
        # Report templates
        self.templates = {
            'processing_report': self._get_processing_template(),
            'quality_report': self._get_quality_template(),
            'batch_report': self._get_batch_template()
        }
        
        # Create default templates if they don't exist
        self._create_default_templates()
        
        logger.info(f"Report Generator initialized - Templates: {JINJA2_AVAILABLE}, PDF: {WEASYPRINT_AVAILABLE}")
    
    def generate_processing_report(self, analysis_data: Dict[str, Any], 
                                 output_format: str = "html") -> Optional[Path]:
        """Generate comprehensive processing report."""
        try:
            # Extract report data
            report_data = self._extract_report_data(analysis_data)
            
            # Create report structure
            report = ProcessingReport(
                title=f"AreTomo3 Processing Report - {report_data['series_name']}",
                timestamp=datetime.now(),
                summary=report_data['summary'],
                sections=self._create_report_sections(report_data),
                metadata=report_data['metadata'],
                quality_metrics=report_data.get('quality_metrics')
            )
            
            # Generate report
            if output_format.lower() == "pdf" and WEASYPRINT_AVAILABLE:
                return self._generate_pdf_report(report)
            else:
                return self._generate_html_report(report)
                
        except Exception as e:
            logger.error(f"Error generating processing report: {e}")
            return None
    
    def _extract_report_data(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and organize data for report generation."""
        report_data = {
            'series_name': 'Unknown',
            'summary': {},
            'metadata': {},
            'quality_metrics': {}
        }
        
        try:
            # Extract series information
            if 'aretomo3_data' in analysis_data:
                aretomo3_data = analysis_data['aretomo3_data']
                
                # Series name
                if 'series_name' in aretomo3_data:
                    report_data['series_name'] = aretomo3_data['series_name']
                
                # CTF data summary
                if 'ctf_data' in aretomo3_data:
                    ctf_data = aretomo3_data['ctf_data']
                    if 'parameters' in ctf_data and not ctf_data['parameters'].empty:
                        df = ctf_data['parameters']
                        report_data['summary']['ctf'] = {
                            'num_micrographs': len(df),
                            'avg_resolution': df['resolution_limit_A'].mean(),
                            'resolution_range': [df['resolution_limit_A'].min(), df['resolution_limit_A'].max()],
                            'avg_defocus': df['defocus1_A'].mean() / 10000,
                            'avg_cross_correlation': df['cross_correlation'].mean()
                        }
                
                # Motion data summary
                if 'motion_data' in aretomo3_data:
                    motion_data = aretomo3_data['motion_data']
                    if 'motion_vectors' in motion_data:
                        motion_vectors = motion_data['motion_vectors']
                        magnitudes = [np.sqrt(mv['x']**2 + mv['y']**2) for mv in motion_vectors]
                        report_data['summary']['motion'] = {
                            'num_frames': len(motion_vectors),
                            'avg_motion': np.mean(magnitudes),
                            'max_motion': np.max(magnitudes),
                            'total_drift': np.sum(magnitudes)
                        }
                
                # Alignment data summary
                if 'alignment_data' in aretomo3_data:
                    alignment_data = aretomo3_data['alignment_data']
                    if 'parameters' in alignment_data and not alignment_data['parameters'].empty:
                        df = alignment_data['parameters']
                        report_data['summary']['alignment'] = {
                            'num_tilts': len(df),
                            'tilt_range': [df['tilt_angle'].min(), df['tilt_angle'].max()],
                            'avg_rotation': df['rotation'].mean(),
                            'avg_translation': np.sqrt(df['trans_x']**2 + df['trans_y']**2).mean()
                        }
            
            # Extract metadata
            if 'metadata' in analysis_data:
                report_data['metadata'] = analysis_data['metadata']
            
            # Extract quality metrics if available
            if 'quality_metrics' in analysis_data:
                report_data['quality_metrics'] = analysis_data['quality_metrics']
            
        except Exception as e:
            logger.error(f"Error extracting report data: {e}")
        
        return report_data
    
    def _create_report_sections(self, report_data: Dict[str, Any]) -> List[ReportSection]:
        """Create report sections from data."""
        sections = []
        
        try:
            # Executive Summary
            summary_content = self._create_summary_content(report_data['summary'])
            sections.append(ReportSection(
                title="Executive Summary",
                content=summary_content
            ))
            
            # CTF Analysis Section
            if 'ctf' in report_data['summary']:
                ctf_content = self._create_ctf_content(report_data['summary']['ctf'])
                sections.append(ReportSection(
                    title="CTF Analysis",
                    content=ctf_content
                ))
            
            # Motion Correction Section
            if 'motion' in report_data['summary']:
                motion_content = self._create_motion_content(report_data['summary']['motion'])
                sections.append(ReportSection(
                    title="Motion Correction",
                    content=motion_content
                ))
            
            # Alignment Section
            if 'alignment' in report_data['summary']:
                alignment_content = self._create_alignment_content(report_data['summary']['alignment'])
                sections.append(ReportSection(
                    title="Tilt Series Alignment",
                    content=alignment_content
                ))
            
            # Quality Assessment Section
            if report_data.get('quality_metrics'):
                quality_content = self._create_quality_content(report_data['quality_metrics'])
                sections.append(ReportSection(
                    title="Quality Assessment",
                    content=quality_content
                ))
            
            # Technical Details Section
            tech_content = self._create_technical_content(report_data['metadata'])
            sections.append(ReportSection(
                title="Technical Details",
                content=tech_content
            ))
            
        except Exception as e:
            logger.error(f"Error creating report sections: {e}")
        
        return sections
    
    def _create_summary_content(self, summary: Dict[str, Any]) -> str:
        """Create executive summary content."""
        content = "<h3>Processing Overview</h3>\n"
        
        if 'ctf' in summary:
            ctf = summary['ctf']
            content += f"<p><strong>CTF Analysis:</strong> Processed {ctf['num_micrographs']} micrographs "
            content += f"with average resolution of {ctf['avg_resolution']:.1f} Å.</p>\n"
        
        if 'motion' in summary:
            motion = summary['motion']
            content += f"<p><strong>Motion Correction:</strong> Corrected {motion['num_frames']} frames "
            content += f"with average motion of {motion['avg_motion']:.2f} pixels.</p>\n"
        
        if 'alignment' in summary:
            alignment = summary['alignment']
            content += f"<p><strong>Alignment:</strong> Aligned {alignment['num_tilts']} tilts "
            content += f"covering {alignment['tilt_range'][1] - alignment['tilt_range'][0]:.1f}° range.</p>\n"
        
        return content
    
    def _create_ctf_content(self, ctf_data: Dict[str, Any]) -> str:
        """Create CTF analysis content."""
        content = f"""
        <h4>CTF Estimation Results</h4>
        <ul>
            <li><strong>Micrographs processed:</strong> {ctf_data['num_micrographs']}</li>
            <li><strong>Average resolution:</strong> {ctf_data['avg_resolution']:.1f} Å</li>
            <li><strong>Resolution range:</strong> {ctf_data['resolution_range'][0]:.1f} - {ctf_data['resolution_range'][1]:.1f} Å</li>
            <li><strong>Average defocus:</strong> {ctf_data['avg_defocus']:.2f} μm</li>
            <li><strong>Average cross-correlation:</strong> {ctf_data['avg_cross_correlation']:.3f}</li>
        </ul>
        
        <h4>Quality Assessment</h4>
        <p>The CTF estimation shows {'excellent' if ctf_data['avg_resolution'] < 4 else 'good' if ctf_data['avg_resolution'] < 6 else 'acceptable'} 
        resolution with {'high' if ctf_data['avg_cross_correlation'] > 0.8 else 'moderate' if ctf_data['avg_cross_correlation'] > 0.6 else 'low'} 
        confidence in the fits.</p>
        """
        return content
    
    def _create_motion_content(self, motion_data: Dict[str, Any]) -> str:
        """Create motion correction content."""
        content = f"""
        <h4>Motion Correction Results</h4>
        <ul>
            <li><strong>Frames processed:</strong> {motion_data['num_frames']}</li>
            <li><strong>Average motion:</strong> {motion_data['avg_motion']:.2f} pixels</li>
            <li><strong>Maximum motion:</strong> {motion_data['max_motion']:.2f} pixels</li>
            <li><strong>Total drift:</strong> {motion_data['total_drift']:.2f} pixels</li>
        </ul>
        
        <h4>Motion Assessment</h4>
        <p>Motion correction was {'excellent' if motion_data['avg_motion'] < 1 else 'good' if motion_data['avg_motion'] < 2 else 'acceptable'} 
        with {'minimal' if motion_data['max_motion'] < 3 else 'moderate' if motion_data['max_motion'] < 5 else 'significant'} 
        maximum displacement observed.</p>
        """
        return content
    
    def _create_alignment_content(self, alignment_data: Dict[str, Any]) -> str:
        """Create alignment content."""
        content = f"""
        <h4>Tilt Series Alignment Results</h4>
        <ul>
            <li><strong>Tilts aligned:</strong> {alignment_data['num_tilts']}</li>
            <li><strong>Tilt range:</strong> {alignment_data['tilt_range'][0]:.1f}° to {alignment_data['tilt_range'][1]:.1f}°</li>
            <li><strong>Average rotation:</strong> {alignment_data['avg_rotation']:.3f}°</li>
            <li><strong>Average translation:</strong> {alignment_data['avg_translation']:.2f} pixels</li>
        </ul>
        
        <h4>Alignment Quality</h4>
        <p>The tilt series alignment shows {'excellent' if alignment_data['avg_translation'] < 2 else 'good' if alignment_data['avg_translation'] < 5 else 'acceptable'} 
        precision with {'optimal' if abs(alignment_data['tilt_range'][1] - alignment_data['tilt_range'][0]) > 100 else 'good'} 
        angular coverage.</p>
        """
        return content
    
    def _create_quality_content(self, quality_data: Dict[str, Any]) -> str:
        """Create quality assessment content."""
        content = f"""
        <h4>Overall Quality Metrics</h4>
        <ul>
            <li><strong>Resolution Score:</strong> {quality_data.get('resolution_score', 0):.1f}/100</li>
            <li><strong>CTF Quality:</strong> {quality_data.get('ctf_quality', 0):.1f}/100</li>
            <li><strong>Motion Score:</strong> {quality_data.get('motion_score', 0):.1f}/100</li>
            <li><strong>Alignment Quality:</strong> {quality_data.get('alignment_quality', 0):.1f}/100</li>
            <li><strong>Overall Score:</strong> {quality_data.get('overall_score', 0):.1f}/100</li>
        </ul>
        
        <h4>Recommendations</h4>
        <p>Based on the quality assessment, this reconstruction is 
        {'excellent and ready for analysis' if quality_data.get('overall_score', 0) > 80 else 
         'good with minor issues' if quality_data.get('overall_score', 0) > 60 else 
         'acceptable but may benefit from parameter optimization'}.</p>
        """
        return content
    
    def _create_technical_content(self, metadata: Dict[str, Any]) -> str:
        """Create technical details content."""
        content = f"""
        <h4>Processing Parameters</h4>
        <ul>
            <li><strong>Pixel Size:</strong> {metadata.get('pixel_size', 'N/A')} Å</li>
            <li><strong>Voltage:</strong> {metadata.get('voltage', 'N/A')} kV</li>
            <li><strong>Spherical Aberration:</strong> {metadata.get('cs', 'N/A')} mm</li>
            <li><strong>Amplitude Contrast:</strong> {metadata.get('amp_contrast', 'N/A')}</li>
        </ul>
        
        <h4>Processing Information</h4>
        <ul>
            <li><strong>Processing Date:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
            <li><strong>AreTomo3 GUI Version:</strong> 1.0.0</li>
            <li><strong>Processing Mode:</strong> {metadata.get('processing_mode', 'Standard')}</li>
        </ul>
        """
        return content
    
    def _generate_html_report(self, report: ProcessingReport) -> Path:
        """Generate HTML report."""
        try:
            # Create HTML content
            html_content = self._create_html_content(report)
            
            # Save to file
            timestamp = report.timestamp.strftime("%Y%m%d_%H%M%S")
            filename = f"aretomo3_report_{timestamp}.html"
            output_path = self.output_dir / filename
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML report generated: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return None
    
    def _generate_pdf_report(self, report: ProcessingReport) -> Path:
        """Generate PDF report."""
        try:
            # Create HTML content
            html_content = self._create_html_content(report)
            
            # Convert to PDF
            timestamp = report.timestamp.strftime("%Y%m%d_%H%M%S")
            filename = f"aretomo3_report_{timestamp}.pdf"
            output_path = self.output_dir / filename
            
            HTML(string=html_content).write_pdf(output_path)
            
            logger.info(f"PDF report generated: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {e}")
            return None
    
    def _create_html_content(self, report: ProcessingReport) -> str:
        """Create HTML content for report."""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{{ report.title }}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #2c3e50; border-bottom: 2px solid #3498db; }
                h2 { color: #34495e; margin-top: 30px; }
                h3 { color: #7f8c8d; }
                .summary { background-color: #ecf0f1; padding: 20px; border-radius: 5px; }
                .section { margin: 20px 0; }
                ul { line-height: 1.6; }
                .timestamp { color: #95a5a6; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <h1>{{ report.title }}</h1>
            <p class="timestamp">Generated: {{ report.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            
            {% for section in report.sections %}
            <div class="section">
                <h2>{{ section.title }}</h2>
                {{ section.content | safe }}
            </div>
            {% endfor %}
        </body>
        </html>
        """
        
        if JINJA2_AVAILABLE:
            template = Template(html_template)
            return template.render(report=report)
        else:
            # Simple string replacement fallback
            content = html_template.replace("{{ report.title }}", report.title)
            content = content.replace("{{ report.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}", 
                                    report.timestamp.strftime('%Y-%m-%d %H:%M:%S'))
            
            sections_html = ""
            for section in report.sections:
                sections_html += f'<div class="section"><h2>{section.title}</h2>{section.content}</div>'
            
            content = content.replace("{% for section in report.sections %}{{ section.title }}{{ section.content | safe }}{% endfor %}", 
                                    sections_html)
            
            return content
    
    def _get_processing_template(self) -> str:
        """Get processing report template."""
        return "processing_report.html"
    
    def _get_quality_template(self) -> str:
        """Get quality report template."""
        return "quality_report.html"
    
    def _get_batch_template(self) -> str:
        """Get batch report template."""
        return "batch_report.html"
    
    def _create_default_templates(self):
        """Create default report templates."""
        # This would create default Jinja2 templates
        # For now, we use the embedded template in _create_html_content
        pass


# Global report generator instance
report_generator = ReportGenerator()


def generate_processing_report(analysis_data: Dict[str, Any], 
                             output_format: str = "html") -> Optional[Path]:
    """Convenience function to generate processing report."""
    return report_generator.generate_processing_report(analysis_data, output_format)
