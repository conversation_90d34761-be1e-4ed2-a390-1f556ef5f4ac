#!/usr/bin/env python3
"""
Comprehensive Production Readiness Test Suite for AreTomo3 GUI
Tests every critical function for production deployment.
"""

import sys
import os
import traceback
import logging
from pathlib import Path
import time
import threading
import tempfile
import json

# Add src to path
sys.path.insert(0, 'src')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionTestSuite:
    """Comprehensive production readiness test suite."""
    
    def __init__(self):
        self.test_results = {}
        self.critical_failures = []
        self.warnings = []
        self.passed_tests = 0
        self.total_tests = 0
        
    def run_test(self, test_name, test_func):
        """Run a single test and record results."""
        self.total_tests += 1
        logger.info(f"🧪 Running test: {test_name}")
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                self.test_results[test_name] = {
                    'status': 'PASS',
                    'duration': end_time - start_time,
                    'message': 'Test passed successfully'
                }
                self.passed_tests += 1
                logger.info(f"✅ {test_name} - PASSED ({end_time - start_time:.2f}s)")
            else:
                self.test_results[test_name] = {
                    'status': 'FAIL',
                    'duration': end_time - start_time,
                    'message': 'Test failed'
                }
                self.critical_failures.append(test_name)
                logger.error(f"❌ {test_name} - FAILED")
                
        except Exception as e:
            self.test_results[test_name] = {
                'status': 'ERROR',
                'duration': 0,
                'message': str(e),
                'traceback': traceback.format_exc()
            }
            self.critical_failures.append(test_name)
            logger.error(f"💥 {test_name} - ERROR: {e}")
    
    def test_core_imports(self):
        """Test all critical imports."""
        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            from aretomo3_gui.web.api_server import AreTomo3WebAPI
            from aretomo3_gui.core.realtime_processor import RealTimeProcessor
            from aretomo3_gui.database.database_manager import DatabaseManager
            from aretomo3_gui.utils.file_utils import validate_safe_path
            return True
        except ImportError as e:
            logger.error(f"Import failed: {e}")
            return False
    
    def test_security_features(self):
        """Test security implementations."""
        try:
            from aretomo3_gui.utils.file_utils import validate_safe_path, sanitize_filename
            
            # Test path validation
            assert validate_safe_path("/safe/path") == True
            assert validate_safe_path("../../../etc/passwd") == False
            assert validate_safe_path("safe_file.txt") == True
            
            # Test filename sanitization
            assert sanitize_filename("safe_file.txt") == "safe_file.txt"
            assert sanitize_filename("../dangerous.txt") == "__dangerous.txt"
            assert sanitize_filename("file<>|?.txt") == "file_____.txt"
            
            return True
        except Exception as e:
            logger.error(f"Security test failed: {e}")
            return False
    
    def test_database_operations(self):
        """Test database security and operations."""
        try:
            from aretomo3_gui.database.database_manager import get_database_manager
            
            db = get_database_manager()
            
            # Test secure update operations
            test_updates = {
                'project_name': 'test_project',
                'status': 'completed',
                'invalid_column': 'should_be_ignored'  # Should be filtered out
            }
            
            # This should not raise an exception and should filter invalid columns
            # Note: We're testing the method exists and handles invalid input safely
            return hasattr(db, '_update_session_sqlite')
            
        except Exception as e:
            logger.error(f"Database test failed: {e}")
            return False
    
    def test_web_api_security(self):
        """Test web API security features."""
        try:
            from aretomo3_gui.web.api_server import AreTomo3WebAPI
            from aretomo3_gui.core.realtime_processor import RealTimeProcessor
            
            processor = RealTimeProcessor()
            api = AreTomo3WebAPI(processor)
            
            # Test API key generation
            api_key = api.generate_api_key("test_user")
            assert api_key.startswith("at3gui_test_user_")
            
            # Test API key validation
            assert api.validate_api_key(api_key) == True
            assert api.validate_api_key("invalid_key") == False
            
            # Test rate limiting structure
            assert hasattr(api, 'check_rate_limit')
            assert hasattr(api, 'rate_limits')
            
            return True
        except Exception as e:
            logger.error(f"Web API security test failed: {e}")
            return False
    
    def test_gui_initialization(self):
        """Test GUI can be initialized without errors."""
        try:
            # Import Qt first
            from PyQt6.QtWidgets import QApplication
            
            # Create QApplication if it doesn't exist
            if not QApplication.instance():
                app = QApplication(sys.argv)
            
            from aretomo3_gui.gui.main_window import AreTomoGUI
            
            # Try to create main window
            window = AreTomoGUI()
            
            # Test tab structure
            assert hasattr(window, 'tab_widget')
            assert window.tab_widget.count() >= 8  # Should have at least 8 tabs
            
            # Test professional tab names
            tab_names = []
            for i in range(window.tab_widget.count()):
                tab_names.append(window.tab_widget.tabText(i))
            
            expected_names = [
                "Project Setup", "Reconstruction Parameters", "Live Processing",
                "Batch Processing", "Data Analysis", "3D Viewer", 
                "Remote Dashboard", "System Logs"
            ]
            
            for expected in expected_names:
                if expected not in tab_names:
                    logger.warning(f"Expected tab '{expected}' not found in {tab_names}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"GUI initialization test failed: {e}")
            return False
    
    def test_file_operations(self):
        """Test file operations and path security."""
        try:
            from aretomo3_gui.utils.file_utils import get_file_info, get_directory_contents
            
            # Create temporary test file
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
                f.write("test content")
                temp_file = f.name
            
            try:
                # Test safe file info retrieval
                file_info = get_file_info(temp_file)
                assert file_info is not None
                assert 'filename' in file_info
                assert 'file_size' in file_info
                
                # Test directory contents (should be safe)
                temp_dir = os.path.dirname(temp_file)
                contents = get_directory_contents(temp_dir)
                assert isinstance(contents, list)
                
                # Test unsafe path rejection
                unsafe_info = get_file_info("../../../etc/passwd")
                assert unsafe_info is None  # Should be rejected
                
                return True
            finally:
                # Cleanup
                os.unlink(temp_file)
                
        except Exception as e:
            logger.error(f"File operations test failed: {e}")
            return False
    
    def test_memory_management(self):
        """Test memory management and resource cleanup."""
        try:
            import psutil
            import gc
            
            # Get initial memory usage
            process = psutil.Process()
            initial_memory = process.memory_info().rss
            
            # Create and destroy some objects
            test_objects = []
            for i in range(1000):
                test_objects.append({"data": "x" * 1000, "id": i})
            
            # Clear objects and force garbage collection
            test_objects.clear()
            gc.collect()
            
            # Check memory didn't grow excessively
            final_memory = process.memory_info().rss
            memory_growth = final_memory - initial_memory
            
            # Allow up to 50MB growth for test objects
            if memory_growth > 50 * 1024 * 1024:
                logger.warning(f"Memory growth: {memory_growth / 1024 / 1024:.1f} MB")
                return False
            
            return True
        except Exception as e:
            logger.error(f"Memory management test failed: {e}")
            return False
    
    def test_error_handling(self):
        """Test error handling and recovery."""
        try:
            from aretomo3_gui.utils.file_utils import get_file_info
            
            # Test handling of non-existent files
            result = get_file_info("/non/existent/file.txt")
            assert result is None  # Should handle gracefully
            
            # Test handling of invalid input
            result = get_file_info(None)
            assert result is None  # Should handle gracefully
            
            result = get_file_info("")
            assert result is None  # Should handle gracefully
            
            return True
        except Exception as e:
            logger.error(f"Error handling test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all production readiness tests."""
        logger.info("🚀 Starting Comprehensive Production Readiness Test Suite")
        logger.info("=" * 80)
        
        # Core functionality tests
        self.run_test("Core Imports", self.test_core_imports)
        self.run_test("Security Features", self.test_security_features)
        self.run_test("Database Operations", self.test_database_operations)
        self.run_test("Web API Security", self.test_web_api_security)
        self.run_test("GUI Initialization", self.test_gui_initialization)
        self.run_test("File Operations", self.test_file_operations)
        self.run_test("Memory Management", self.test_memory_management)
        self.run_test("Error Handling", self.test_error_handling)
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report."""
        logger.info("=" * 80)
        logger.info("📊 PRODUCTION READINESS TEST REPORT")
        logger.info("=" * 80)
        
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        logger.info(f"Tests Run: {self.total_tests}")
        logger.info(f"Passed: {self.passed_tests}")
        logger.info(f"Failed: {len(self.critical_failures)}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if self.critical_failures:
            logger.error("❌ CRITICAL FAILURES:")
            for failure in self.critical_failures:
                logger.error(f"  - {failure}")
        
        if success_rate >= 95:
            logger.info("🎉 PRODUCTION READY - All critical tests passed!")
        elif success_rate >= 80:
            logger.warning("⚠️  MOSTLY READY - Some issues need attention")
        else:
            logger.error("🚫 NOT PRODUCTION READY - Critical issues found")
        
        # Save detailed report
        report_data = {
            'timestamp': time.time(),
            'total_tests': self.total_tests,
            'passed_tests': self.passed_tests,
            'success_rate': success_rate,
            'test_results': self.test_results,
            'critical_failures': self.critical_failures
        }
        
        with open('production_test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        logger.info("📄 Detailed report saved to: production_test_report.json")

if __name__ == "__main__":
    test_suite = ProductionTestSuite()
    test_suite.run_all_tests()
