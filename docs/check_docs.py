#!/usr/bin/env python3
"""
Documentation checker utility for AreTomo3 GUI
Lists all files in the docs directory and provides analysis
"""

import os
import glob
from pathlib import Path

def check_docs_directory():
    """Check and analyze the docs directory structure."""
    docs_dir = Path(__file__).parent
    print(f"Checking documentation directory: {docs_dir}")
    print("=" * 60)
    
    if not docs_dir.exists():
        print("❌ Docs directory does not exist!")
        return
    
    # List all files
    all_files = []
    for pattern in ["*", "**/*"]:
        all_files.extend(glob.glob(str(docs_dir / pattern), recursive=True))
    
    # Filter out directories
    files = [f for f in all_files if os.path.isfile(f)]
    
    if not files:
        print("📁 Docs directory is empty")
        print("\n💡 Suggested documentation structure:")
        suggested_docs = [
            "README.md - Main documentation",
            "installation.md - Installation instructions", 
            "user_guide.md - User guide and tutorials",
            "api_reference.md - API documentation",
            "file_formats.md - Supported file formats (MRC, MDOC)",
            "troubleshooting.md - Common issues and solutions",
            "changelog.md - Version history",
            "examples/ - Example workflows and scripts",
            "images/ - Screenshots and diagrams"
        ]
        for suggestion in suggested_docs:
            print(f"  - {suggestion}")
        return
    
    print(f"📄 Found {len(files)} files:")
    print()
    
    # Categorize files
    categories = {
        'Markdown': ['.md', '.markdown'],
        'Text': ['.txt', '.rst'],
        'Images': ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
        'PDFs': ['.pdf'],
        'HTML': ['.html', '.htm'],
        'Config': ['.yml', '.yaml', '.json', '.toml'],
        'Other': []
    }
    
    categorized = {cat: [] for cat in categories}
    
    for file_path in files:
        rel_path = os.path.relpath(file_path, docs_dir)
        ext = os.path.splitext(file_path)[1].lower()
        
        categorized_file = False
        for category, extensions in categories.items():
            if category != 'Other' and ext in extensions:
                categorized[category].append(rel_path)
                categorized_file = True
                break
        
        if not categorized_file:
            categorized['Other'].append(rel_path)
    
    # Display categorized files
    for category, file_list in categorized.items():
        if file_list:
            print(f"📂 {category} files:")
            for file_rel in sorted(file_list):
                file_size = os.path.getsize(docs_dir / file_rel)
                size_str = format_file_size(file_size)
                print(f"   {file_rel} ({size_str})")
            print()
    
    # Check for common documentation files
    print("🔍 Documentation completeness check:")
    important_docs = {
        'README.md': 'Main project documentation',
        'installation.md': 'Installation instructions',
        'user_guide.md': 'User guide',
        'api_reference.md': 'API documentation',
        'changelog.md': 'Version history'
    }
    
    for doc_file, description in important_docs.items():
        if any(doc_file.lower() in f.lower() for f in files):
            print(f"   ✅ {doc_file} - {description}")
        else:
            print(f"   ❌ {doc_file} - {description} (missing)")
    
    print()
    
    # Analyze file content briefly
    print("📝 Content analysis:")
    for file_path in files:
        if file_path.endswith(('.md', '.txt', '.rst')):
            try:
                rel_path = os.path.relpath(file_path, docs_dir)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = len(content.splitlines())
                    words = len(content.split())
                    print(f"   {rel_path}: {lines} lines, ~{words} words")
            except Exception as e:
                print(f"   {rel_path}: Error reading file - {e}")
    
    print("\n" + "=" * 60)
    print("✨ Documentation check complete!")

def format_file_size(size_bytes):
    """Format file size in human readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"

if __name__ == "__main__":
    check_docs_directory()