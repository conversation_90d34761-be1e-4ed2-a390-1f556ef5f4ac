{"timestamp": 1748738487.615683, "total_tests": 8, "passed_tests": 5, "success_rate": 62.5, "test_results": {"Core Imports": {"status": "PASS", "duration": 2.26961350440979, "message": "Test passed successfully"}, "Security Features": {"status": "FAIL", "duration": 5.125999450683594e-05, "message": "Test failed"}, "Database Operations": {"status": "PASS", "duration": 1.9073486328125e-06, "message": "Test passed successfully"}, "Web API Security": {"status": "FAIL", "duration": 3.218650817871094e-05, "message": "Test failed"}, "GUI Initialization": {"status": "PASS", "duration": 1.559889316558838, "message": "Test passed successfully"}, "File Operations": {"status": "FAIL", "duration": 0.001032114028930664, "message": "Test failed"}, "Memory Management": {"status": "PASS", "duration": 0.11217975616455078, "message": "Test passed successfully"}, "Error Handling": {"status": "PASS", "duration": 0.00017833709716796875, "message": "Test passed successfully"}}, "critical_failures": ["Security Features", "Web API Security", "File Operations"]}