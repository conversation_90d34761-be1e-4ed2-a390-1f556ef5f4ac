#!/usr/bin/env python3
"""
Comprehensive test runner for AreTomo3 GUI.
Executes all tests systematically and generates detailed reports.
"""

import sys
import os
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime
import argparse

def setup_environment():
    """Set up the testing environment."""
    # Add src to Python path
    src_path = Path(__file__).parent / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    # Set environment variables for testing
    os.environ['PYTEST_CURRENT_TEST'] = 'true'
    os.environ['QT_QPA_PLATFORM'] = 'offscreen'  # For headless GUI testing

def run_test_suite(test_path, verbose=True, capture=False):
    """Run a specific test suite and return results."""
    cmd = [
        sys.executable, "-m", "pytest",
        str(test_path),
        "--tb=short"
    ]
    
    if verbose:
        cmd.append("-v")
    
    if capture:
        cmd.append("-s")
    
    print(f"\n🔧 Running test suite: {test_path.name}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        elapsed_time = time.time() - start_time
        
        return {
            'test_file': test_path.name,
            'success': result.returncode == 0,
            'elapsed_time': elapsed_time,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'return_code': result.returncode
        }
    
    except subprocess.TimeoutExpired:
        elapsed_time = time.time() - start_time
        return {
            'test_file': test_path.name,
            'success': False,
            'elapsed_time': elapsed_time,
            'stdout': '',
            'stderr': 'Test timed out after 300 seconds',
            'return_code': -1
        }
    
    except Exception as e:
        elapsed_time = time.time() - start_time
        return {
            'test_file': test_path.name,
            'success': False,
            'elapsed_time': elapsed_time,
            'stdout': '',
            'stderr': f'Error running test: {e}',
            'return_code': -2
        }

def discover_test_files():
    """Discover all test files in the tests directory."""
    test_dir = Path(__file__).parent / "tests"
    
    # Define test categories and their priorities
    test_categories = {
        'unit': {
            'pattern': 'test_*.py',
            'priority': 1,
            'description': 'Unit tests for individual components'
        },
        'integration': {
            'pattern': 'test_*integration*.py',
            'priority': 2,
            'description': 'Integration tests for component interactions'
        },
        'gui': {
            'pattern': 'gui/test_*.py',
            'priority': 3,
            'description': 'GUI component tests'
        },
        'utils': {
            'pattern': 'utils/test_*.py',
            'priority': 4,
            'description': 'Utility function tests'
        },
        'comprehensive': {
            'pattern': 'test_comprehensive*.py',
            'priority': 5,
            'description': 'Comprehensive system tests'
        }
    }
    
    discovered_tests = {}
    
    for category, config in test_categories.items():
        pattern = config['pattern']
        if '/' in pattern:
            # Subdirectory pattern
            subdir, file_pattern = pattern.split('/', 1)
            search_path = test_dir / subdir
            if search_path.exists():
                test_files = list(search_path.glob(file_pattern))
            else:
                test_files = []
        else:
            # Root directory pattern
            test_files = list(test_dir.glob(pattern))
            # Exclude files that match other categories
            for other_category, other_config in test_categories.items():
                if other_category != category and 'integration' not in other_config['pattern']:
                    other_pattern = other_config['pattern'].replace('*/', '')
                    test_files = [f for f in test_files if not f.name.startswith(other_pattern.replace('test_', '').replace('.py', ''))]
        
        if test_files:
            discovered_tests[category] = {
                'files': sorted(test_files),
                'priority': config['priority'],
                'description': config['description']
            }
    
    return discovered_tests

def generate_test_report(results, output_file="test_report.json"):
    """Generate comprehensive test report."""
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_suites': len(results),
            'passed_suites': sum(1 for r in results if r['success']),
            'failed_suites': sum(1 for r in results if not r['success']),
            'total_time': sum(r['elapsed_time'] for r in results)
        },
        'results': results
    }
    
    # Calculate success rate
    if report['summary']['total_suites'] > 0:
        report['summary']['success_rate'] = (
            report['summary']['passed_suites'] / report['summary']['total_suites'] * 100
        )
    else:
        report['summary']['success_rate'] = 0
    
    # Save detailed report
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    return report

def print_summary_report(report):
    """Print a summary of test results."""
    print("\n" + "=" * 80)
    print("🎯 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("=" * 80)
    
    summary = report['summary']
    
    print(f"📊 Total Test Suites: {summary['total_suites']}")
    print(f"✅ Passed: {summary['passed_suites']}")
    print(f"❌ Failed: {summary['failed_suites']}")
    print(f"⏱️  Total Time: {summary['total_time']:.2f} seconds")
    print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
    
    print("\n📋 DETAILED RESULTS:")
    print("-" * 40)
    
    for result in report['results']:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{status} {result['test_file']} ({result['elapsed_time']:.2f}s)")
        
        if not result['success']:
            print(f"   Error: {result['stderr'][:100]}...")
    
    print("\n" + "=" * 80)
    
    if summary['success_rate'] >= 90:
        print("🎉 EXCELLENT! Most tests are passing.")
    elif summary['success_rate'] >= 70:
        print("👍 GOOD! Most tests are passing, but some need attention.")
    elif summary['success_rate'] >= 50:
        print("⚠️  WARNING! Many tests are failing. Review needed.")
    else:
        print("🚨 CRITICAL! Most tests are failing. Immediate attention required.")

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="AreTomo3 GUI Comprehensive Test Runner")
    parser.add_argument("--category", choices=['unit', 'integration', 'gui', 'utils', 'comprehensive', 'all'],
                       default='all', help="Test category to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--capture", "-s", action="store_true", help="Capture output")
    parser.add_argument("--output", "-o", default="test_report.json", help="Output report file")
    parser.add_argument("--quick", action="store_true", help="Run only quick tests")
    
    args = parser.parse_args()
    
    print("🚀 AreTomo3 GUI Comprehensive Test Runner")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Category: {args.category}")
    print(f"Verbose: {args.verbose}")
    
    # Set up environment
    setup_environment()
    
    # Discover tests
    discovered_tests = discover_test_files()
    
    if not discovered_tests:
        print("❌ No test files found!")
        return 1
    
    print(f"\n📁 Discovered test categories:")
    for category, info in discovered_tests.items():
        print(f"   {category}: {len(info['files'])} files - {info['description']}")
    
    # Determine which tests to run
    if args.category == 'all':
        tests_to_run = []
        # Sort by priority
        for category in sorted(discovered_tests.keys(), key=lambda x: discovered_tests[x]['priority']):
            tests_to_run.extend(discovered_tests[category]['files'])
    else:
        if args.category in discovered_tests:
            tests_to_run = discovered_tests[args.category]['files']
        else:
            print(f"❌ Category '{args.category}' not found!")
            return 1
    
    if args.quick:
        # Run only the first test from each category for quick validation
        tests_to_run = tests_to_run[:min(3, len(tests_to_run))]
        print(f"\n⚡ Quick mode: Running {len(tests_to_run)} tests")
    
    print(f"\n🔧 Running {len(tests_to_run)} test suites...")
    
    # Run tests
    results = []
    for test_file in tests_to_run:
        result = run_test_suite(test_file, verbose=args.verbose, capture=args.capture)
        results.append(result)
        
        # Print immediate result
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['test_file']} ({result['elapsed_time']:.2f}s)")
        
        if not result['success'] and args.verbose:
            print(f"   Error: {result['stderr']}")
    
    # Generate and display report
    report = generate_test_report(results, args.output)
    print_summary_report(report)
    
    print(f"\n📄 Detailed report saved to: {args.output}")
    
    # Return appropriate exit code
    return 0 if report['summary']['success_rate'] >= 70 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
