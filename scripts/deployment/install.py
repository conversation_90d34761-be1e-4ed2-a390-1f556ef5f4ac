#!/usr/bin/env python3
"""
AT3GUI Python Installation Script
Advanced installation script for AreTomo3 GUI
"""

import sys
import os
import platform
import subprocess
import argparse
from pathlib import Path

def check_system_requirements():
    """Check if system meets requirements for installation."""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher required")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Check for required system tools
    required_tools = []
    
    if platform.system() != "Windows":
        required_tools.extend(["git"])
        if platform.system() == "Linux":
            pass  # git is sufficient for Linux
        elif platform.system() == "Darwin":
            pass  # git is sufficient for macOS
    else:
        required_tools.extend(["git"])
    
    missing_tools = []
    for tool in required_tools:
        try:
            subprocess.run([tool, "--version"], capture_output=True, check=True)
            print(f"✅ {tool} available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing_tools.append(tool)
            print(f"⚠️  {tool} not found (optional)")
    
    if missing_tools:
        print(f"\n⚠️  Missing optional tools: {', '.join(missing_tools)}")
        print("These are not required but may be useful for development.")
    
    return True

def create_venv():
    """Create virtual environment."""
    print("\n🐍 Creating virtual environment...")
    
    try:
        venv_path = Path.cwd() / "AT3GUI" / "at3gui_env"
        venv_path.parent.mkdir(exist_ok=True)
        
        if venv_path.exists():
            print("⚠️  Virtual environment already exists. Removing and recreating...")
            import shutil
            shutil.rmtree(venv_path)
            # Also remove parent if empty
            if not any(venv_path.parent.iterdir()):
                venv_path.parent.rmdir()
        
        # Create parent directory
        venv_path.parent.mkdir(exist_ok=True)
        subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
        
        # Activate virtual environment
        if platform.system() == "Windows":
            activate_script = venv_path / "Scripts" / "activate.bat"
            python_exe = venv_path / "Scripts" / "python.exe"
        else:
            activate_script = venv_path / "bin" / "activate"
            python_exe = venv_path / "bin" / "python"
        
        print(f"✅ Virtual environment created at: {venv_path}")
        print(f"🔄 To activate: source {activate_script}")
        
        return str(python_exe), str(venv_path)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return None, None

def install_dependencies(python_exe=None):
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    if python_exe is None:
        python_exe = sys.executable
    
    try:
        # First ensure pip is up to date
        subprocess.run([
            python_exe, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True)
        
        # Install Qt platform plugins first
        subprocess.run([
            python_exe, "-m", "pip", "install", "PyQt6-Qt6"
        ], check=True)
        
        # Get project directory (parent of scripts directory)
        script_dir = Path(__file__).parent
        project_dir = script_dir.parent
        
        # Install in editable mode
        subprocess.run([
            python_exe, "-m", "pip", "install", "-e", str(project_dir)
        ], check=True)
        
        print("✅ Python dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def test_installation(python_exe=None):
    """Test the installation."""
    print("\n🧪 Testing installation...")
    
    if python_exe is None:
        python_exe = sys.executable
    
    try:
        # Test basic import
        result = subprocess.run([
            python_exe, "-c", "import aretomo3_gui; print('AT3GUI module imports successfully')"
        ], capture_output=True, text=True, check=True)
        print("✅ " + result.stdout.strip())
        
        # Test GUI import
        result = subprocess.run([
            python_exe, "-c", "from aretomo3_gui.gui.main_window import AreTomoGUI; print('GUI components load successfully')"
        ], capture_output=True, text=True, check=True)
        print("✅ " + result.stdout.strip())
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Import test failed: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False

def create_desktop_shortcut(venv_path=None):
    """Create desktop shortcut (Linux/macOS)."""
    if platform.system() == "Windows":
        print("ℹ️  Desktop shortcut creation not implemented for Windows")
        return
    
    try:
        desktop_dir = Path.home() / "Desktop"
        if not desktop_dir.exists():
            desktop_dir = Path.home() / ".local" / "share" / "applications"
            desktop_dir.mkdir(parents=True, exist_ok=True)
        
        if venv_path:
            if platform.system() == "Windows":
                python_exe = Path(venv_path) / "Scripts" / "python.exe"
            else:
                python_exe = Path(venv_path) / "bin" / "python"
            exec_command = f"{python_exe} -m aretomo3_gui"
        else:
            exec_command = f"{sys.executable} -m aretomo3_gui"
        
        shortcut_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=AreTomo3 GUI
Comment=GUI for AreTomo3 tomographic reconstruction
Exec={exec_command}
Icon=application-x-executable
Terminal=false
Categories=Science;Education;
"""
        
        shortcut_file = desktop_dir / "aretomo3-gui.desktop"
        with open(shortcut_file, 'w') as f:
            f.write(shortcut_content)
        
        # Make executable
        shortcut_file.chmod(0o755)
        
        print(f"✅ Desktop shortcut created: {shortcut_file}")
        
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")

def main():
    """Main installation function."""
    parser = argparse.ArgumentParser(description="Install AreTomo3 GUI")
    parser.add_argument("--no-test", action="store_true", help="Skip installation testing")
    parser.add_argument("--no-shortcut", action="store_true", help="Skip desktop shortcut creation")
    parser.add_argument("--no-venv", action="store_true", help="Install in current environment (no virtual environment)")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎯 AreTomo3 GUI Installation")
    print("=" * 60)
    
    # Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please install missing components.")
        sys.exit(1)
    
    python_exe = None
    venv_path = None
    
    # Create virtual environment (unless skipped)
    if not args.no_venv:
        python_exe, venv_path = create_venv()
        if not python_exe:
            print("\n❌ Failed to create virtual environment.")
            print("You can try installing in current environment with --no-venv")
            sys.exit(1)
    else:
        print("\n⚠️  Installing in current Python environment (no virtual environment)")
        python_exe = sys.executable
    
    # Install dependencies
    if not install_dependencies(python_exe):
        print("\n❌ Failed to install dependencies.")
        sys.exit(1)
    
    # Test installation
    if not args.no_test:
        if not test_installation(python_exe):
            print("\n⚠️  Installation completed but tests failed.")
            print("You may still be able to use the application.")
    
    # Create desktop shortcut
    if not args.no_shortcut:
        create_desktop_shortcut(venv_path)
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 Installation completed successfully!")
    print("=" * 60)
    
    if venv_path:
        if platform.system() == "Windows":
            activate_script = Path(venv_path) / "Scripts" / "activate.bat"
        else:
            activate_script = Path(venv_path) / "bin" / "activate"
        
        print("To run AreTomo3 GUI:")
        print(f"  1. Activate environment: source {activate_script}")
        print("  2. Run: python -m aretomo3_gui")
    else:
        print("To run AreTomo3 GUI:")
        print("  Command line: python -m aretomo3_gui")
    
    print("")
    print("NOTE: EER file support has been removed from AT3GUI.")
    print("See docs/EER_SUPPORT.md for more information.")
    print("=" * 60)

if __name__ == "__main__":
    main()