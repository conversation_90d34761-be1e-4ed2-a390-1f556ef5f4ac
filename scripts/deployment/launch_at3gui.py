#!/usr/bin/env python3
"""
AT3GUI Launcher Script
======================

This script provides a convenient way to launch the AT3GUI application.
It handles environment setup and provides helpful error messages.

Usage:
    python launch_at3gui.py
    ./launch_at3gui.py
"""

import sys
import os
from pathlib import Path

def check_virtual_environment():
    """Check if we're running in a virtual environment."""
    return hasattr(sys, 'real_prefix') or (
        hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
    )

def check_qt_display():
    """Check if QT can access the display."""
    if 'DISPLAY' not in os.environ and sys.platform.startswith('linux'):
        print("⚠️  Warning: No DISPLAY environment variable found.")
        print("   If running over SSH, make sure to use 'ssh -X' for X11 forwarding.")
        print("   Or set QT_QPA_PLATFORM=offscreen for headless mode.")
        print()

def main():
    """Main launcher function."""
    print("🚀 AT3GUI Launcher")
    print("==================")
    print()
    
    # Check if we're in the correct directory
    current_dir = Path.cwd()
    expected_files = ['src/aretomo3_gui/main.py', 'pyproject.toml']
    
    for file in expected_files:
        if not (current_dir / file).exists():
            print(f"❌ Error: Expected file '{file}' not found.")
            print(f"   Make sure you're running this from the AT3GUI root directory.")
            print(f"   Current directory: {current_dir}")
            sys.exit(1)
    
    # Check virtual environment
    if not check_virtual_environment():
        print("⚠️  Warning: Not running in a virtual environment.")
        print("   It's recommended to activate the virtual environment first:")
        print("   source venv/bin/activate")
        print()
        
        # Check if venv exists
        if (current_dir / 'venv').exists():
            response = input("Would you like to continue anyway? (y/N): ")
            if response.lower() != 'y':
                print("Cancelled. Please activate the virtual environment and try again.")
                sys.exit(0)
        else:
            print("❌ Virtual environment not found. Please run setup_at3gui.sh first.")
            sys.exit(1)
    else:
        print("✅ Running in virtual environment")
    
    # Check display for GUI
    check_qt_display()
    
    # Try to import the main modules
    try:
        print("📋 Checking AT3GUI installation...")
        import aretomo3_gui
        print("✅ AT3GUI package found")
        
        from aretomo3_gui.main import AreTomoGUI
        print("✅ Main GUI class available")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Please make sure AT3GUI is properly installed:")
        print("   pip install -e .")
        sys.exit(1)
    
    # Launch the application
    try:
        print("🚀 Launching AT3GUI...")
        print()
        
        # Import and run the main application
        from aretomo3_gui.main import main as run_app
        run_app()
        
    except KeyboardInterrupt:
        print("\n👋 AT3GUI closed by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error launching AT3GUI: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure you're in a virtual environment with all dependencies")
        print("2. For display issues, try: export QT_QPA_PLATFORM=offscreen")
        print("3. For SSH connections, use: ssh -X username@hostname")
        print("4. Check the logs in src/logs/ for detailed error information")
        sys.exit(1)

if __name__ == "__main__":
    main()
