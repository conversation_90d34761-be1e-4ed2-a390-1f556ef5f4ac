#!/usr/bin/env python3
"""
AreTomo3 GUI Project Reorganization Script
==========================================

This script reorganizes the project into a clean, professional structure.
"""

import os
import shutil
import sys
from pathlib import Path

class ProjectReorganizer:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "reorganization_backup"
        
    def create_backup(self):
        """Create a backup before reorganization."""
        print("📦 Creating backup...")
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        # Backup critical directories
        critical_dirs = ['src', 'tests', 'scripts', 'docs']
        self.backup_dir.mkdir()
        
        for dir_name in critical_dirs:
            src_dir = self.project_root / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, self.backup_dir / dir_name)
        
        print(f"✅ Backup created at: {self.backup_dir}")
    
    def create_clean_structure(self):
        """Create the clean directory structure."""
        print("🏗️  Creating clean directory structure...")
        
        # Define the target structure
        structure = {
            'src/aretomo3_gui': ['core', 'gui', 'utils', 'tools'],
            'tests': ['unit', 'integration', 'gui', 'data'],
            'docs': ['user', 'developer', 'api'],
            'scripts': ['utils'],
            'config': ['templates'],
            'build': []
        }
        
        # Create directories
        for base_dir, subdirs in structure.items():
            base_path = self.project_root / base_dir
            base_path.mkdir(exist_ok=True)
            
            # Create __init__.py for Python packages
            if base_dir.startswith(('src', 'tests')):
                (base_path / '__init__.py').touch()
            
            for subdir in subdirs:
                sub_path = base_path / subdir
                sub_path.mkdir(exist_ok=True)
                
                # Create __init__.py for Python packages
                if base_dir.startswith(('src', 'tests')):
                    (sub_path / '__init__.py').touch()
        
        print("✅ Directory structure created")
    
    def reorganize_source_code(self):
        """Reorganize source code files."""
        print("📁 Reorganizing source code...")
        
        # Source files are already in src/aretomo3_gui, just ensure they're clean
        src_dir = self.project_root / 'src' / 'aretomo3_gui'
        
        # Ensure critical files exist
        critical_files = ['__init__.py', '__main__.py', 'main.py']
        for file_name in critical_files:
            file_path = src_dir / file_name
            if not file_path.exists():
                print(f"⚠️  Missing critical file: {file_path}")
        
        print("✅ Source code organized")
    
    def reorganize_tests(self):
        """Reorganize test files."""
        print("🧪 Reorganizing tests...")
        
        tests_dir = self.project_root / 'tests'
        
        # Define test organization
        test_mapping = {
            'unit': ['test_core_functionality.py', 'test_error_handling.py', 'test_resource_manager.py'],
            'integration': ['test_installation.py', 'test_batch_processing.py', 'FRESH_INSTALL_TEST.py'],
            'gui': ['test_gui_basic.py', 'test_gui_components.py', 'test_application_startup.py'],
            'data': []  # Test data files
        }
        
        # Move test files to appropriate directories
        for category, files in test_mapping.items():
            category_dir = tests_dir / category
            category_dir.mkdir(exist_ok=True)
            (category_dir / '__init__.py').touch()
            
            for file_name in files:
                # Look for the file in various locations
                for search_dir in [tests_dir, tests_dir / 'integration', tests_dir / 'gui', tests_dir / 'core']:
                    src_file = search_dir / file_name
                    if src_file.exists():
                        dst_file = category_dir / file_name
                        if not dst_file.exists():
                            shutil.move(str(src_file), str(dst_file))
                            print(f"  Moved: {file_name} -> {category}/")
                        break
        
        print("✅ Tests organized")
    
    def reorganize_docs(self):
        """Reorganize documentation files."""
        print("📚 Reorganizing documentation...")
        
        docs_dir = self.project_root / 'docs'
        
        # Define documentation organization
        doc_mapping = {
            'user': ['USER_GUIDE.md', 'INSTALLATION.md', 'TROUBLESHOOTING.md', 'QUICK_START.md'],
            'developer': ['DEVELOPER_GUIDE.md', 'CONTRIBUTING.md', 'CODE_OF_CONDUCT.md'],
            'api': ['API_REFERENCE.md', 'CHANGELOG.md']
        }
        
        # Move documentation files
        for category, files in doc_mapping.items():
            category_dir = docs_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for file_name in files:
                # Check if file exists in docs or root
                for search_dir in [docs_dir, self.project_root]:
                    src_file = search_dir / file_name
                    if src_file.exists():
                        dst_file = category_dir / file_name
                        if not dst_file.exists():
                            shutil.copy2(str(src_file), str(dst_file))
                            print(f"  Copied: {file_name} -> docs/{category}/")
                        break
        
        print("✅ Documentation organized")
    
    def clean_root_directory(self):
        """Clean up the root directory."""
        print("🧹 Cleaning root directory...")
        
        # Files that should stay in root
        keep_in_root = {
            'README.md', 'requirements.txt', 'pyproject.toml', 'setup.py',
            '.gitignore', 'LICENSE', 'CHANGELOG.md'
        }
        
        # Move test data to proper location
        test_data_src = self.project_root / 'test_data'
        if test_data_src.exists():
            test_data_dst = self.project_root / 'tests' / 'data'
            if not test_data_dst.exists():
                shutil.move(str(test_data_src), str(test_data_dst))
                print("  Moved: test_data -> tests/data/")
        
        print("✅ Root directory cleaned")
    
    def update_imports(self):
        """Update import statements in Python files."""
        print("🔧 Updating import statements...")
        
        # This is a placeholder - in practice, you'd need to scan and update imports
        # For now, just print a reminder
        print("⚠️  Remember to update import statements in:")
        print("   - Test files")
        print("   - Source files")
        print("   - Script files")
        
    def create_gitignore(self):
        """Create a proper .gitignore file."""
        print("📝 Creating .gitignore...")
        
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
at3gui_env/
AT3GUI/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*_backup/
backup_*/
reorganization_backup/

# Test artifacts
.pytest_cache/
.coverage
htmlcov/

# Build artifacts
build/
dist/
*.egg-info/
"""
        
        gitignore_path = self.project_root / '.gitignore'
        with open(gitignore_path, 'w') as f:
            f.write(gitignore_content)
        
        print("✅ .gitignore created")
    
    def create_summary(self):
        """Create a reorganization summary."""
        print("📋 Creating reorganization summary...")
        
        summary = """# Project Reorganization Complete

## ✅ Completed Actions

1. **Directory Structure**: Created clean, professional structure
2. **Source Code**: Organized in src/aretomo3_gui/
3. **Tests**: Categorized into unit/, integration/, gui/
4. **Documentation**: Organized by audience (user/, developer/, api/)
5. **Scripts**: Cleaned and organized
6. **Root Directory**: Cleaned of clutter

## 📁 New Structure

```
AT3GUI_working/
├── src/aretomo3_gui/          # Source code
├── tests/                     # All tests organized by type
├── docs/                      # Documentation by audience
├── scripts/                   # Installation and utility scripts
├── config/                    # Configuration templates
└── build/                     # Build artifacts (gitignored)
```

## 🔧 Next Steps

1. **Update Imports**: Review and fix any broken import statements
2. **Test Everything**: Run comprehensive tests to ensure nothing broke
3. **Update Documentation**: Verify all documentation links still work
4. **Verify Scripts**: Ensure installation and launch scripts work

## 📦 Backup

A complete backup was created at: `reorganization_backup/`
"""
        
        summary_path = self.project_root / 'REORGANIZATION_SUMMARY.md'
        with open(summary_path, 'w') as f:
            f.write(summary)
        
        print(f"✅ Summary created: {summary_path}")
    
    def run_reorganization(self):
        """Run the complete reorganization process."""
        print("🚀 Starting AreTomo3 GUI Project Reorganization")
        print("=" * 50)
        
        try:
            self.create_backup()
            self.create_clean_structure()
            self.reorganize_source_code()
            self.reorganize_tests()
            self.reorganize_docs()
            self.clean_root_directory()
            self.create_gitignore()
            self.update_imports()
            self.create_summary()
            
            print("\n🎉 Reorganization Complete!")
            print(f"📦 Backup available at: {self.backup_dir}")
            print("📋 See REORGANIZATION_SUMMARY.md for details")
            
        except Exception as e:
            print(f"\n❌ Error during reorganization: {e}")
            print(f"📦 Restore from backup: {self.backup_dir}")
            return False
        
        return True

def main():
    """Main entry point."""
    if len(sys.argv) != 2:
        print("Usage: python reorganize_project.py <project_directory>")
        sys.exit(1)
    
    project_dir = sys.argv[1]
    if not os.path.exists(project_dir):
        print(f"Error: Project directory does not exist: {project_dir}")
        sys.exit(1)
    
    reorganizer = ProjectReorganizer(project_dir)
    success = reorganizer.run_reorganization()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
