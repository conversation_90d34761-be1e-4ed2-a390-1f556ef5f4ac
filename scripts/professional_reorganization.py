#!/usr/bin/env python3
"""
Professional Project Reorganization Script
Cleans up the project structure, removes unnecessary files, and creates professional organization.
"""

import os
import shutil
import logging
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('reorganization.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProjectReorganizer:
    """Professional project reorganization manager."""
    
    def __init__(self, project_root: Path):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "backups_AT3GUI"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def create_backup(self):
        """Create a backup before reorganization."""
        logger.info("Creating backup before reorganization...")
        
        backup_name = f"pre_reorganization_backup_{self.timestamp}"
        backup_path = self.backup_dir / f"{backup_name}.tar.gz"
        
        # Create compressed backup
        import tarfile
        with tarfile.open(backup_path, "w:gz") as tar:
            tar.add(self.project_root / "src", arcname="src")
            tar.add(self.project_root / "tests", arcname="tests")
            tar.add(self.project_root / "docs", arcname="docs")
            tar.add(self.project_root / "scripts", arcname="scripts")
            
        logger.info(f"Backup created: {backup_path}")
        return backup_path
        
    def remove_unnecessary_files(self):
        """Remove unnecessary and duplicate files."""
        logger.info("Removing unnecessary files...")
        
        # Files to remove
        unnecessary_files = [
            "test_enhanced_implementation.py",
            "test_implementation.py",
            "BACKUP_INFO.txt",
            "PROJECT_STRUCTURE.md",
            "QUICK_START.md",
            "REORGANIZATION_PLAN.md"
        ]
        
        # Directories to clean up
        cleanup_dirs = [
            "src/__pycache__",
            "src/aretomo3_gui/__pycache__",
            "tests/__pycache__",
            "tests/gui/__pycache__",
            "tests/core/__pycache__",
            "tests/unit/__pycache__",
            "tests/batch/__pycache__",
            "tests/integration/__pycache__"
        ]
        
        # Remove unnecessary files
        for file_name in unnecessary_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Removed: {file_name}")
                
        # Remove __pycache__ directories
        for dir_path in cleanup_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists():
                shutil.rmtree(full_path)
                logger.info(f"Removed: {dir_path}")
                
        # Remove duplicate documentation
        docs_to_remove = [
            "docs/user_guide.md",  # Duplicate of docs/user/USER_GUIDE.md
            "docs/reports/BACKUP_INFO.txt",  # Duplicate
        ]
        
        for doc_path in docs_to_remove:
            full_path = self.project_root / doc_path
            if full_path.exists():
                full_path.unlink()
                logger.info(f"Removed duplicate: {doc_path}")
                
    def standardize_file_names(self):
        """Standardize file names to professional conventions."""
        logger.info("Standardizing file names...")
        
        # Rename files to follow professional conventions
        renames = {
            "README.md": "README.md",  # Already good
            "pyproject.toml": "pyproject.toml",  # Already good
            "requirements.txt": "requirements.txt",  # Already good
        }
        
        # Check for any non-standard file names in src/
        src_dir = self.project_root / "src" / "aretomo3_gui"
        for file_path in src_dir.rglob("*.py"):
            # Ensure all Python files follow snake_case
            if not self._is_snake_case(file_path.stem):
                new_name = self._to_snake_case(file_path.stem) + ".py"
                new_path = file_path.parent / new_name
                if not new_path.exists():
                    file_path.rename(new_path)
                    logger.info(f"Renamed: {file_path.name} -> {new_name}")
                    
    def _is_snake_case(self, name: str) -> bool:
        """Check if name follows snake_case convention."""
        return name.islower() and "_" in name or name.islower()
        
    def _to_snake_case(self, name: str) -> str:
        """Convert name to snake_case."""
        import re
        # Convert CamelCase to snake_case
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
        
    def create_professional_structure(self):
        """Create professional directory structure."""
        logger.info("Creating professional directory structure...")
        
        # Professional directories to ensure exist
        professional_dirs = [
            "src/aretomo3_gui",
            "tests/unit",
            "tests/integration", 
            "tests/gui",
            "docs/user",
            "docs/developer",
            "docs/api",
            "scripts/deployment",
            "scripts/development",
            "examples",
            "config/templates"
        ]
        
        for dir_path in professional_dirs:
            full_path = self.project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            
        # Create __init__.py files where needed
        init_dirs = [
            "src/aretomo3_gui",
            "tests",
            "tests/unit",
            "tests/integration",
            "tests/gui"
        ]
        
        for dir_path in init_dirs:
            init_file = self.project_root / dir_path / "__init__.py"
            if not init_file.exists():
                init_file.touch()
                logger.info(f"Created: {init_file}")
                
    def organize_scripts(self):
        """Organize scripts into professional categories."""
        logger.info("Organizing scripts...")
        
        # Create script categories
        script_categories = {
            "deployment": [
                "install.py", "install.sh", "uninstall.sh", 
                "setup_at3gui.bat", "launch.sh", "launch_at3gui.py"
            ],
            "development": [
                "backup.sh", "cleanup.sh", "validate_project.sh",
                "reorganize_project.py"
            ],
            "utilities": [
                "backup_project.sh", "quick_backup.sh", "safe_backup.sh"
            ]
        }
        
        scripts_dir = self.project_root / "scripts"
        
        # Create category directories
        for category in script_categories:
            category_dir = scripts_dir / category
            category_dir.mkdir(exist_ok=True)
            
        # Move scripts to appropriate categories
        for category, scripts in script_categories.items():
            for script in scripts:
                src_path = scripts_dir / script
                dst_path = scripts_dir / category / script
                if src_path.exists() and not dst_path.exists():
                    shutil.move(str(src_path), str(dst_path))
                    logger.info(f"Moved: {script} -> {category}/")
                    
    def create_professional_documentation(self):
        """Create comprehensive professional documentation."""
        logger.info("Creating professional documentation...")
        
        # Main README.md
        readme_content = self._generate_main_readme()
        with open(self.project_root / "README.md", "w") as f:
            f.write(readme_content)
            
        # CHANGELOG.md
        changelog_content = self._generate_changelog()
        with open(self.project_root / "CHANGELOG.md", "w") as f:
            f.write(changelog_content)
            
        # LICENSE file
        license_content = self._generate_license()
        with open(self.project_root / "LICENSE", "w") as f:
            f.write(license_content)
            
        logger.info("Professional documentation created")
        
    def _generate_main_readme(self) -> str:
        """Generate main README.md content."""
        return """# AreTomo3 GUI

A professional graphical user interface for AreTomo3 tomographic reconstruction software.

## Features

- **Enhanced Parameter Management**: Comprehensive parameter configuration with help system
- **Live Processing**: Real-time file monitoring and automated processing
- **Advanced Analysis**: Comprehensive result analysis and visualization
- **Professional Backup System**: Automated compressed backups with cleanup
- **Modern Architecture**: Built with PyQt6 and modern Python practices

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Launch the application
python -m aretomo3_gui
```

## Documentation

- [User Guide](docs/user/USER_GUIDE.md)
- [Installation Guide](docs/user/INSTALLATION.md)
- [Developer Guide](docs/developer/DEVELOPER_GUIDE.md)
- [API Reference](docs/api/API_REFERENCE.md)

## Requirements

- Python 3.8+
- PyQt6
- NumPy
- Matplotlib (optional, for plotting)

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

See [CONTRIBUTING.md](docs/developer/CONTRIBUTING.md) for contribution guidelines.
"""

    def _generate_changelog(self) -> str:
        """Generate CHANGELOG.md content."""
        return f"""# Changelog

All notable changes to AreTomo3 GUI will be documented in this file.

## [2.0.0] - {datetime.now().strftime('%Y-%m-%d')}

### Added
- Enhanced analysis tab with comprehensive visualization
- Live processing with real-time file monitoring
- Professional parameter management with help system
- Automated backup system with compression
- Modern PyQt6-based interface

### Changed
- Complete GUI reorganization for better user experience
- Improved error handling and logging
- Enhanced testing framework

### Fixed
- Import issues and circular dependencies
- Memory leaks in file monitoring
- GUI responsiveness during processing

## [1.0.0] - Previous Release
- Initial AreTomo3 GUI implementation
"""

    def _generate_license(self) -> str:
        """Generate LICENSE content."""
        return f"""MIT License

Copyright (c) {datetime.now().year} AreTomo3 GUI Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"""

    def run_reorganization(self):
        """Run the complete reorganization process."""
        logger.info("Starting professional project reorganization...")
        
        try:
            # 1. Create backup
            self.create_backup()
            
            # 2. Remove unnecessary files
            self.remove_unnecessary_files()
            
            # 3. Standardize file names
            self.standardize_file_names()
            
            # 4. Create professional structure
            self.create_professional_structure()
            
            # 5. Organize scripts
            self.organize_scripts()
            
            # 6. Create professional documentation
            self.create_professional_documentation()
            
            logger.info("✅ Professional reorganization completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Reorganization failed: {e}")
            raise

def main():
    """Main function."""
    project_root = Path(__file__).parent.parent
    reorganizer = ProjectReorganizer(project_root)
    reorganizer.run_reorganization()

if __name__ == "__main__":
    main()
