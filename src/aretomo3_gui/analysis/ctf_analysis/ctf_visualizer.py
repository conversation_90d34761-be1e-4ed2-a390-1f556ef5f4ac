#!/usr/bin/env python3
"""
Interactive CTF 2D Visualizer for AreTomo3

This module provides an interactive viewer for 2D CTF power spectra with:
- Slider navigation through tilt series
- Real-time 2D FFT display updates
- CTF ring overlay
- Quality indicators
- Zoom and pan capabilities
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON>lider, Button
from matplotlib.patches import Circle
import matplotlib.patches as patches
from typing import Dict, List, Tuple, Optional, Union
import logging

# Set matplotlib style to match GUI theme
plt.style.use('default')
import matplotlib as mpl

logger = logging.getLogger(__name__)


class CTF2DVisualizer:
    """
    Interactive 2D CTF visualizer with slider navigation.

    Features:
    - Slider to navigate through tilt series
    - Real-time 2D FFT display updates
    - CTF ring overlay from theoretical fit
    - Quality indicators and metadata display
    """

    def __init__(self, ctf_data: Dict):
        """
        Initialize the CTF visualizer.

        Args:
            ctf_data: Dictionary containing parsed CTF data
        """
        self.ctf_data = ctf_data
        self.current_tilt_idx = 0

        # Extract data
        self.power_spectra = ctf_data.get('power_spectra')
        self.parameters = ctf_data.get('parameters')
        self.tilt_angles = ctf_data.get('tilt_angles', [])
        self.series_name = ctf_data.get('series_name', 'Unknown')

        # Validate data
        if self.power_spectra is None:
            raise ValueError("No power spectra data available")

        if self.parameters is None or self.parameters.empty:
            raise ValueError("No CTF parameters available")

        self.n_tilts = self.power_spectra.shape[0]

        # Initialize matplotlib figure
        self.fig = None
        self.ax_main = None
        self.ax_slider = None
        self.slider = None
        self.im = None
        self.ctf_rings = []
        self.info_text = None

        # Display settings
        self.show_ctf_rings = True
        self.show_ctf_fit = True  # Show actual CTF fit from data
        self.log_scale = False  # Start with linear scale, allow toggle
        self.zoom_factor = 1.0
        self.pan_offset = [0, 0]

        # Theme settings to match GUI
        self._setup_theme()

        logger.info(f"Initialized CTF visualizer for {self.series_name} with {self.n_tilts} tilts")

    def _setup_theme(self):
        """Set up matplotlib theme to match GUI."""
        # Dark theme colors to match the GUI
        self.theme = {
            'bg_color': '#1a1a1a',
            'text_color': '#ffffff',
            'grid_color': '#383838',
            'accent_color': '#4d4d4d',
            'button_color': '#454545',
            'button_hover': '#505050',
            'ctf_ring_color': '#ff6b6b',  # Red for CTF rings
            'ctf_fit_color': '#4ecdc4',   # Cyan for CTF fit
            'quality_good': '#51cf66',    # Green
            'quality_poor': '#ff8787'     # Light red
        }

        # Set matplotlib parameters to match GUI theme
        mpl.rcParams.update({
            'figure.facecolor': self.theme['bg_color'],
            'axes.facecolor': self.theme['bg_color'],
            'axes.edgecolor': self.theme['grid_color'],
            'axes.labelcolor': self.theme['text_color'],
            'xtick.color': self.theme['text_color'],
            'ytick.color': self.theme['text_color'],
            'text.color': self.theme['text_color'],
            'grid.color': self.theme['grid_color'],
            'grid.alpha': 0.3,
            'font.family': 'sans-serif',
            'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif'],
            'font.size': 9,
            'axes.titlesize': 10,
            'axes.labelsize': 9,
            'xtick.labelsize': 8,
            'ytick.labelsize': 8,
            'legend.fontsize': 8,
            'axes.linewidth': 1,
            'xtick.major.width': 1,
            'ytick.major.width': 1
        })

    def create_interactive_viewer(self):
        """Create the interactive CTF viewer interface."""
        # Create figure with dark theme
        self.fig = plt.figure(figsize=(16, 12), facecolor=self.theme['bg_color'])
        self.fig.suptitle(f'Interactive CTF Viewer - {self.series_name}',
                         fontsize=14, fontweight='bold', color=self.theme['text_color'])

        # Main plot for 2D FFT (left side, larger)
        self.ax_main = plt.subplot2grid((7, 6), (0, 0), colspan=3, rowspan=4,
                                       facecolor=self.theme['bg_color'])
        self.ax_main.set_title('2D Power Spectrum with CTF Rings', color=self.theme['text_color'])
        self.ax_main.set_xlabel('Pixels', color=self.theme['text_color'])
        self.ax_main.set_ylabel('Pixels', color=self.theme['text_color'])
        self.ax_main.tick_params(colors=self.theme['text_color'])

        # 1D CTF plot (right side, top)
        self.ax_ctf = plt.subplot2grid((7, 6), (0, 3), colspan=3, rowspan=2,
                                      facecolor=self.theme['bg_color'])
        self.ax_ctf.set_title('1D CTF Profile', color=self.theme['text_color'])
        self.ax_ctf.set_xlabel('Spatial Frequency (1/Å)', color=self.theme['text_color'])
        self.ax_ctf.set_ylabel('CTF Amplitude', color=self.theme['text_color'])
        self.ax_ctf.tick_params(colors=self.theme['text_color'])
        self.ax_ctf.grid(True, alpha=0.3, color=self.theme['grid_color'])

        # Info panel (right side, middle)
        self.ax_info = plt.subplot2grid((7, 6), (2, 3), colspan=3, rowspan=2,
                                       facecolor=self.theme['bg_color'])
        self.ax_info.axis('off')

        # Controls panel (right side, bottom)
        self.ax_controls = plt.subplot2grid((7, 6), (4, 3), colspan=3, rowspan=2,
                                           facecolor=self.theme['bg_color'])
        self.ax_controls.axis('off')

        # Remove quality plot - not needed

        # Slider for tilt navigation
        self.ax_slider = plt.subplot2grid((7, 6), (6, 0), colspan=6, facecolor=self.theme['bg_color'])

        # Initialize slider with theme - show actual stage tilt values
        self.ax_slider.set_facecolor(self.theme['bg_color'])

        # Create custom slider that shows stage tilt angles
        if self.tilt_angles and len(self.tilt_angles) > 0:
            min_tilt = min(self.tilt_angles)
            max_tilt = max(self.tilt_angles)
            self.slider = Slider(
                self.ax_slider,
                'Stage Tilt (°)',
                min_tilt,
                max_tilt,
                valinit=self.tilt_angles[0],
                valfmt='%.1f°',
                valstep=None,  # Allow continuous movement
                facecolor=self.theme['accent_color'],
                edgecolor=self.theme['text_color']
            )
        else:
            # Fallback to index-based slider
            self.slider = Slider(
                self.ax_slider,
                'Tilt Index',
                0,
                self.n_tilts - 1,
                valinit=0,
                valfmt='%d',
                valstep=1,
                facecolor=self.theme['accent_color'],
                edgecolor=self.theme['text_color']
            )

        # Style slider text
        self.slider.label.set_color(self.theme['text_color'])
        self.slider.valtext.set_color(self.theme['text_color'])
        self.slider.on_changed(self.update_tilt)

        # Add control buttons
        self._create_control_buttons()

        # Remove quality plot setup

        # Display initial tilt
        self.update_display()

        # Connect mouse events for zoom/pan
        self.fig.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.fig.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)

        plt.tight_layout()
        return self.fig



    def _create_control_buttons(self):
        """Create control buttons for the interface."""
        # Button positions adjusted for new layout
        button_width = 0.12
        button_height = 0.06

        # Toggle CTF rings button
        ax_rings = plt.axes([0.68, 0.45, button_width, button_height], facecolor=self.theme['button_color'])
        self.btn_rings = Button(ax_rings, '🔴 Rings', color=self.theme['button_color'], hovercolor=self.theme['button_hover'])
        self.btn_rings.label.set_color(self.theme['text_color'])
        self.btn_rings.on_clicked(self.toggle_ctf_rings)

        # Log scale toggle button
        ax_log = plt.axes([0.82, 0.45, button_width, button_height], facecolor=self.theme['button_color'])
        self.btn_log = Button(ax_log, 'Log Scale', color=self.theme['button_color'], hovercolor=self.theme['button_hover'])
        self.btn_log.label.set_color(self.theme['text_color'])
        self.btn_log.on_clicked(self.toggle_log_scale)

        # CTF Fit toggle button
        ax_fit = plt.axes([0.82, 0.37, button_width, button_height], facecolor=self.theme['button_color'])
        self.btn_fit = Button(ax_fit, 'CTF Fit', color=self.theme['button_color'], hovercolor=self.theme['button_hover'])
        self.btn_fit.label.set_color(self.theme['text_color'])
        self.btn_fit.on_clicked(self.toggle_ctf_fit)

        # Reset zoom button
        ax_reset = plt.axes([0.68, 0.37, button_width, button_height], facecolor=self.theme['button_color'])
        self.btn_reset = Button(ax_reset, 'Reset', color=self.theme['button_color'], hovercolor=self.theme['button_hover'])
        self.btn_reset.label.set_color(self.theme['text_color'])
        self.btn_reset.on_clicked(self.reset_zoom)

        # Previous/Next buttons
        ax_prev = plt.axes([0.82, 0.37, 0.06, button_height], facecolor=self.theme['button_color'])
        self.btn_prev = Button(ax_prev, '◀', color=self.theme['button_color'], hovercolor=self.theme['button_hover'])
        self.btn_prev.label.set_color(self.theme['text_color'])
        self.btn_prev.on_clicked(self.prev_tilt)

        ax_next = plt.axes([0.88, 0.37, 0.06, button_height], facecolor=self.theme['button_color'])
        self.btn_next = Button(ax_next, '▶', color=self.theme['button_color'], hovercolor=self.theme['button_hover'])
        self.btn_next.label.set_color(self.theme['text_color'])
        self.btn_next.on_clicked(self.next_tilt)

    def update_tilt(self, val):
        """Update display when slider value changes."""
        self.current_tilt_idx = int(self.slider.val)
        self.update_display()

    def update_display(self):
        """Update the main display with current tilt data."""
        if self.current_tilt_idx >= self.n_tilts:
            return

        # Get current power spectrum
        current_spectrum = self.power_spectra[self.current_tilt_idx]

        # Apply log scale if enabled
        if self.log_scale:
            display_data = np.log10(np.maximum(current_spectrum, 1e-10))
        else:
            display_data = current_spectrum

        # Update or create 2D image
        if self.im is None:
            self.im = self.ax_main.imshow(
                display_data,
                cmap='gray',
                origin='lower',
                interpolation='bilinear'
            )
            self.ax_main.set_aspect('equal')
        else:
            self.im.set_data(display_data)
            self.im.set_clim(vmin=display_data.min(), vmax=display_data.max())

        # Update CTF rings
        self.update_ctf_rings()

        # Update 1D CTF plot
        self.update_ctf_1d_plot()

        # Update info panel
        self.update_info_panel()

        # Remove quality plot indicator update

        # Update title
        stage_tilt = self.tilt_angles[self.current_tilt_idx] if self.tilt_angles else self.current_tilt_idx
        self.ax_main.set_title(f'2D Power Spectrum - Tilt {self.current_tilt_idx + 1}/{self.n_tilts} (Stage: {stage_tilt:.1f}°)')

        self.fig.canvas.draw()

    def extract_radial_profile(self, image_2d):
        """Extract radial profile from 2D power spectrum."""
        h, w = image_2d.shape
        center_x, center_y = w // 2, h // 2

        # Create coordinate arrays
        y, x = np.ogrid[:h, :w]
        x = x - center_x
        y = y - center_y

        # Calculate radial distances
        r = np.sqrt(x*x + y*y).astype(int)

        # Get maximum radius
        max_radius = min(center_x, center_y)

        # Calculate radial average
        radial_profile = []
        radii = []

        for radius in range(1, max_radius):
            mask = (r == radius)
            if np.any(mask):
                radial_profile.append(np.mean(image_2d[mask]))
                radii.append(radius)

        return np.array(radii), np.array(radial_profile)

    def calculate_spatial_frequencies(self, radii, pixel_size_a=1.0):
        """Convert pixel radii to spatial frequencies."""
        # Assuming pixel size in Angstroms
        # Spatial frequency = radius / (image_size * pixel_size)
        # For simplicity, we'll use a reasonable pixel size estimate
        image_size = self.power_spectra.shape[1]  # Assuming square images

        # Convert to spatial frequency in 1/Angstrom
        spatial_freq = radii / (image_size * pixel_size_a)

        return spatial_freq

    def calculate_theoretical_ctf_1d(self, defocus_um, voltage_kv=300, cs_mm=2.7,
                                    amplitude_contrast=0.1, pixel_size_a=1.0):
        """Calculate theoretical 1D CTF profile."""
        # Physical constants and calculations
        wavelength_a = 12.3986 / np.sqrt(voltage_kv * (1022.0 + voltage_kv))

        # Create spatial frequency array
        max_freq = 0.5 / pixel_size_a  # Nyquist frequency
        freq = np.linspace(0.001, max_freq, 1000)  # Avoid zero

        # Convert defocus to meters for calculation
        defocus_m = defocus_um * 1e-6
        cs_m = cs_mm * 1e-3
        wavelength_m = wavelength_a * 1e-10

        # Calculate phase
        chi = (np.pi * wavelength_m * defocus_m * freq**2 +
               0.5 * np.pi * cs_m * wavelength_m**3 * freq**4)

        # Calculate CTF
        ctf = -np.sqrt(1 - amplitude_contrast**2) * np.sin(chi) - amplitude_contrast * np.cos(chi)

        return freq, ctf

    def update_ctf_1d_plot(self):
        """Update the 1D CTF plot showing both radial profile and theoretical CTF fit."""
        if self.parameters.empty:
            return

        # Get current power spectrum and parameters
        current_spectrum = self.power_spectra[self.current_tilt_idx]
        current_params = self.parameters.iloc[self.current_tilt_idx]

        # Extract radial profile from 2D power spectrum
        radii, radial_profile = self.extract_radial_profile(current_spectrum)

        # Convert radii to spatial frequencies
        pixel_size_a = 1.0  # This should ideally come from metadata
        spatial_freq = self.calculate_spatial_frequencies(radii, pixel_size_a)

        # Normalize radial profile to [0, 1] range like in the reference image
        if len(radial_profile) > 0:
            # Normalize to [0, 1] range for display (like PS in the image)
            radial_profile_norm = (radial_profile - radial_profile.min()) / (radial_profile.max() - radial_profile.min())
        else:
            radial_profile_norm = np.array([])

        # Calculate theoretical CTF
        defocus1 = current_params['defocus1_A'] / 10000  # Convert to μm
        defocus2 = current_params['defocus2_A'] / 10000
        avg_defocus = (defocus1 + defocus2) / 2

        freq_theory, ctf_theory = self.calculate_theoretical_ctf_1d(avg_defocus, pixel_size_a=pixel_size_a)

        # Clear and plot
        self.ax_ctf.clear()
        self.ax_ctf.set_facecolor(self.theme['bg_color'])

        # Plot 1: Power Spectrum (PS) - Gray line like in reference image
        if len(spatial_freq) > 0 and len(radial_profile_norm) > 0:
            self.ax_ctf.plot(spatial_freq, radial_profile_norm,
                           color='gray', linewidth=1.5,
                           label='PS', alpha=0.8)

        # Plot 2: Theoretical CTF - Red line like in reference image
        if len(freq_theory) > 0:
            # Limit frequency range to match data
            max_data_freq = spatial_freq.max() if len(spatial_freq) > 0 else 0.3
            mask = freq_theory <= max_data_freq
            self.ax_ctf.plot(freq_theory[mask], ctf_theory[mask],
                           color='red', linewidth=1.5,
                           label='CTF', alpha=0.8)

        # Plot 3: CTF Fit - Blue line like in reference image (if enabled)
        if self.show_ctf_fit and len(freq_theory) > 0:
            # For now, use theoretical as fit - in real implementation this would be actual fit
            max_data_freq = spatial_freq.max() if len(spatial_freq) > 0 else 0.3
            mask = freq_theory <= max_data_freq
            ctf_fit = ctf_theory[mask] * 0.9  # Slightly different to show as separate curve
            self.ax_ctf.plot(freq_theory[mask], ctf_fit,
                           color='cyan', linewidth=1.5,
                           label='FIT', alpha=0.8)

        # Add zero line
        self.ax_ctf.axhline(y=0, color=self.theme['grid_color'], linestyle='-', alpha=0.5)

        # Mark resolution limit
        resolution_limit = current_params.get('resolution_limit_A', 10.0)
        if resolution_limit > 0 and resolution_limit < 50:  # Reasonable resolution
            res_freq = 1.0 / resolution_limit
            max_freq_display = spatial_freq.max() if len(spatial_freq) > 0 else 0.5
            if res_freq < max_freq_display:
                self.ax_ctf.axvline(x=res_freq, color=self.theme['quality_good'],
                                   linestyle=':', alpha=0.8, linewidth=2,
                                   label=f'Resolution Limit ({resolution_limit:.1f}Å)')

        # Add CTF quality and defocus info
        cross_corr = current_params.get('cross_correlation', 0.0)
        quality_color = self.theme['quality_good'] if cross_corr > 0.5 else self.theme['quality_poor']

        info_text = f'Defocus: {avg_defocus:.2f}μm\nQuality: {cross_corr:.3f}'
        self.ax_ctf.text(0.02, 0.98, info_text, transform=self.ax_ctf.transAxes,
                        color=quality_color, fontsize=8, fontweight='bold',
                        verticalalignment='top')

        # Style the plot
        self.ax_ctf.set_title('1D CTF Analysis: Data vs Theory', color=self.theme['text_color'], fontsize=10)
        self.ax_ctf.set_xlabel('Spatial Frequency (1/Å)', color=self.theme['text_color'])
        self.ax_ctf.set_ylabel('Amplitude', color=self.theme['text_color'])
        self.ax_ctf.tick_params(colors=self.theme['text_color'])
        self.ax_ctf.grid(True, alpha=0.3, color=self.theme['grid_color'])

        # Add legend
        if len(spatial_freq) > 0 or self.show_ctf_fit:
            self.ax_ctf.legend(fontsize=7, loc='upper right')

        # Set reasonable limits
        if len(spatial_freq) > 0:
            max_freq = min(spatial_freq.max(), 0.5)  # Don't go beyond Nyquist
            self.ax_ctf.set_xlim(0, max_freq)
        else:
            self.ax_ctf.set_xlim(0, 0.3)
        self.ax_ctf.set_ylim(0.0, 1.0)  # Match reference image range



    def update_ctf_rings(self):
        """Update CTF ring overlay and fit visualization."""
        # Clear existing rings
        for ring in self.ctf_rings:
            ring.remove()
        self.ctf_rings.clear()

        if not self.show_ctf_rings or self.parameters.empty:
            return

        # Get current CTF parameters
        current_params = self.parameters.iloc[self.current_tilt_idx]

        # Get image center and size
        h, w = self.power_spectra[self.current_tilt_idx].shape
        center_x, center_y = w // 2, h // 2

        # Calculate theoretical CTF rings based on actual parameters
        defocus1 = current_params['defocus1_A']
        defocus2 = current_params['defocus2_A']
        avg_defocus = (defocus1 + defocus2) / 2

        # Microscope parameters (typical values - should be configurable)
        voltage_kv = 300  # kV
        cs_mm = 2.7      # mm
        pixel_size_a = 1.0  # Angstrom (this should come from metadata)

        # Calculate electron wavelength
        wavelength_a = 12.3986 / np.sqrt(voltage_kv * (1022.0 + voltage_kv))

        # Calculate CTF zero crossings (simplified)
        # This gives us the radii where CTF crosses zero
        max_radius = min(w, h) // 3

        # Calculate several CTF zero crossings
        zero_crossings = []
        for n in range(1, 8):  # First 7 zeros
            # Simplified formula for CTF zeros
            # Real implementation would solve: chi = (n - 0.5) * pi
            s_zero = np.sqrt((n - 0.5) / (wavelength_a * avg_defocus * 1e-4))  # spatial frequency
            radius_pixels = s_zero * pixel_size_a * max_radius / 0.5  # Convert to pixels

            if radius_pixels < max_radius:
                zero_crossings.append(radius_pixels)

        # Draw CTF rings at zero crossings
        for i, radius in enumerate(zero_crossings):
            # Color rings based on quality
            cross_corr = current_params.get('cross_correlation', 0.0)
            if cross_corr > 0.5:
                ring_color = self.theme['ctf_ring_color']
                alpha = 0.8
            else:
                ring_color = self.theme['quality_poor']
                alpha = 0.5

            # Create ring
            ring = Circle(
                (center_x, center_y),
                radius,
                fill=False,
                color=ring_color,
                alpha=alpha,
                linewidth=1.5 if i < 3 else 1.0,  # Thicker for first few rings
                linestyle='-' if i % 2 == 0 else '--'  # Alternate line styles
            )
            self.ax_main.add_patch(ring)
            self.ctf_rings.append(ring)

        # Add resolution limit circle
        resolution_limit = current_params.get('resolution_limit_A', 10.0)
        if resolution_limit > 0:
            res_radius = max_radius * (3.0 / resolution_limit)  # Approximate conversion
            if res_radius < max_radius:
                res_circle = Circle(
                    (center_x, center_y),
                    res_radius,
                    fill=False,
                    color=self.theme['quality_good'],
                    alpha=0.6,
                    linewidth=2,
                    linestyle=':'
                )
                self.ax_main.add_patch(res_circle)
                self.ctf_rings.append(res_circle)

    def update_info_panel(self):
        """Update the information panel with current tilt data."""
        self.ax_info.clear()
        self.ax_info.axis('off')
        self.ax_info.set_facecolor(self.theme['bg_color'])

        if self.parameters.empty:
            return

        # Get current parameters
        current_params = self.parameters.iloc[self.current_tilt_idx]
        stage_tilt = self.tilt_angles[self.current_tilt_idx] if self.tilt_angles else 'N/A'
        astigmatism_angle = current_params.get('astigmatism_angle', 'N/A')

        # Determine quality color
        cross_corr = current_params.get('cross_correlation', 0.0)
        quality_color = self.theme['quality_good'] if cross_corr > 0.5 else self.theme['quality_poor']

        # Format information with better styling
        info_lines = [
            "📊 TILT INFORMATION",
            "",
            f"Tilt: {self.current_tilt_idx + 1}/{self.n_tilts}",
            f"Stage Tilt: {stage_tilt:.2f}°" if isinstance(stage_tilt, (int, float)) else f"Stage Tilt: {stage_tilt}°",
            "",
            "🔬 CTF PARAMETERS",
            "",
            f"Defocus 1: {current_params['defocus1_A']/10000:.2f} μm",
            f"Defocus 2: {current_params['defocus2_A']/10000:.2f} μm",
            f"Astigmatism: {astigmatism_angle:.1f}°" if isinstance(astigmatism_angle, (int, float)) else f"Astigmatism: {astigmatism_angle}°",
            f"Phase Shift: {current_params['phase_shift_rad']:.3f} rad",
            "",
            "⭐ QUALITY METRICS",
            "",
            f"Cross Corr: {current_params['cross_correlation']:.3f}",
            f"Resolution: {current_params['resolution_limit_A']:.1f} Å",
            "",
            "📏 ASTIGMATISM",
            "",
            f"{abs(current_params['defocus1_A'] - current_params['defocus2_A'])/10000:.3f} μm"
        ]

        # Add text with theme colors
        y_pos = 0.95
        for line in info_lines:
            if line.startswith(('📊', '🔬', '⭐', '📏')):
                # Header lines
                self.ax_info.text(0.05, y_pos, line, transform=self.ax_info.transAxes,
                                 fontsize=10, fontweight='bold', verticalalignment='top',
                                 color=self.theme['accent_color'])
            elif line == "":
                # Empty lines for spacing
                pass
            elif "Cross Corr:" in line:
                # Quality-colored cross correlation
                self.ax_info.text(0.05, y_pos, line, transform=self.ax_info.transAxes,
                                 fontsize=9, verticalalignment='top', fontfamily='monospace',
                                 color=quality_color)
            else:
                # Regular text
                self.ax_info.text(0.05, y_pos, line, transform=self.ax_info.transAxes,
                                 fontsize=9, verticalalignment='top', fontfamily='monospace',
                                 color=self.theme['text_color'])

            y_pos -= 0.04  # Move down for next line

    def toggle_ctf_rings(self, event):
        """Toggle CTF ring display."""
        self.show_ctf_rings = not self.show_ctf_rings
        self.update_display()

    def toggle_log_scale(self, event):
        """Toggle logarithmic scale for 2D display."""
        self.log_scale = not self.log_scale
        self.update_display()

    def toggle_ctf_fit(self, event):
        """Toggle CTF fit display (blue FIT curve)."""
        self.show_ctf_fit = not self.show_ctf_fit
        self.update_display()

    def reset_zoom(self, event):
        """Reset zoom and pan to default."""
        self.zoom_factor = 1.0
        self.pan_offset = [0, 0]
        self.ax_main.set_xlim(0, self.power_spectra.shape[2])
        self.ax_main.set_ylim(0, self.power_spectra.shape[1])
        self.fig.canvas.draw()

    def prev_tilt(self, event):
        """Go to previous tilt."""
        if self.current_tilt_idx > 0:
            self.current_tilt_idx -= 1
            self.slider.set_val(self.current_tilt_idx)

    def next_tilt(self, event):
        """Go to next tilt."""
        if self.current_tilt_idx < self.n_tilts - 1:
            self.current_tilt_idx += 1
            self.slider.set_val(self.current_tilt_idx)

    def on_scroll(self, event):
        """Handle mouse scroll for zooming."""
        if event.inaxes != self.ax_main:
            return

        # Zoom in/out
        scale_factor = 1.1 if event.button == 'up' else 1/1.1

        xlim = self.ax_main.get_xlim()
        ylim = self.ax_main.get_ylim()

        # Get mouse position
        xdata, ydata = event.xdata, event.ydata

        # Calculate new limits
        new_width = (xlim[1] - xlim[0]) * scale_factor
        new_height = (ylim[1] - ylim[0]) * scale_factor

        relx = (xlim[1] - xdata) / (xlim[1] - xlim[0])
        rely = (ylim[1] - ydata) / (ylim[1] - ylim[0])

        self.ax_main.set_xlim([xdata - new_width * (1 - relx), xdata + new_width * relx])
        self.ax_main.set_ylim([ydata - new_height * (1 - rely), ydata + new_height * rely])

        self.fig.canvas.draw()

    def on_mouse_press(self, event):
        """Handle mouse press for panning."""
        if event.inaxes != self.ax_main:
            return
        self.last_mouse_pos = (event.xdata, event.ydata)

    def on_mouse_motion(self, event):
        """Handle mouse motion for panning."""
        if (event.inaxes != self.ax_main or
            not hasattr(self, 'last_mouse_pos') or
            not event.button):
            return

        if self.last_mouse_pos is None:
            return

        dx = event.xdata - self.last_mouse_pos[0]
        dy = event.ydata - self.last_mouse_pos[1]

        xlim = self.ax_main.get_xlim()
        ylim = self.ax_main.get_ylim()

        self.ax_main.set_xlim([xlim[0] - dx, xlim[1] - dx])
        self.ax_main.set_ylim([ylim[0] - dy, ylim[1] - dy])

        self.fig.canvas.draw()

    def show(self):
        """Display the interactive viewer."""
        if self.fig is None:
            self.create_interactive_viewer()
        plt.show()
        return self.fig


def test_ctf_visualizer():
    """Test function for CTF visualizer."""
    from .ctf_parser import CTFDataParser
    import sys

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        # Parse CTF data
        parser = CTFDataParser(test_path)
        ctf_data = parser.parse_all()

        if ctf_data['power_spectra'] is None:
            print("No power spectra available - cannot test visualizer")
            return False

        # Create visualizer
        visualizer = CTF2DVisualizer(ctf_data)

        print(f"Created CTF visualizer for {ctf_data['series_name']}")
        print("Use slider to navigate through tilts")
        print("Use mouse wheel to zoom, drag to pan")
        print("Use buttons to toggle rings and log scale")

        # Show interactive viewer
        visualizer.show()

        return True

    except Exception as e:
        print(f"Error testing CTF visualizer: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_ctf_visualizer()
