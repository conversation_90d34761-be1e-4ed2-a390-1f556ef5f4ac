#!/usr/bin/env python3
"""
AreTomo3 GUI Interactive Plotter
Interactive plotting functionality using Plotly for web-based visualizations.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

# Plotly imports with fallbacks
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

logger = logging.getLogger(__name__)


def create_ctf_resolution_plot(ctf_data: Dict[str, Any], use_broken_axis: bool = False) -> str:
    """Create interactive CTF resolution plot using Plotly."""
    if not PLOTLY_AVAILABLE:
        logger.error("Plotly not available for interactive plotting")
        return "<html><body><h2>Plotly not available</h2><p>Please install plotly for interactive plots.</p></body></html>"
    
    try:
        logger.info("Creating interactive CTF resolution plot")
        
        # Extract CTF parameters
        if 'ctf_parameters' in ctf_data:
            # Handle nested format
            all_params = []
            for series_name, series_data in ctf_data['ctf_parameters'].items():
                if 'parameters' in series_data and not series_data['parameters'].empty:
                    df = series_data['parameters'].copy()
                    df['series'] = series_name
                    all_params.append(df)
            
            if not all_params:
                return _create_no_data_html("No CTF parameters found")
            
            combined_df = pd.concat(all_params, ignore_index=True)
        else:
            return _create_no_data_html("No CTF data structure found")
        
        # Create interactive plot
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Resolution vs Tilt Angle', 'Resolution Distribution', 
                          'Resolution vs Defocus', 'Resolution Timeline'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Plot 1: Resolution vs Tilt Angle
        if 'tilt_angle' in combined_df.columns and 'resolution_limit_A' in combined_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=combined_df['tilt_angle'],
                    y=combined_df['resolution_limit_A'],
                    mode='markers+lines',
                    name='Resolution vs Tilt',
                    marker=dict(
                        size=8,
                        color=combined_df['cross_correlation'] if 'cross_correlation' in combined_df.columns else 'blue',
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="Cross Correlation")
                    ),
                    hovertemplate='<b>Tilt Angle:</b> %{x:.1f}°<br>' +
                                '<b>Resolution:</b> %{y:.1f} Å<br>' +
                                '<b>CC:</b> %{marker.color:.3f}<extra></extra>'
                ),
                row=1, col=1
            )
        
        # Plot 2: Resolution Distribution
        if 'resolution_limit_A' in combined_df.columns:
            fig.add_trace(
                go.Histogram(
                    x=combined_df['resolution_limit_A'],
                    nbinsx=20,
                    name='Resolution Distribution',
                    marker_color='lightblue',
                    opacity=0.7
                ),
                row=1, col=2
            )
        
        # Plot 3: Resolution vs Defocus
        if 'defocus1_A' in combined_df.columns and 'resolution_limit_A' in combined_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=combined_df['defocus1_A'] / 10000,  # Convert to μm
                    y=combined_df['resolution_limit_A'],
                    mode='markers',
                    name='Resolution vs Defocus',
                    marker=dict(size=8, color='red', opacity=0.6),
                    hovertemplate='<b>Defocus:</b> %{x:.2f} μm<br>' +
                                '<b>Resolution:</b> %{y:.1f} Å<extra></extra>'
                ),
                row=2, col=1
            )
        
        # Plot 4: Resolution Timeline (if micrograph index available)
        if 'resolution_limit_A' in combined_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(combined_df))),
                    y=combined_df['resolution_limit_A'],
                    mode='lines+markers',
                    name='Resolution Timeline',
                    line=dict(color='green', width=2),
                    marker=dict(size=6),
                    hovertemplate='<b>Image #:</b> %{x}<br>' +
                                '<b>Resolution:</b> %{y:.1f} Å<extra></extra>'
                ),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            title=dict(
                text="Interactive CTF Resolution Analysis",
                x=0.5,
                font=dict(size=20)
            ),
            height=800,
            showlegend=True,
            template='plotly_white'
        )
        
        # Update axes labels
        fig.update_xaxes(title_text="Tilt Angle (°)", row=1, col=1)
        fig.update_yaxes(title_text="Resolution (Å)", row=1, col=1)
        
        fig.update_xaxes(title_text="Resolution (Å)", row=1, col=2)
        fig.update_yaxes(title_text="Count", row=1, col=2)
        
        fig.update_xaxes(title_text="Defocus (μm)", row=2, col=1)
        fig.update_yaxes(title_text="Resolution (Å)", row=2, col=1)
        
        fig.update_xaxes(title_text="Image Number", row=2, col=2)
        fig.update_yaxes(title_text="Resolution (Å)", row=2, col=2)
        
        # Convert to HTML
        html_content = pyo.plot(fig, output_type='div', include_plotlyjs=True)
        
        # Wrap in full HTML
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>CTF Resolution Analysis</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .info {{ background-color: #e7f3ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="info">
                <h3>CTF Resolution Analysis</h3>
                <p>Interactive plots showing CTF resolution analysis results. Hover over points for details.</p>
                <p><strong>Data points:</strong> {len(combined_df)} micrographs</p>
                <p><strong>Average resolution:</strong> {combined_df['resolution_limit_A'].mean():.1f} Å</p>
            </div>
            {html_content}
        </body>
        </html>
        """
        
        logger.info(f"Created interactive CTF resolution plot with {len(combined_df)} data points")
        return full_html
        
    except Exception as e:
        logger.error(f"Error creating CTF resolution plot: {e}")
        return _create_error_html(f"Error creating CTF resolution plot: {str(e)}")


def create_ctf_defocus_plot(ctf_data: Dict[str, Any]) -> str:
    """Create interactive CTF defocus plot using Plotly."""
    if not PLOTLY_AVAILABLE:
        logger.error("Plotly not available for interactive plotting")
        return "<html><body><h2>Plotly not available</h2><p>Please install plotly for interactive plots.</p></body></html>"
    
    try:
        logger.info("Creating interactive CTF defocus plot")
        
        # Extract CTF parameters
        if 'ctf_parameters' in ctf_data:
            # Handle nested format
            all_params = []
            for series_name, series_data in ctf_data['ctf_parameters'].items():
                if 'parameters' in series_data and not series_data['parameters'].empty:
                    df = series_data['parameters'].copy()
                    df['series'] = series_name
                    all_params.append(df)
            
            if not all_params:
                return _create_no_data_html("No CTF parameters found")
            
            combined_df = pd.concat(all_params, ignore_index=True)
        else:
            return _create_no_data_html("No CTF data structure found")
        
        # Create interactive plot
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Defocus vs Tilt Angle', 'Defocus Distribution', 
                          'Defocus Astigmatism', 'Defocus Quality'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Plot 1: Defocus vs Tilt Angle
        if 'tilt_angle' in combined_df.columns and 'defocus1_A' in combined_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=combined_df['tilt_angle'],
                    y=combined_df['defocus1_A'] / 10000,  # Convert to μm
                    mode='markers+lines',
                    name='Defocus 1',
                    marker=dict(size=8, color='blue'),
                    hovertemplate='<b>Tilt Angle:</b> %{x:.1f}°<br>' +
                                '<b>Defocus 1:</b> %{y:.2f} μm<extra></extra>'
                ),
                row=1, col=1
            )
            
            if 'defocus2_A' in combined_df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=combined_df['tilt_angle'],
                        y=combined_df['defocus2_A'] / 10000,  # Convert to μm
                        mode='markers+lines',
                        name='Defocus 2',
                        marker=dict(size=8, color='red'),
                        hovertemplate='<b>Tilt Angle:</b> %{x:.1f}°<br>' +
                                    '<b>Defocus 2:</b> %{y:.2f} μm<extra></extra>'
                    ),
                    row=1, col=1
                )
        
        # Plot 2: Defocus Distribution
        if 'defocus1_A' in combined_df.columns:
            fig.add_trace(
                go.Histogram(
                    x=combined_df['defocus1_A'] / 10000,
                    nbinsx=20,
                    name='Defocus Distribution',
                    marker_color='lightgreen',
                    opacity=0.7
                ),
                row=1, col=2
            )
        
        # Plot 3: Defocus Astigmatism
        if 'defocus1_A' in combined_df.columns and 'defocus2_A' in combined_df.columns:
            astigmatism = abs(combined_df['defocus1_A'] - combined_df['defocus2_A']) / 10000
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(combined_df))),
                    y=astigmatism,
                    mode='markers+lines',
                    name='Astigmatism',
                    marker=dict(
                        size=8,
                        color=combined_df['cross_correlation'] if 'cross_correlation' in combined_df.columns else 'purple',
                        colorscale='Plasma',
                        showscale=True,
                        colorbar=dict(title="Cross Correlation", x=1.1)
                    ),
                    hovertemplate='<b>Image #:</b> %{x}<br>' +
                                '<b>Astigmatism:</b> %{y:.3f} μm<br>' +
                                '<b>CC:</b> %{marker.color:.3f}<extra></extra>'
                ),
                row=2, col=1
            )
        
        # Plot 4: Defocus Quality (Cross-correlation vs Defocus)
        if 'defocus1_A' in combined_df.columns and 'cross_correlation' in combined_df.columns:
            avg_defocus = combined_df['defocus1_A'] / 10000
            if 'defocus2_A' in combined_df.columns:
                avg_defocus = (combined_df['defocus1_A'] + combined_df['defocus2_A']) / 2 / 10000
            
            fig.add_trace(
                go.Scatter(
                    x=avg_defocus,
                    y=combined_df['cross_correlation'],
                    mode='markers',
                    name='Quality vs Defocus',
                    marker=dict(
                        size=10,
                        color=combined_df['resolution_limit_A'] if 'resolution_limit_A' in combined_df.columns else 'orange',
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="Resolution (Å)", x=1.2)
                    ),
                    hovertemplate='<b>Defocus:</b> %{x:.2f} μm<br>' +
                                '<b>Cross Correlation:</b> %{y:.3f}<br>' +
                                '<b>Resolution:</b> %{marker.color:.1f} Å<extra></extra>'
                ),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            title=dict(
                text="Interactive CTF Defocus Analysis",
                x=0.5,
                font=dict(size=20)
            ),
            height=800,
            showlegend=True,
            template='plotly_white'
        )
        
        # Update axes labels
        fig.update_xaxes(title_text="Tilt Angle (°)", row=1, col=1)
        fig.update_yaxes(title_text="Defocus (μm)", row=1, col=1)
        
        fig.update_xaxes(title_text="Defocus (μm)", row=1, col=2)
        fig.update_yaxes(title_text="Count", row=1, col=2)
        
        fig.update_xaxes(title_text="Image Number", row=2, col=1)
        fig.update_yaxes(title_text="Astigmatism (μm)", row=2, col=1)
        
        fig.update_xaxes(title_text="Average Defocus (μm)", row=2, col=2)
        fig.update_yaxes(title_text="Cross Correlation", row=2, col=2)
        
        # Convert to HTML
        html_content = pyo.plot(fig, output_type='div', include_plotlyjs=True)
        
        # Wrap in full HTML
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>CTF Defocus Analysis</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .info {{ background-color: #e7f3ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="info">
                <h3>CTF Defocus Analysis</h3>
                <p>Interactive plots showing CTF defocus analysis results. Hover over points for details.</p>
                <p><strong>Data points:</strong> {len(combined_df)} micrographs</p>
                <p><strong>Average defocus:</strong> {combined_df['defocus1_A'].mean() / 10000:.2f} μm</p>
                {f"<p><strong>Average astigmatism:</strong> {abs(combined_df['defocus1_A'] - combined_df['defocus2_A']).mean() / 10000:.3f} μm</p>" if 'defocus2_A' in combined_df.columns else ""}
            </div>
            {html_content}
        </body>
        </html>
        """
        
        logger.info(f"Created interactive CTF defocus plot with {len(combined_df)} data points")
        return full_html
        
    except Exception as e:
        logger.error(f"Error creating CTF defocus plot: {e}")
        return _create_error_html(f"Error creating CTF defocus plot: {str(e)}")


def _create_no_data_html(message: str) -> str:
    """Create HTML for no data message."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>No Data Available</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; text-align: center; }}
            .message {{ background-color: #fff3cd; padding: 20px; border-radius: 5px; border: 1px solid #ffeaa7; }}
        </style>
    </head>
    <body>
        <div class="message">
            <h2>No Data Available</h2>
            <p>{message}</p>
            <p>Please ensure CTF estimation has been completed and data is available.</p>
        </div>
    </body>
    </html>
    """


def _create_error_html(error_message: str) -> str:
    """Create HTML for error message."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Plot Error</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; text-align: center; }}
            .error {{ background-color: #f8d7da; padding: 20px; border-radius: 5px; border: 1px solid #f5c6cb; }}
        </style>
    </head>
    <body>
        <div class="error">
            <h2>Plot Generation Error</h2>
            <p>{error_message}</p>
            <p>Please check the logs for more details.</p>
        </div>
    </body>
    </html>
    """
