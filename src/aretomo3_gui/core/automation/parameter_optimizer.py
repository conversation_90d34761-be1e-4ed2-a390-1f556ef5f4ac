"""Parameter optimization for AreTomo3 processing."""
from typing import Dict, List, Optional, Tuple, Union

class ParameterOptimizer:
    """Optimize AreTomo3 parameters based on sample characteristics."""

    def __init__(self) -> None:
        """Initialize parameter optimizer."""
        self.default_params = {
            'binning': 1,
            'patch_x': 100,
            'patch_y': 100,
            'iterations': 5,
            'tolerance': 0.5
        }

    def optimize_parameters(self, 
                          sample_thickness: float,
                          pixel_size: float,
                          tilt_range: Tuple[float, float],
                          sample_type: str = 'general'
                          ) -> Dict[str, Union[int, float]]:
        """
        Optimize processing parameters based on sample characteristics.

        Args:
            sample_thickness: Sample thickness in nm
            pixel_size: Pixel size in Angstroms
            tilt_range: Tuple of (min_tilt, max_tilt) in degrees
            sample_type: Type of sample ('general', 'cellular', 'material')

        Returns:
            Dict of optimized parameters
        """
        params = self.default_params.copy()

        # Adjust binning based on pixel size
        if pixel_size < 2.0:
            params['binning'] = 2
        elif pixel_size < 1.0:
            params['binning'] = 4

        # Adjust patch size based on sample thickness
        if sample_thickness > 500:  # thick sample
            params['patch_x'] = 200
            params['patch_y'] = 200
        elif sample_thickness < 100:  # thin sample
            params['patch_x'] = 64
            params['patch_y'] = 64

        # Adjust iterations based on tilt range
        tilt_min, tilt_max = tilt_range
        if abs(tilt_max - tilt_min) > 120:
            params['iterations'] = 7
            params['tolerance'] = 0.3

        return params
