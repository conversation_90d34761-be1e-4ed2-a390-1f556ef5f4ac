#!/usr/bin/env python3
"""
Intelligent workflow management for AreTomo3 processing.
Provides automated dataset organization, parameter optimization, and processing scheduling.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time
from datetime import datetime

from PyQt6.QtCore import QObject, pyqtSignal, QTimer

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """Status of workflow execution."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DatasetType(Enum):
    """Types of datasets for automatic classification."""
    UNKNOWN = "unknown"
    HIGH_RESOLUTION = "high_resolution"
    MEDIUM_RESOLUTION = "medium_resolution"
    LOW_RESOLUTION = "low_resolution"
    PHASE_PLATE = "phase_plate"
    CRYO_FIB = "cryo_fib"
    CELLULAR = "cellular"
    PURIFIED_PROTEIN = "purified_protein"


@dataclass
class DatasetInfo:
    """Information about a dataset."""
    name: str
    path: str
    file_count: int
    tilt_range: Tuple[float, float]
    pixel_size: float
    voltage: int
    dataset_type: DatasetType = DatasetType.UNKNOWN
    estimated_quality: float = 0.0
    processing_priority: int = 1
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class WorkflowTask:
    """A single workflow task."""
    id: str
    dataset: DatasetInfo
    parameters: Dict[str, Any]
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    output_files: List[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.output_files is None:
            self.output_files = []


class WorkflowManager(QObject):
    """Intelligent workflow manager for AreTomo3 processing."""
    
    # Signals
    dataset_detected = pyqtSignal(object)  # DatasetInfo
    workflow_started = pyqtSignal(str)  # task_id
    workflow_progress = pyqtSignal(str, float)  # task_id, progress
    workflow_completed = pyqtSignal(str, bool, str)  # task_id, success, message
    queue_updated = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.tasks: Dict[str, WorkflowTask] = {}
        self.processing_queue: List[str] = []
        self.active_tasks: Dict[str, Any] = {}  # task_id -> worker
        self.max_concurrent_tasks = 2
        
        # Dataset monitoring
        self.monitored_directories: List[str] = []
        self.dataset_cache: Dict[str, DatasetInfo] = {}
        
        # Workflow templates
        self.workflow_templates: Dict[str, Dict[str, Any]] = {}
        self.load_default_templates()
        
        # Processing timer
        self.processing_timer = QTimer()
        self.processing_timer.timeout.connect(self.process_queue)
        self.processing_timer.start(5000)  # Check every 5 seconds
        
        logger.info("Workflow manager initialized")
    
    def load_default_templates(self):
        """Load default workflow templates."""
        self.workflow_templates = {
            "high_resolution": {
                "name": "High Resolution Processing",
                "description": "Optimized for high-resolution datasets",
                "parameters": {
                    "mc_bin": 1.0,
                    "mc_patch": [5, 5],
                    "at_bin": [1.0, 2.0, 0.0],
                    "vol_z": 2048,
                    "sart": [30, 5],
                    "corr_ctf": [1, 20]
                }
            },
            "medium_resolution": {
                "name": "Medium Resolution Processing",
                "description": "Balanced processing for medium resolution",
                "parameters": {
                    "mc_bin": 2.0,
                    "mc_patch": [3, 3],
                    "at_bin": [2.0, 4.0, 0.0],
                    "vol_z": 1024,
                    "sart": [20, 5],
                    "corr_ctf": [1, 15]
                }
            },
            "fast_processing": {
                "name": "Fast Processing",
                "description": "Quick processing for screening",
                "parameters": {
                    "mc_bin": 4.0,
                    "mc_patch": [2, 2],
                    "at_bin": [4.0, 8.0, 0.0],
                    "vol_z": 512,
                    "sart": [10, 5],
                    "corr_ctf": [0, 0]
                }
            },
            "phase_plate": {
                "name": "Phase Plate Processing",
                "description": "Optimized for phase plate data",
                "parameters": {
                    "mc_bin": 1.0,
                    "mc_patch": [4, 4],
                    "at_bin": [1.0, 2.0, 0.0],
                    "vol_z": 1024,
                    "sart": [25, 5],
                    "corr_ctf": [1, 25],
                    "ext_phase": [90.0, 30.0]
                }
            }
        }
    
    def add_monitored_directory(self, directory: str):
        """Add a directory to monitor for new datasets."""
        if directory not in self.monitored_directories:
            self.monitored_directories.append(directory)
            self.scan_directory(directory)
            logger.info(f"Added monitored directory: {directory}")
    
    def remove_monitored_directory(self, directory: str):
        """Remove a directory from monitoring."""
        if directory in self.monitored_directories:
            self.monitored_directories.remove(directory)
            logger.info(f"Removed monitored directory: {directory}")
    
    def scan_directory(self, directory: str) -> List[DatasetInfo]:
        """Scan directory for datasets and classify them."""
        datasets = []
        
        try:
            path = Path(directory)
            if not path.exists():
                logger.warning(f"Directory does not exist: {directory}")
                return datasets
            
            # Look for tilt series patterns
            for subdir in path.iterdir():
                if subdir.is_dir():
                    dataset = self.analyze_dataset(str(subdir))
                    if dataset:
                        datasets.append(dataset)
                        self.dataset_cache[dataset.name] = dataset
                        self.dataset_detected.emit(dataset)
            
            logger.info(f"Found {len(datasets)} datasets in {directory}")
            
        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {e}")
        
        return datasets
    
    def analyze_dataset(self, dataset_path: str) -> Optional[DatasetInfo]:
        """Analyze a dataset directory and extract information."""
        try:
            path = Path(dataset_path)
            
            # Count relevant files
            eer_files = list(path.glob("*.eer"))
            mrc_files = list(path.glob("*.mrc"))
            mdoc_files = list(path.glob("*.mdoc"))
            
            if not (eer_files or mrc_files):
                return None
            
            file_count = len(eer_files) + len(mrc_files)
            
            # Extract metadata from MDOC files
            pixel_size = 1.0
            voltage = 300
            tilt_range = (-60.0, 60.0)
            
            if mdoc_files:
                metadata = self.parse_mdoc_metadata(mdoc_files[0])
                pixel_size = metadata.get('pixel_size', pixel_size)
                voltage = metadata.get('voltage', voltage)
                tilt_range = metadata.get('tilt_range', tilt_range)
            
            # Classify dataset type
            dataset_type = self.classify_dataset_type(
                pixel_size, voltage, file_count, tilt_range
            )
            
            # Estimate quality
            quality = self.estimate_dataset_quality(
                dataset_path, file_count, pixel_size
            )
            
            # Determine processing priority
            priority = self.calculate_priority(dataset_type, quality, file_count)
            
            dataset = DatasetInfo(
                name=path.name,
                path=dataset_path,
                file_count=file_count,
                tilt_range=tilt_range,
                pixel_size=pixel_size,
                voltage=voltage,
                dataset_type=dataset_type,
                estimated_quality=quality,
                processing_priority=priority
            )
            
            logger.info(f"Analyzed dataset: {dataset.name} ({dataset_type.value})")
            return dataset
            
        except Exception as e:
            logger.error(f"Error analyzing dataset {dataset_path}: {e}")
            return None
    
    def parse_mdoc_metadata(self, mdoc_file: Path) -> Dict[str, Any]:
        """Parse metadata from MDOC file."""
        metadata = {}
        
        try:
            with open(mdoc_file, 'r') as f:
                content = f.read()
            
            # Extract key parameters
            import re
            
            pixel_match = re.search(r'PixelSpacing\s*=\s*([\d.]+)', content)
            if pixel_match:
                metadata['pixel_size'] = float(pixel_match.group(1))
            
            voltage_match = re.search(r'Voltage\s*=\s*([\d.]+)', content)
            if voltage_match:
                metadata['voltage'] = int(float(voltage_match.group(1)))
            
            # Extract tilt angles
            angles = []
            for match in re.finditer(r'TiltAngle\s*=\s*([-\d.]+)', content):
                angles.append(float(match.group(1)))
            
            if angles:
                metadata['tilt_range'] = (min(angles), max(angles))
            
        except Exception as e:
            logger.error(f"Error parsing MDOC file {mdoc_file}: {e}")
        
        return metadata
    
    def classify_dataset_type(self, pixel_size: float, voltage: int, 
                            file_count: int, tilt_range: Tuple[float, float]) -> DatasetType:
        """Classify dataset type based on acquisition parameters."""
        
        # High resolution criteria
        if pixel_size < 2.0 and file_count > 40:
            return DatasetType.HIGH_RESOLUTION
        
        # Medium resolution
        elif pixel_size < 4.0 and file_count > 30:
            return DatasetType.MEDIUM_RESOLUTION
        
        # Low resolution or screening
        elif pixel_size >= 4.0 or file_count < 30:
            return DatasetType.LOW_RESOLUTION
        
        # Phase plate detection (limited tilt range)
        tilt_span = abs(tilt_range[1] - tilt_range[0])
        if tilt_span < 100:  # Less than typical ±60° range
            return DatasetType.PHASE_PLATE
        
        return DatasetType.UNKNOWN
    
    def estimate_dataset_quality(self, dataset_path: str, file_count: int, 
                               pixel_size: float) -> float:
        """Estimate dataset quality score (0-1)."""
        quality = 0.5  # Base quality
        
        # File count contribution
        if file_count > 60:
            quality += 0.2
        elif file_count > 40:
            quality += 0.1
        elif file_count < 20:
            quality -= 0.2
        
        # Pixel size contribution
        if pixel_size < 2.0:
            quality += 0.2
        elif pixel_size < 3.0:
            quality += 0.1
        elif pixel_size > 5.0:
            quality -= 0.1
        
        # TODO: Add more sophisticated quality metrics
        # - Motion correction metrics
        # - CTF estimation quality
        # - Dose distribution
        
        return max(0.0, min(1.0, quality))
    
    def calculate_priority(self, dataset_type: DatasetType, quality: float, 
                         file_count: int) -> int:
        """Calculate processing priority (1=highest, 5=lowest)."""
        
        # Base priority by type
        type_priorities = {
            DatasetType.HIGH_RESOLUTION: 1,
            DatasetType.MEDIUM_RESOLUTION: 2,
            DatasetType.PHASE_PLATE: 2,
            DatasetType.LOW_RESOLUTION: 3,
            DatasetType.CRYO_FIB: 1,
            DatasetType.CELLULAR: 2,
            DatasetType.PURIFIED_PROTEIN: 3,
            DatasetType.UNKNOWN: 4
        }
        
        priority = type_priorities.get(dataset_type, 4)
        
        # Adjust by quality
        if quality > 0.8:
            priority = max(1, priority - 1)
        elif quality < 0.3:
            priority = min(5, priority + 1)
        
        return priority
    
    def create_workflow_task(self, dataset: DatasetInfo, 
                           template_name: Optional[str] = None,
                           custom_parameters: Optional[Dict[str, Any]] = None) -> str:
        """Create a new workflow task."""
        
        task_id = f"task_{dataset.name}_{int(time.time())}"
        
        # Select appropriate template
        if template_name is None:
            template_name = self.select_optimal_template(dataset)
        
        # Get base parameters from template
        template = self.workflow_templates.get(template_name, {})
        parameters = template.get('parameters', {}).copy()
        
        # Apply custom parameters
        if custom_parameters:
            parameters.update(custom_parameters)
        
        # Add dataset-specific parameters
        parameters.update({
            'input_prefix': dataset.path,
            'pixel_size': dataset.pixel_size,
            'voltage': dataset.voltage,
            'output_dir': os.path.join(dataset.path, 'processed')
        })
        
        task = WorkflowTask(
            id=task_id,
            dataset=dataset,
            parameters=parameters
        )
        
        self.tasks[task_id] = task
        self.add_to_queue(task_id)
        
        logger.info(f"Created workflow task: {task_id} using template {template_name}")
        return task_id
    
    def select_optimal_template(self, dataset: DatasetInfo) -> str:
        """Select the optimal workflow template for a dataset."""
        
        type_templates = {
            DatasetType.HIGH_RESOLUTION: "high_resolution",
            DatasetType.MEDIUM_RESOLUTION: "medium_resolution",
            DatasetType.LOW_RESOLUTION: "fast_processing",
            DatasetType.PHASE_PLATE: "phase_plate"
        }
        
        return type_templates.get(dataset.dataset_type, "medium_resolution")
    
    def add_to_queue(self, task_id: str):
        """Add task to processing queue with priority ordering."""
        if task_id not in self.processing_queue:
            task = self.tasks[task_id]
            
            # Insert based on priority
            inserted = False
            for i, existing_id in enumerate(self.processing_queue):
                existing_task = self.tasks[existing_id]
                if task.dataset.processing_priority < existing_task.dataset.processing_priority:
                    self.processing_queue.insert(i, task_id)
                    inserted = True
                    break
            
            if not inserted:
                self.processing_queue.append(task_id)
            
            self.queue_updated.emit()
            logger.info(f"Added task {task_id} to queue (priority {task.dataset.processing_priority})")
    
    def process_queue(self):
        """Process tasks in the queue."""
        if len(self.active_tasks) >= self.max_concurrent_tasks:
            return
        
        if not self.processing_queue:
            return
        
        # Get next task
        task_id = self.processing_queue.pop(0)
        task = self.tasks.get(task_id)
        
        if not task or task.status != WorkflowStatus.PENDING:
            return
        
        # Start processing
        self.start_task(task_id)
    
    def start_task(self, task_id: str):
        """Start processing a workflow task."""
        task = self.tasks[task_id]
        task.status = WorkflowStatus.RUNNING
        task.started_at = datetime.now()
        
        self.active_tasks[task_id] = None  # Placeholder for actual worker
        
        self.workflow_started.emit(task_id)
        logger.info(f"Started workflow task: {task_id}")
        
        # TODO: Implement actual AreTomo3 processing
        # For now, simulate processing
        QTimer.singleShot(10000, lambda: self.complete_task(task_id, True, "Simulated completion"))
    
    def complete_task(self, task_id: str, success: bool, message: str):
        """Complete a workflow task."""
        task = self.tasks.get(task_id)
        if not task:
            return
        
        task.status = WorkflowStatus.COMPLETED if success else WorkflowStatus.FAILED
        task.completed_at = datetime.now()
        
        if not success:
            task.error_message = message
        
        # Remove from active tasks
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        
        self.workflow_completed.emit(task_id, success, message)
        self.queue_updated.emit()
        
        logger.info(f"Completed workflow task: {task_id} ({'success' if success else 'failed'})")
    
    def cancel_task(self, task_id: str):
        """Cancel a workflow task."""
        task = self.tasks.get(task_id)
        if not task:
            return
        
        if task.status == WorkflowStatus.RUNNING:
            # TODO: Stop actual processing
            pass
        
        task.status = WorkflowStatus.CANCELLED
        
        # Remove from queue and active tasks
        if task_id in self.processing_queue:
            self.processing_queue.remove(task_id)
        
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        
        self.queue_updated.emit()
        logger.info(f"Cancelled workflow task: {task_id}")
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status."""
        return {
            'total_tasks': len(self.tasks),
            'pending_tasks': len(self.processing_queue),
            'active_tasks': len(self.active_tasks),
            'completed_tasks': len([t for t in self.tasks.values() 
                                  if t.status == WorkflowStatus.COMPLETED]),
            'failed_tasks': len([t for t in self.tasks.values() 
                               if t.status == WorkflowStatus.FAILED])
        }
    
    def export_workflow_report(self, output_file: str):
        """Export workflow processing report."""
        report = {
            'generated_at': datetime.now().isoformat(),
            'queue_status': self.get_queue_status(),
            'tasks': [asdict(task) for task in self.tasks.values()],
            'templates': self.workflow_templates
        }
        
        try:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Exported workflow report to: {output_file}")
            
        except Exception as e:
            logger.error(f"Error exporting workflow report: {e}")
