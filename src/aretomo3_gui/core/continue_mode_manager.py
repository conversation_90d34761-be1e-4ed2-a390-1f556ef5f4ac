"""
AreTomo3 Continue Mode Manager
Handles continue, pause, and resume functionality for AreTomo3 processing.
"""

import os
import json
import time
import signal
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

class ProcessingState(Enum):
    """Processing state enumeration."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    INTERRUPTED = "interrupted"

@dataclass
class ContinueSession:
    """Continue session data structure."""
    session_id: str
    input_directory: str
    output_directory: str
    series_name: str
    command: str
    state: ProcessingState
    process_id: Optional[int]
    start_time: float
    pause_time: Optional[float]
    resume_time: Optional[float]
    completed_frames: int
    total_frames: int
    current_step: str
    error_message: Optional[str]

class ContinueModeManager:
    """Manages AreTomo3 continue mode functionality."""
    
    def __init__(self, state_dir: Optional[str] = None):
        """Initialize continue mode manager."""
        self.state_dir = Path(state_dir) if state_dir else Path.home() / ".aretomo3_gui" / "continue_states"
        self.state_dir.mkdir(parents=True, exist_ok=True)
        
        self.active_sessions: Dict[str, ContinueSession] = {}
        self.process_handles: Dict[str, subprocess.Popen] = {}
        
        # Load existing sessions
        self.load_existing_sessions()
        
        logger.info(f"Continue mode manager initialized with state directory: {self.state_dir}")

    def start_processing(self, series_name: str, command: str, input_dir: str, 
                        output_dir: str) -> Tuple[bool, str]:
        """Start AreTomo3 processing with continue mode support."""
        try:
            session_id = f"continue_{series_name}_{int(time.time())}"
            
            # Create continue session
            session = ContinueSession(
                session_id=session_id,
                input_directory=input_dir,
                output_directory=output_dir,
                series_name=series_name,
                command=command,
                state=ProcessingState.RUNNING,
                process_id=None,
                start_time=time.time(),
                pause_time=None,
                resume_time=None,
                completed_frames=0,
                total_frames=0,
                current_step="initialization",
                error_message=None
            )
            
            # Add continue mode parameters to command
            continue_command = self.add_continue_parameters(command, output_dir, series_name)
            
            # Start process
            process = subprocess.Popen(
                continue_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # Create new process group for clean termination
            )
            
            session.process_id = process.pid
            self.active_sessions[session_id] = session
            self.process_handles[session_id] = process
            
            # Save session state
            self.save_session_state(session)
            
            logger.info(f"Started continue mode processing for {series_name} with PID {process.pid}")
            return True, session_id
            
        except Exception as e:
            logger.error(f"Error starting continue mode processing: {e}")
            return False, str(e)

    def pause_processing(self, session_id: str) -> bool:
        """Pause AreTomo3 processing."""
        try:
            if session_id not in self.active_sessions:
                logger.warning(f"Session not found: {session_id}")
                return False
            
            session = self.active_sessions[session_id]
            process = self.process_handles.get(session_id)
            
            if not process or session.state != ProcessingState.RUNNING:
                logger.warning(f"Cannot pause session {session_id}: not running")
                return False
            
            # Send SIGSTOP to pause the process
            os.killpg(os.getpgid(process.pid), signal.SIGSTOP)
            
            session.state = ProcessingState.PAUSED
            session.pause_time = time.time()
            
            self.save_session_state(session)
            
            logger.info(f"Paused processing for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing processing: {e}")
            return False

    def resume_processing(self, session_id: str) -> bool:
        """Resume paused AreTomo3 processing."""
        try:
            if session_id not in self.active_sessions:
                logger.warning(f"Session not found: {session_id}")
                return False
            
            session = self.active_sessions[session_id]
            process = self.process_handles.get(session_id)
            
            if not process or session.state != ProcessingState.PAUSED:
                logger.warning(f"Cannot resume session {session_id}: not paused")
                return False
            
            # Send SIGCONT to resume the process
            os.killpg(os.getpgid(process.pid), signal.SIGCONT)
            
            session.state = ProcessingState.RUNNING
            session.resume_time = time.time()
            
            self.save_session_state(session)
            
            logger.info(f"Resumed processing for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error resuming processing: {e}")
            return False

    def stop_processing(self, session_id: str) -> bool:
        """Stop AreTomo3 processing."""
        try:
            if session_id not in self.active_sessions:
                logger.warning(f"Session not found: {session_id}")
                return False
            
            session = self.active_sessions[session_id]
            process = self.process_handles.get(session_id)
            
            if process:
                # Terminate process group
                try:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                    
                    # Wait for graceful termination
                    try:
                        process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        # Force kill if not terminated gracefully
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                        process.wait()
                        
                except ProcessLookupError:
                    # Process already terminated
                    pass
                
                del self.process_handles[session_id]
            
            session.state = ProcessingState.INTERRUPTED
            self.save_session_state(session)
            
            logger.info(f"Stopped processing for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping processing: {e}")
            return False

    def continue_processing(self, session_id: str) -> Tuple[bool, str]:
        """Continue interrupted AreTomo3 processing."""
        try:
            if session_id not in self.active_sessions:
                logger.warning(f"Session not found: {session_id}")
                return False, "Session not found"
            
            session = self.active_sessions[session_id]
            
            if session.state not in [ProcessingState.INTERRUPTED, ProcessingState.FAILED]:
                logger.warning(f"Cannot continue session {session_id}: state is {session.state}")
                return False, f"Invalid state: {session.state}"
            
            # Create continue command with resume parameters
            continue_command = self.create_continue_command(session)
            
            # Start new process
            process = subprocess.Popen(
                continue_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            session.process_id = process.pid
            session.state = ProcessingState.RUNNING
            session.resume_time = time.time()
            
            self.process_handles[session_id] = process
            self.save_session_state(session)
            
            logger.info(f"Continued processing for session {session_id} with PID {process.pid}")
            return True, session_id
            
        except Exception as e:
            logger.error(f"Error continuing processing: {e}")
            return False, str(e)

    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """Get status of a continue session."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        process = self.process_handles.get(session_id)
        
        # Check if process is still running
        if process:
            poll_result = process.poll()
            if poll_result is not None:
                # Process has terminated
                if poll_result == 0:
                    session.state = ProcessingState.COMPLETED
                else:
                    session.state = ProcessingState.FAILED
                    session.error_message = f"Process exited with code {poll_result}"
                
                del self.process_handles[session_id]
                self.save_session_state(session)
        
        # Calculate runtime
        runtime = time.time() - session.start_time
        if session.pause_time and session.resume_time:
            pause_duration = session.resume_time - session.pause_time
            runtime -= pause_duration
        
        return {
            "session_id": session_id,
            "series_name": session.series_name,
            "state": session.state.value,
            "process_id": session.process_id,
            "runtime_seconds": runtime,
            "completed_frames": session.completed_frames,
            "total_frames": session.total_frames,
            "current_step": session.current_step,
            "error_message": session.error_message,
            "can_pause": session.state == ProcessingState.RUNNING,
            "can_resume": session.state == ProcessingState.PAUSED,
            "can_continue": session.state in [ProcessingState.INTERRUPTED, ProcessingState.FAILED],
            "can_stop": session.state in [ProcessingState.RUNNING, ProcessingState.PAUSED]
        }

    def get_all_sessions(self) -> List[Dict]:
        """Get status of all continue sessions."""
        return [self.get_session_status(session_id) for session_id in self.active_sessions.keys()]

    def add_continue_parameters(self, command: str, output_dir: str, series_name: str) -> str:
        """Add continue mode parameters to AreTomo3 command."""
        # Add continue mode parameters
        continue_params = [
            f"-OutDir {output_dir}",
            f"-LogDir {output_dir}/{series_name}_Log",
            "-Resume 1",  # Enable resume capability
            "-SaveStack 1"  # Save intermediate stacks for continue mode
        ]
        
        # Check if parameters already exist in command
        for param in continue_params:
            param_name = param.split()[0]
            if param_name not in command:
                command += f" {param}"
        
        return command

    def create_continue_command(self, session: ContinueSession) -> str:
        """Create command for continuing interrupted processing."""
        # Start with original command
        command = session.command
        
        # Add continue-specific parameters
        continue_params = [
            "-Resume 2",  # Continue from interruption
            f"-LogDir {session.output_directory}/{session.series_name}_Log"
        ]
        
        for param in continue_params:
            param_name = param.split()[0]
            # Replace existing parameter or add new one
            if param_name in command:
                # Replace existing parameter
                import re
                pattern = rf"{param_name}\s+\S+"
                command = re.sub(pattern, param, command)
            else:
                command += f" {param}"
        
        return command

    def save_session_state(self, session: ContinueSession):
        """Save session state to file."""
        try:
            state_file = self.state_dir / f"{session.session_id}.json"
            
            session_data = {
                "session_id": session.session_id,
                "input_directory": session.input_directory,
                "output_directory": session.output_directory,
                "series_name": session.series_name,
                "command": session.command,
                "state": session.state.value,
                "process_id": session.process_id,
                "start_time": session.start_time,
                "pause_time": session.pause_time,
                "resume_time": session.resume_time,
                "completed_frames": session.completed_frames,
                "total_frames": session.total_frames,
                "current_step": session.current_step,
                "error_message": session.error_message
            }
            
            with open(state_file, 'w') as f:
                json.dump(session_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving session state: {e}")

    def load_existing_sessions(self):
        """Load existing continue sessions from state files."""
        try:
            for state_file in self.state_dir.glob("continue_*.json"):
                try:
                    with open(state_file, 'r') as f:
                        session_data = json.load(f)
                    
                    session = ContinueSession(
                        session_id=session_data["session_id"],
                        input_directory=session_data["input_directory"],
                        output_directory=session_data["output_directory"],
                        series_name=session_data["series_name"],
                        command=session_data["command"],
                        state=ProcessingState(session_data["state"]),
                        process_id=session_data.get("process_id"),
                        start_time=session_data["start_time"],
                        pause_time=session_data.get("pause_time"),
                        resume_time=session_data.get("resume_time"),
                        completed_frames=session_data.get("completed_frames", 0),
                        total_frames=session_data.get("total_frames", 0),
                        current_step=session_data.get("current_step", "unknown"),
                        error_message=session_data.get("error_message")
                    )
                    
                    self.active_sessions[session.session_id] = session
                    
                    # Check if process is still running
                    if session.process_id and session.state in [ProcessingState.RUNNING, ProcessingState.PAUSED]:
                        try:
                            os.kill(session.process_id, 0)  # Check if process exists
                            logger.info(f"Recovered running session: {session.session_id}")
                        except OSError:
                            # Process no longer exists
                            session.state = ProcessingState.INTERRUPTED
                            self.save_session_state(session)
                            logger.info(f"Marked orphaned session as interrupted: {session.session_id}")
                    
                except Exception as e:
                    logger.warning(f"Could not load session from {state_file}: {e}")
            
            logger.info(f"Loaded {len(self.active_sessions)} continue sessions")
            
        except Exception as e:
            logger.error(f"Error loading existing sessions: {e}")

    def cleanup_completed_sessions(self, max_age_hours: int = 24):
        """Clean up completed sessions older than specified hours."""
        try:
            current_time = time.time()
            sessions_to_remove = []
            
            for session_id, session in self.active_sessions.items():
                if session.state in [ProcessingState.COMPLETED, ProcessingState.FAILED]:
                    age_hours = (current_time - session.start_time) / 3600
                    if age_hours > max_age_hours:
                        sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                # Remove session state file
                state_file = self.state_dir / f"{session_id}.json"
                if state_file.exists():
                    state_file.unlink()
                
                # Remove from active sessions
                del self.active_sessions[session_id]
                
                logger.info(f"Cleaned up old session: {session_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")

    # Alias methods for backward compatibility with tests
    def create_session(self, input_dir: str, output_dir: str, parameters: dict) -> str:
        """Create a new processing session (alias for start_processing)."""
        import time
        session_id = f"session_{int(time.time())}"
        series_name = parameters.get('series_name', 'unknown')
        command = parameters.get('command', 'AreTomo3')

        success, result = self.start_processing(series_name, command, input_dir, output_dir)
        return result if success else session_id

    def pause_session(self, session_id: str) -> bool:
        """Pause a session (alias for pause_processing)."""
        return self.pause_processing(session_id)

    def resume_session(self, session_id: str) -> bool:
        """Resume a session (alias for resume_processing)."""
        return self.resume_processing(session_id)

    def update_session_progress(self, session_id: str, processed_file: str, success: bool = True) -> None:
        """Update session progress."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            if success:
                session.completed_frames += 1
            else:
                session.error_message = f"Failed to process: {processed_file}"
            self.save_session_state(session)
