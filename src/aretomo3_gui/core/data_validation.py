#!/usr/bin/env python3
"""
AreTomo3 GUI Data Validation Framework
Comprehensive validation for all data types and formats.
"""

import logging
import re
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import mrcfile
from datetime import datetime

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Validation issue severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of a validation check."""
    is_valid: bool
    severity: ValidationSeverity
    message: str
    field: Optional[str] = None
    value: Any = None
    suggestion: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationReport:
    """Comprehensive validation report."""
    is_valid: bool
    results: List[ValidationResult]
    summary: Dict[str, int]
    timestamp: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Calculate summary after initialization."""
        self.summary = {
            "total": len(self.results),
            "info": sum(1 for r in self.results if r.severity == ValidationSeverity.INFO),
            "warning": sum(1 for r in self.results if r.severity == ValidationSeverity.WARNING),
            "error": sum(1 for r in self.results if r.severity == ValidationSeverity.ERROR),
            "critical": sum(1 for r in self.results if r.severity == ValidationSeverity.CRITICAL)
        }
        
        # Overall validity based on errors and critical issues
        self.is_valid = self.summary["error"] == 0 and self.summary["critical"] == 0


class DataValidator:
    """
    Comprehensive data validation framework for AreTomo3 GUI.
    """
    
    def __init__(self):
        """Initialize the data validator."""
        self.validators: Dict[str, Callable] = {}
        self.file_validators: Dict[str, Callable] = {}
        self.custom_rules: Dict[str, Callable] = {}
        
        # Register built-in validators
        self._register_builtin_validators()
        self._register_file_validators()
        
        logger.info("Data Validation Framework initialized")
    
    def _register_builtin_validators(self):
        """Register built-in validation functions."""
        self.validators.update({
            "required": self._validate_required,
            "numeric": self._validate_numeric,
            "positive": self._validate_positive,
            "range": self._validate_range,
            "email": self._validate_email,
            "path": self._validate_path,
            "file_exists": self._validate_file_exists,
            "directory_exists": self._validate_directory_exists,
            "extension": self._validate_extension,
            "regex": self._validate_regex,
            "length": self._validate_length,
            "array_shape": self._validate_array_shape,
            "dataframe": self._validate_dataframe,
            "tilt_angles": self._validate_tilt_angles,
            "pixel_size": self._validate_pixel_size,
            "voltage": self._validate_voltage,
            "defocus": self._validate_defocus
        })
    
    def _register_file_validators(self):
        """Register file format validators."""
        self.file_validators.update({
            ".mrc": self._validate_mrc_file,
            ".mrcs": self._validate_mrc_file,
            ".tif": self._validate_tiff_file,
            ".tiff": self._validate_tiff_file,
            ".eer": self._validate_eer_file,
            ".mdoc": self._validate_mdoc_file,
            ".txt": self._validate_text_file,
            ".csv": self._validate_csv_file,
            ".json": self._validate_json_file,
            ".aln": self._validate_alignment_file
        })
    
    def validate_data(self, data: Any, rules: Dict[str, Any], 
                     context: str = "data") -> ValidationReport:
        """
        Validate data against a set of rules.
        
        Args:
            data: Data to validate
            rules: Validation rules dictionary
            context: Context description for validation
            
        Returns:
            ValidationReport: Comprehensive validation report
        """
        results = []
        
        try:
            # Validate each field according to rules
            for field_name, field_rules in rules.items():
                field_value = self._get_field_value(data, field_name)
                field_results = self._validate_field(field_value, field_rules, field_name)
                results.extend(field_results)
            
            # Apply custom validation rules if any
            if hasattr(data, '__dict__'):
                custom_results = self._apply_custom_rules(data, context)
                results.extend(custom_results)
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.CRITICAL,
                message=f"Validation error: {str(e)}",
                context={"validation_context": context}
            ))
        
        return ValidationReport(is_valid=True, results=results)
    
    def validate_file(self, file_path: Union[str, Path]) -> ValidationReport:
        """
        Validate a file based on its extension and content.
        
        Args:
            file_path: Path to file to validate
            
        Returns:
            ValidationReport: File validation report
        """
        results = []
        file_path = Path(file_path)
        
        try:
            # Check if file exists
            if not file_path.exists():
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"File does not exist: {file_path}",
                    field="file_path",
                    value=str(file_path)
                ))
                return ValidationReport(is_valid=False, results=results)
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size == 0:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"File is empty: {file_path}",
                    field="file_size",
                    value=file_size
                ))
            
            # Validate based on file extension
            extension = file_path.suffix.lower()
            if extension in self.file_validators:
                validator = self.file_validators[extension]
                file_results = validator(file_path)
                results.extend(file_results)
            else:
                results.append(ValidationResult(
                    is_valid=True,
                    severity=ValidationSeverity.INFO,
                    message=f"No specific validator for {extension} files",
                    field="file_type",
                    value=extension
                ))
            
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {e}")
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"File validation error: {str(e)}",
                field="file_validation",
                value=str(file_path)
            ))
        
        return ValidationReport(is_valid=True, results=results)
    
    def _get_field_value(self, data: Any, field_name: str) -> Any:
        """Get field value from data object."""
        if isinstance(data, dict):
            return data.get(field_name)
        elif hasattr(data, field_name):
            return getattr(data, field_name)
        elif isinstance(data, (list, tuple)) and field_name.isdigit():
            index = int(field_name)
            return data[index] if 0 <= index < len(data) else None
        else:
            return None
    
    def _validate_field(self, value: Any, rules: Union[str, List[str], Dict[str, Any]], 
                       field_name: str) -> List[ValidationResult]:
        """Validate a single field against its rules."""
        results = []
        
        # Normalize rules to list format
        if isinstance(rules, str):
            rules = [rules]
        elif isinstance(rules, dict):
            # Handle complex rule definitions
            rule_list = []
            for rule_name, rule_params in rules.items():
                if isinstance(rule_params, bool) and rule_params:
                    rule_list.append(rule_name)
                elif rule_params is not None:
                    rule_list.append((rule_name, rule_params))
            rules = rule_list
        
        # Apply each validation rule
        for rule in rules:
            if isinstance(rule, tuple):
                rule_name, rule_params = rule
            else:
                rule_name = rule
                rule_params = None
            
            if rule_name in self.validators:
                validator = self.validators[rule_name]
                try:
                    result = validator(value, rule_params, field_name)
                    if result:
                        results.append(result)
                except Exception as e:
                    results.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Validation rule '{rule_name}' failed: {str(e)}",
                        field=field_name,
                        value=value
                    ))
        
        return results
    
    def _apply_custom_rules(self, data: Any, context: str) -> List[ValidationResult]:
        """Apply custom validation rules."""
        results = []
        
        for rule_name, rule_func in self.custom_rules.items():
            try:
                result = rule_func(data, context)
                if result:
                    results.append(result)
            except Exception as e:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Custom rule '{rule_name}' failed: {str(e)}",
                    context={"rule": rule_name, "context": context}
                ))
        
        return results
    
    # Built-in validator implementations
    def _validate_required(self, value: Any, params: Any, field_name: str) -> Optional[ValidationResult]:
        """Validate that a field is not None or empty."""
        if value is None or (isinstance(value, (str, list, dict)) and len(value) == 0):
            return ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Field '{field_name}' is required",
                field=field_name,
                value=value,
                suggestion="Provide a valid value for this field"
            )
        return None
    
    def _validate_numeric(self, value: Any, params: Any, field_name: str) -> Optional[ValidationResult]:
        """Validate that a value is numeric."""
        if value is not None and not isinstance(value, (int, float, np.number)):
            try:
                float(value)
            except (ValueError, TypeError):
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Field '{field_name}' must be numeric",
                    field=field_name,
                    value=value,
                    suggestion="Provide a numeric value"
                )
        return None
    
    def _validate_positive(self, value: Any, params: Any, field_name: str) -> Optional[ValidationResult]:
        """Validate that a numeric value is positive."""
        if value is not None:
            try:
                num_value = float(value)
                if num_value <= 0:
                    return ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Field '{field_name}' must be positive",
                        field=field_name,
                        value=value,
                        suggestion="Provide a positive value"
                    )
            except (ValueError, TypeError):
                pass  # Let numeric validator handle this
        return None
    
    def _validate_range(self, value: Any, params: Tuple[float, float], field_name: str) -> Optional[ValidationResult]:
        """Validate that a value is within a specified range."""
        if value is not None and params:
            try:
                num_value = float(value)
                min_val, max_val = params
                if not (min_val <= num_value <= max_val):
                    return ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Field '{field_name}' must be between {min_val} and {max_val}",
                        field=field_name,
                        value=value,
                        suggestion=f"Provide a value between {min_val} and {max_val}"
                    )
            except (ValueError, TypeError):
                pass  # Let numeric validator handle this
        return None
    
    def _validate_path(self, value: Any, params: Any, field_name: str) -> Optional[ValidationResult]:
        """Validate that a value is a valid path."""
        if value is not None:
            try:
                Path(value)
            except (ValueError, TypeError):
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Field '{field_name}' is not a valid path",
                    field=field_name,
                    value=value,
                    suggestion="Provide a valid file or directory path"
                )
        return None
    
    def _validate_file_exists(self, value: Any, params: Any, field_name: str) -> Optional[ValidationResult]:
        """Validate that a file exists."""
        if value is not None:
            file_path = Path(value)
            if not file_path.exists() or not file_path.is_file():
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"File does not exist: {value}",
                    field=field_name,
                    value=value,
                    suggestion="Provide a path to an existing file"
                )
        return None
    
    def _validate_tilt_angles(self, value: Any, params: Any, field_name: str) -> Optional[ValidationResult]:
        """Validate tilt angles for tomography."""
        if value is not None:
            try:
                if isinstance(value, (list, np.ndarray)):
                    angles = np.array(value)
                    if np.any(np.abs(angles) > 90):
                        return ValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.WARNING,
                            message=f"Tilt angles exceed ±90°",
                            field=field_name,
                            value=value,
                            suggestion="Check if tilt angles are in the expected range"
                        )
            except Exception:
                return ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Invalid tilt angle data format",
                    field=field_name,
                    value=value
                )
        return None
    
    # File format validators
    def _validate_mrc_file(self, file_path: Path) -> List[ValidationResult]:
        """Validate MRC file format."""
        results = []
        try:
            with mrcfile.open(file_path, mode='r') as mrc:
                # Check header validity
                if not mrc.header.is_valid():
                    results.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message="Invalid MRC header",
                        field="mrc_header"
                    ))
                
                # Check data shape
                if mrc.data is None:
                    results.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message="MRC file contains no data",
                        field="mrc_data"
                    ))
                else:
                    results.append(ValidationResult(
                        is_valid=True,
                        severity=ValidationSeverity.INFO,
                        message=f"MRC file shape: {mrc.data.shape}",
                        field="mrc_shape",
                        value=mrc.data.shape
                    ))
                    
        except Exception as e:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Error reading MRC file: {str(e)}",
                field="mrc_file"
            ))
        
        return results
    
    def _validate_mdoc_file(self, file_path: Path) -> List[ValidationResult]:
        """Validate MDOC file format."""
        results = []
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                
                # Check for required MDOC sections
                if '[ZValue' not in content:
                    results.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.WARNING,
                        message="MDOC file missing ZValue sections",
                        field="mdoc_structure"
                    ))
                
                # Count tilt images
                tilt_count = content.count('[ZValue')
                results.append(ValidationResult(
                    is_valid=True,
                    severity=ValidationSeverity.INFO,
                    message=f"MDOC contains {tilt_count} tilt images",
                    field="mdoc_tilt_count",
                    value=tilt_count
                ))
                
        except Exception as e:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Error reading MDOC file: {str(e)}",
                field="mdoc_file"
            ))
        
        return results
    
    def register_custom_rule(self, name: str, validator_func: Callable):
        """Register a custom validation rule."""
        self.custom_rules[name] = validator_func
        logger.info(f"Registered custom validation rule: {name}")


# Global data validator instance
data_validator = DataValidator()
