#!/usr/bin/env python3
"""
AreTomo3 GUI Database Integration
SQLite-based database for storing processing results, metadata, and analysis data.
"""

import logging
import sqlite3
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
import contextlib

logger = logging.getLogger(__name__)


@dataclass
class ProcessingRecord:
    """Processing record for database storage."""
    id: Optional[int] = None
    session_id: str = ""
    input_path: str = ""
    output_path: str = ""
    parameters: Dict[str, Any] = None
    status: str = "pending"  # pending, running, completed, failed
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    results_metadata: Dict[str, Any] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.results_metadata is None:
            self.results_metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class AnalysisRecord:
    """Analysis record for database storage."""
    id: Optional[int] = None
    processing_id: int = 0
    analysis_type: str = ""
    parameters: Dict[str, Any] = None
    results: Dict[str, Any] = None
    quality_metrics: Dict[str, float] = None
    file_paths: List[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.results is None:
            self.results = {}
        if self.quality_metrics is None:
            self.quality_metrics = {}
        if self.file_paths is None:
            self.file_paths = []
        if self.created_at is None:
            self.created_at = datetime.now()


class DatabaseManager:
    """
    Database manager for AreTomo3 GUI.
    Handles SQLite database operations for processing and analysis data.
    """
    
    def __init__(self, db_path: Union[str, Path] = None):
        """Initialize the database manager."""
        if db_path is None:
            db_path = Path.home() / ".aretomo3_gui" / "aretomo3.db"
        
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Initialize database
        self._initialize_database()
        
        logger.info(f"Database Manager initialized - DB: {self.db_path}")
    
    def _initialize_database(self):
        """Initialize database tables."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Create processing records table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS processing_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    input_path TEXT NOT NULL,
                    output_path TEXT NOT NULL,
                    parameters TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'pending',
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    error_message TEXT,
                    results_metadata TEXT,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create analysis records table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS analysis_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    processing_id INTEGER NOT NULL,
                    analysis_type TEXT NOT NULL,
                    parameters TEXT NOT NULL,
                    results TEXT NOT NULL,
                    quality_metrics TEXT NOT NULL,
                    file_paths TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (processing_id) REFERENCES processing_records (id)
                )
            """)
            
            # Create sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    config TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_accessed TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create quality metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS quality_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    processing_id INTEGER NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_unit TEXT,
                    tilt_angle REAL,
                    frame_number INTEGER,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (processing_id) REFERENCES processing_records (id)
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_processing_session ON processing_records(session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_processing_status ON processing_records(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_analysis_processing ON analysis_records(processing_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_analysis_type ON analysis_records(analysis_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_quality_processing ON quality_metrics(processing_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_quality_metric ON quality_metrics(metric_name)")
            
            conn.commit()
            logger.info("Database tables initialized successfully")
    
    @contextlib.contextmanager
    def _get_connection(self):
        """Get database connection with proper cleanup."""
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def add_processing_record(self, record: ProcessingRecord) -> int:
        """Add a processing record to the database."""
        with self._lock:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO processing_records (
                        session_id, input_path, output_path, parameters,
                        status, start_time, end_time, error_message,
                        results_metadata, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.session_id,
                    record.input_path,
                    record.output_path,
                    json.dumps(record.parameters),
                    record.status,
                    record.start_time,
                    record.end_time,
                    record.error_message,
                    json.dumps(record.results_metadata),
                    record.created_at,
                    record.updated_at
                ))
                
                record_id = cursor.lastrowid
                conn.commit()
                
                logger.debug(f"Added processing record: {record_id}")
                return record_id
    
    def update_processing_record(self, record_id: int, updates: Dict[str, Any]) -> bool:
        """Update a processing record."""
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Build update query dynamically
                    set_clauses = []
                    values = []
                    
                    for key, value in updates.items():
                        if key in ['parameters', 'results_metadata'] and isinstance(value, dict):
                            value = json.dumps(value)
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                    
                    # Always update the updated_at timestamp
                    set_clauses.append("updated_at = ?")
                    values.append(datetime.now())
                    values.append(record_id)
                    
                    query = f"UPDATE processing_records SET {', '.join(set_clauses)} WHERE id = ?"
                    cursor.execute(query, values)
                    
                    conn.commit()
                    logger.debug(f"Updated processing record: {record_id}")
                    return True
                    
            except Exception as e:
                logger.error(f"Error updating processing record {record_id}: {e}")
                return False
    
    def get_processing_record(self, record_id: int) -> Optional[ProcessingRecord]:
        """Get a processing record by ID."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM processing_records WHERE id = ?", (record_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_processing_record(row)
            return None
    
    def get_processing_records(self, session_id: str = None, status: str = None, 
                             limit: int = None) -> List[ProcessingRecord]:
        """Get processing records with optional filters."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM processing_records WHERE 1=1"
            params = []
            
            if session_id:
                query += " AND session_id = ?"
                params.append(session_id)
            
            if status:
                query += " AND status = ?"
                params.append(status)
            
            query += " ORDER BY created_at DESC"
            
            if limit:
                query += " LIMIT ?"
                params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            return [self._row_to_processing_record(row) for row in rows]
    
    def add_analysis_record(self, record: AnalysisRecord) -> int:
        """Add an analysis record to the database."""
        with self._lock:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO analysis_records (
                        processing_id, analysis_type, parameters, results,
                        quality_metrics, file_paths, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.processing_id,
                    record.analysis_type,
                    json.dumps(record.parameters),
                    json.dumps(record.results),
                    json.dumps(record.quality_metrics),
                    json.dumps(record.file_paths),
                    record.created_at
                ))
                
                record_id = cursor.lastrowid
                conn.commit()
                
                logger.debug(f"Added analysis record: {record_id}")
                return record_id
    
    def get_analysis_records(self, processing_id: int = None, 
                           analysis_type: str = None) -> List[AnalysisRecord]:
        """Get analysis records with optional filters."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM analysis_records WHERE 1=1"
            params = []
            
            if processing_id:
                query += " AND processing_id = ?"
                params.append(processing_id)
            
            if analysis_type:
                query += " AND analysis_type = ?"
                params.append(analysis_type)
            
            query += " ORDER BY created_at DESC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            return [self._row_to_analysis_record(row) for row in rows]
    
    def add_quality_metrics(self, processing_id: int, metrics: Dict[str, float], 
                          tilt_angle: float = None, frame_number: int = None):
        """Add quality metrics for a processing record."""
        with self._lock:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                for metric_name, metric_value in metrics.items():
                    cursor.execute("""
                        INSERT INTO quality_metrics (
                            processing_id, metric_name, metric_value,
                            tilt_angle, frame_number, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        processing_id, metric_name, float(metric_value),
                        tilt_angle, frame_number, datetime.now()
                    ))
                
                conn.commit()
                logger.debug(f"Added {len(metrics)} quality metrics for processing {processing_id}")
    
    def get_quality_metrics(self, processing_id: int, metric_name: str = None) -> List[Dict[str, Any]]:
        """Get quality metrics for a processing record."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM quality_metrics WHERE processing_id = ?"
            params = [processing_id]
            
            if metric_name:
                query += " AND metric_name = ?"
                params.append(metric_name)
            
            query += " ORDER BY created_at"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            return [dict(row) for row in rows]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # Processing records statistics
            cursor.execute("SELECT COUNT(*) FROM processing_records")
            stats['total_processing_records'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT status, COUNT(*) FROM processing_records GROUP BY status")
            stats['processing_by_status'] = dict(cursor.fetchall())
            
            # Analysis records statistics
            cursor.execute("SELECT COUNT(*) FROM analysis_records")
            stats['total_analysis_records'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT analysis_type, COUNT(*) FROM analysis_records GROUP BY analysis_type")
            stats['analysis_by_type'] = dict(cursor.fetchall())
            
            # Quality metrics statistics
            cursor.execute("SELECT COUNT(*) FROM quality_metrics")
            stats['total_quality_metrics'] = cursor.fetchone()[0]
            
            return stats
    
    def _row_to_processing_record(self, row) -> ProcessingRecord:
        """Convert database row to ProcessingRecord."""
        return ProcessingRecord(
            id=row['id'],
            session_id=row['session_id'],
            input_path=row['input_path'],
            output_path=row['output_path'],
            parameters=json.loads(row['parameters']) if row['parameters'] else {},
            status=row['status'],
            start_time=datetime.fromisoformat(row['start_time']) if row['start_time'] else None,
            end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
            error_message=row['error_message'],
            results_metadata=json.loads(row['results_metadata']) if row['results_metadata'] else {},
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at'])
        )
    
    def _row_to_analysis_record(self, row) -> AnalysisRecord:
        """Convert database row to AnalysisRecord."""
        return AnalysisRecord(
            id=row['id'],
            processing_id=row['processing_id'],
            analysis_type=row['analysis_type'],
            parameters=json.loads(row['parameters']) if row['parameters'] else {},
            results=json.loads(row['results']) if row['results'] else {},
            quality_metrics=json.loads(row['quality_metrics']) if row['quality_metrics'] else {},
            file_paths=json.loads(row['file_paths']) if row['file_paths'] else [],
            created_at=datetime.fromisoformat(row['created_at'])
        )
    
    def cleanup_old_records(self, days_old: int = 30):
        """Clean up old records from the database."""
        with self._lock:
            try:
                cutoff_date = datetime.now() - timedelta(days=days_old)
                
                with self._get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Delete old quality metrics
                    cursor.execute("DELETE FROM quality_metrics WHERE created_at < ?", (cutoff_date,))
                    deleted_metrics = cursor.rowcount
                    
                    # Delete old analysis records
                    cursor.execute("DELETE FROM analysis_records WHERE created_at < ?", (cutoff_date,))
                    deleted_analysis = cursor.rowcount
                    
                    # Delete old processing records (only completed/failed)
                    cursor.execute("""
                        DELETE FROM processing_records 
                        WHERE created_at < ? AND status IN ('completed', 'failed')
                    """, (cutoff_date,))
                    deleted_processing = cursor.rowcount
                    
                    conn.commit()
                    
                    logger.info(f"Cleaned up old records: {deleted_processing} processing, "
                              f"{deleted_analysis} analysis, {deleted_metrics} metrics")
                    
            except Exception as e:
                logger.error(f"Error cleaning up old records: {e}")
    
    def vacuum_database(self):
        """Vacuum the database to reclaim space."""
        try:
            with self._get_connection() as conn:
                conn.execute("VACUUM")
                logger.info("Database vacuumed successfully")
        except Exception as e:
            logger.error(f"Error vacuuming database: {e}")


# Global database manager instance
database_manager = DatabaseManager()
