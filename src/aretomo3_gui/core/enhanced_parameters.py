#!/usr/bin/env python3
"""
Enhanced parameter management for AreTomo3 GUI.
Provides comprehensive coverage of all AreTomo3 parameters with intelligent validation.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union, Tuple
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ParameterCategory(Enum):
    """Categories for organizing parameters."""
    BASIC = "basic"
    MOTION_CORRECTION = "motion_correction"
    RECONSTRUCTION = "reconstruction"
    CTF_CORRECTION = "ctf_correction"
    ADVANCED = "advanced"
    OUTPUT = "output"


class ParameterType(Enum):
    """Types of parameters for validation."""
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    STRING = "string"
    FILE_PATH = "file_path"
    DIRECTORY = "directory"
    CHOICE = "choice"
    RANGE = "range"


@dataclass
class ParameterDefinition:
    """Definition of a single parameter."""
    name: str
    display_name: str
    description: str
    category: ParameterCategory
    param_type: ParameterType
    default_value: Any
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    choices: Optional[List[str]] = None
    dependencies: List[str] = field(default_factory=list)
    conflicts: List[str] = field(default_factory=list)
    command_flag: Optional[str] = None
    tooltip: Optional[str] = None
    required: bool = False


class EnhancedParameterManager:
    """Enhanced parameter manager with complete AreTomo3 coverage."""

    def __init__(self):
        """Initialize the parameter manager."""
        self.parameters: Dict[str, ParameterDefinition] = {}
        self.current_values: Dict[str, Any] = {}
        self.parameter_groups: Dict[ParameterCategory, List[str]] = {}
        self._initialize_parameters()

    def _initialize_parameters(self):
        """Initialize all AreTomo3 parameters."""

        # Basic Input/Output Parameters
        self._add_parameter(ParameterDefinition(
            name="input_prefix",
            display_name="Input Prefix",
            description="Prefix of input file names or path to folder",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.STRING,
            default_value="",
            command_flag="-InPrefix",
            tooltip="Path to input files or folder containing tilt series",
            required=True
        ))

        self._add_parameter(ParameterDefinition(
            name="input_suffix",
            display_name="Input Suffix",
            description="File extension for input files",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.CHOICE,
            default_value="mdoc",
            choices=["mdoc", "mrc", "eer", "tif"],
            command_flag="-InSuffix",
            tooltip="File extension to look for in input directory"
        ))

        self._add_parameter(ParameterDefinition(
            name="output_dir",
            display_name="Output Directory",
            description="Directory for output files",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.DIRECTORY,
            default_value="",
            command_flag="-OutDir",
            tooltip="Directory where processed files will be saved",
            required=True
        ))

        self._add_parameter(ParameterDefinition(
            name="pixel_size",
            display_name="Pixel Size (Å)",
            description="Pixel size in Angstroms",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.1,
            max_value=10.0,
            command_flag="-PixSize",
            tooltip="Physical pixel size of the detector in Angstroms",
            required=True
        ))

        self._add_parameter(ParameterDefinition(
            name="voltage",
            display_name="Voltage (kV)",
            description="Acceleration voltage in kV",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.INTEGER,
            default_value=300,
            min_value=60,
            max_value=300,
            command_flag="-kV",
            tooltip="Electron acceleration voltage"
        ))

        self._add_parameter(ParameterDefinition(
            name="cs",
            display_name="Spherical Aberration (mm)",
            description="Spherical aberration in mm",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.FLOAT,
            default_value=2.7,
            min_value=0.0,
            max_value=10.0,
            command_flag="-Cs",
            tooltip="Spherical aberration coefficient"
        ))

        self._add_parameter(ParameterDefinition(
            name="frame_dose",
            display_name="Frame Dose (e⁻/Å²)",
            description="Dose per frame in electrons per square Angstrom",
            category=ParameterCategory.BASIC,
            param_type=ParameterType.FLOAT,
            default_value=0.0,
            min_value=0.0,
            max_value=10.0,
            command_flag="-FmDose",
            tooltip="Electron dose per frame for dose weighting"
        ))

        # Motion Correction Parameters
        self._add_parameter(ParameterDefinition(
            name="gain_file",
            display_name="Gain Reference File",
            description="Path to gain reference file",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.FILE_PATH,
            default_value="",
            command_flag="-Gain",
            tooltip="Gain reference file for motion correction"
        ))

        self._add_parameter(ParameterDefinition(
            name="dark_file",
            display_name="Dark Reference File",
            description="Path to dark reference file",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.FILE_PATH,
            default_value="",
            command_flag="-Dark",
            tooltip="Dark reference file for motion correction"
        ))

        self._add_parameter(ParameterDefinition(
            name="mc_patch",
            display_name="Motion Correction Patches",
            description="Number of patches for motion correction [X, Y]",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.RANGE,
            default_value=[0, 0],
            min_value=0,
            max_value=20,
            command_flag="-McPatch",
            tooltip="Patch-based motion correction (0 0 = full frame)"
        ))

        self._add_parameter(ParameterDefinition(
            name="mc_iter",
            display_name="MC Iterations",
            description="Maximum iterations for motion correction",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.INTEGER,
            default_value=15,
            min_value=1,
            max_value=100,
            command_flag="-McIter",
            tooltip="Maximum number of iterations for motion correction"
        ))

        self._add_parameter(ParameterDefinition(
            name="mc_tol",
            display_name="MC Tolerance",
            description="Tolerance for motion correction convergence",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.FLOAT,
            default_value=0.1,
            min_value=0.01,
            max_value=1.0,
            command_flag="-McTol",
            tooltip="Convergence tolerance for motion correction"
        ))

        self._add_parameter(ParameterDefinition(
            name="mc_bin",
            display_name="MC Binning",
            description="Binning factor for motion correction",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            min_value=0.5,
            max_value=8.0,
            command_flag="-McBin",
            tooltip="Binning factor applied during motion correction"
        ))

        self._add_parameter(ParameterDefinition(
            name="fm_int",
            display_name="Frame Integration",
            description="Frame integration interval",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.INTEGER,
            default_value=10,
            min_value=1,
            max_value=50,
            command_flag="-FmInt",
            tooltip="Number of frames to integrate for alignment"
        ))

        self._add_parameter(ParameterDefinition(
            name="rot_gain",
            display_name="Rotate Gain",
            description="Rotation angle for gain reference",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.CHOICE,
            default_value="0",
            choices=["0", "1", "2", "3"],
            command_flag="-RotGain",
            tooltip="Rotate gain reference: 0=0°, 1=90°, 2=180°, 3=270°"
        ))

        self._add_parameter(ParameterDefinition(
            name="flip_gain",
            display_name="Flip Gain",
            description="Flip gain reference",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.CHOICE,
            default_value="0",
            choices=["0", "1", "2"],
            command_flag="-FlipGain",
            tooltip="Flip gain: 0=none, 1=upside down, 2=left right"
        ))

        self._add_parameter(ParameterDefinition(
            name="inv_gain",
            display_name="Invert Gain",
            description="Invert gain reference values",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.BOOLEAN,
            default_value=False,
            command_flag="-InvGain",
            tooltip="Invert gain reference values (1/gain)"
        ))

        self._add_parameter(ParameterDefinition(
            name="eer_sampling",
            display_name="EER Sampling",
            description="EER upsampling factor",
            category=ParameterCategory.MOTION_CORRECTION,
            param_type=ParameterType.CHOICE,
            default_value="1",
            choices=["1", "2", "3"],
            command_flag="-EerSampling",
            tooltip="EER upsampling: 1=1x, 2=2x, 3=4x"
        ))

        # Reconstruction Parameters
        self._add_parameter(ParameterDefinition(
            name="tilt_axis",
            display_name="Tilt Axis Angle",
            description="Tilt axis angle in degrees",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.FLOAT,
            default_value=0.0,
            min_value=-180.0,
            max_value=180.0,
            command_flag="-TiltAxis",
            tooltip="Tilt axis angle (0 = auto search)"
        ))

        self._add_parameter(ParameterDefinition(
            name="align_z",
            display_name="Alignment Z Height",
            description="Volume height for alignment",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.INTEGER,
            default_value=256,
            min_value=64,
            max_value=2048,
            command_flag="-AlignZ",
            tooltip="Z height of volume used for alignment"
        ))

        self._add_parameter(ParameterDefinition(
            name="vol_z",
            display_name="Volume Z Height",
            description="Final volume Z height",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.INTEGER,
            default_value=-1,
            min_value=-1,
            max_value=4096,
            command_flag="-VolZ",
            tooltip="Final reconstruction Z height (-1 = auto)"
        ))

        self._add_parameter(ParameterDefinition(
            name="ext_z",
            display_name="Extended Z Range",
            description="Extra Z range for reconstruction",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.INTEGER,
            default_value=300,
            min_value=100,
            max_value=1000,
            command_flag="-ExtZ",
            tooltip="Extra Z space added to estimated thickness"
        ))

        self._add_parameter(ParameterDefinition(
            name="at_bin",
            display_name="Alignment Binning",
            description="Multi-resolution binning factors [1st, 2nd, 3rd]",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.RANGE,
            default_value=[1.0, 0.0, 0.0],
            min_value=0.0,
            max_value=8.0,
            command_flag="-AtBin",
            tooltip="Binning for multi-resolution alignment (0 = disabled)"
        ))

        self._add_parameter(ParameterDefinition(
            name="tilt_cor",
            display_name="Tilt Correction",
            description="Tilt angle offset correction",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.RANGE,
            default_value=[0.0, 0.0],
            min_value=-10.0,
            max_value=10.0,
            command_flag="-TiltCor",
            tooltip="Tilt offset correction [mode, offset]"
        ))

        self._add_parameter(ParameterDefinition(
            name="recon_range",
            display_name="Reconstruction Range",
            description="Tilt angle range for reconstruction",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.RANGE,
            default_value=[-90.0, 90.0],
            min_value=-90.0,
            max_value=90.0,
            command_flag="-ReconRange",
            tooltip="Min and max tilt angles for reconstruction"
        ))

        self._add_parameter(ParameterDefinition(
            name="at_patch",
            display_name="Alignment Patches",
            description="Patch-based alignment parameters",
            category=ParameterCategory.RECONSTRUCTION,
            param_type=ParameterType.RANGE,
            default_value=[0, 0],
            min_value=0,
            max_value=20,
            command_flag="-AtPatch",
            tooltip="Patch-based alignment [X, Y] (0 0 = disabled)"
        ))

        # CTF Correction Parameters
        self._add_parameter(ParameterDefinition(
            name="amp_contrast",
            display_name="Amplitude Contrast",
            description="Amplitude contrast for CTF correction",
            category=ParameterCategory.CTF_CORRECTION,
            param_type=ParameterType.FLOAT,
            default_value=0.07,
            min_value=0.0,
            max_value=1.0,
            command_flag="-AmpContrast",
            tooltip="Amplitude contrast for CTF estimation"
        ))

        self._add_parameter(ParameterDefinition(
            name="corr_ctf",
            display_name="CTF Correction",
            description="CTF correction parameters [enable, lowpass]",
            category=ParameterCategory.CTF_CORRECTION,
            param_type=ParameterType.RANGE,
            default_value=[1, 15],
            min_value=0,
            max_value=100,
            command_flag="-CorrCTF",
            tooltip="CTF correction [1=enable, lowpass filter]"
        ))

        self._add_parameter(ParameterDefinition(
            name="ext_phase",
            display_name="Extended Phase",
            description="Phase plate correction parameters",
            category=ParameterCategory.CTF_CORRECTION,
            param_type=ParameterType.RANGE,
            default_value=[0.0, 0.0],
            min_value=-180.0,
            max_value=180.0,
            command_flag="-ExtPhase",
            tooltip="Phase plate correction [phase, range]"
        ))

        # Advanced Parameters
        self._add_parameter(ParameterDefinition(
            name="sart",
            display_name="SART Parameters",
            description="SART reconstruction parameters [iterations, relaxation]",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.RANGE,
            default_value=[20, 5],
            min_value=1,
            max_value=100,
            command_flag="-Sart",
            tooltip="SART algorithm [iterations, relaxation factor]"
        ))

        self._add_parameter(ParameterDefinition(
            name="wbp",
            display_name="Weighted Back Projection",
            description="Enable weighted back projection",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.BOOLEAN,
            default_value=False,
            command_flag="-Wbp",
            tooltip="Use weighted back projection algorithm"
        ))

        self._add_parameter(ParameterDefinition(
            name="flip_vol",
            display_name="Flip Volume",
            description="Flip volume orientation",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.BOOLEAN,
            default_value=False,
            command_flag="-FlipVol",
            tooltip="Flip volume to xyz orientation (default xzy)"
        ))

        self._add_parameter(ParameterDefinition(
            name="flip_int",
            display_name="Flip Intensity",
            description="Flip intensity values",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.BOOLEAN,
            default_value=False,
            command_flag="-FlipInt",
            tooltip="Flip intensity to make structures white"
        ))

        self._add_parameter(ParameterDefinition(
            name="dark_tol",
            display_name="Dark Tolerance",
            description="Tolerance for removing dark images",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.FLOAT,
            default_value=0.7,
            min_value=0.0,
            max_value=1.0,
            command_flag="-DarkTol",
            tooltip="Dark image removal tolerance (higher = more restrictive)"
        ))

        self._add_parameter(ParameterDefinition(
            name="align",
            display_name="Enable Alignment",
            description="Enable tilt series alignment",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.BOOLEAN,
            default_value=True,
            command_flag="-Align",
            tooltip="Enable/disable tilt series alignment"
        ))

        self._add_parameter(ParameterDefinition(
            name="crop_vol",
            display_name="Crop Volume",
            description="Volume cropping parameters [X, Y]",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.RANGE,
            default_value=[0, 0],
            min_value=0,
            max_value=4096,
            command_flag="-CropVol",
            tooltip="Crop volume dimensions [X, Y] (0 0 = no cropping)"
        ))

        # Output Parameters
        self._add_parameter(ParameterDefinition(
            name="out_xf",
            display_name="Output Transform File",
            description="Generate IMOD-compatible transform file",
            category=ParameterCategory.OUTPUT,
            param_type=ParameterType.BOOLEAN,
            default_value=False,
            command_flag="-OutXF",
            tooltip="Generate IMOD XF transform file"
        ))

        self._add_parameter(ParameterDefinition(
            name="out_imod",
            display_name="IMOD Output Mode",
            description="IMOD compatibility mode",
            category=ParameterCategory.OUTPUT,
            param_type=ParameterType.CHOICE,
            default_value="0",
            choices=["0", "1", "2", "3"],
            command_flag="-OutImod",
            tooltip="IMOD output: 0=none, 1=Relion4, 2=WARP, 3=aligned series"
        ))

        self._add_parameter(ParameterDefinition(
            name="split_sum",
            display_name="Split Sum",
            description="Generate odd/even frame sums",
            category=ParameterCategory.OUTPUT,
            param_type=ParameterType.BOOLEAN,
            default_value=True,
            command_flag="-SplitSum",
            tooltip="Generate odd and even frame sums for validation"
        ))

        self._add_parameter(ParameterDefinition(
            name="gpu_id",
            display_name="GPU ID",
            description="GPU device ID(s) to use",
            category=ParameterCategory.ADVANCED,
            param_type=ParameterType.STRING,
            default_value="0",
            command_flag="-Gpu",
            tooltip="GPU device ID (space-separated for multiple GPUs)"
        ))

        # Group parameters by category
        for param in self.parameters.values():
            if param.category not in self.parameter_groups:
                self.parameter_groups[param.category] = []
            self.parameter_groups[param.category].append(param.name)

    def _add_parameter(self, param: ParameterDefinition):
        """Add a parameter to the manager."""
        self.parameters[param.name] = param
        self.current_values[param.name] = param.default_value

    def get_parameter(self, name: str) -> Optional[ParameterDefinition]:
        """Get parameter definition by name."""
        return self.parameters.get(name)

    def get_value(self, name: str) -> Any:
        """Get current parameter value."""
        return self.current_values.get(name)

    def set_value(self, name: str, value: Any) -> bool:
        """Set parameter value with validation."""
        param = self.get_parameter(name)
        if not param:
            logger.warning(f"Unknown parameter: {name}")
            return False

        if self._validate_value(param, value):
            self.current_values[name] = value
            return True
        return False

    def _validate_value(self, param: ParameterDefinition, value: Any) -> bool:
        """Validate parameter value."""
        try:
            if param.param_type == ParameterType.INTEGER:
                value = int(value)
                if param.min_value is not None and value < param.min_value:
                    return False
                if param.max_value is not None and value > param.max_value:
                    return False

            elif param.param_type == ParameterType.FLOAT:
                value = float(value)
                if param.min_value is not None and value < param.min_value:
                    return False
                if param.max_value is not None and value > param.max_value:
                    return False

            elif param.param_type == ParameterType.CHOICE:
                if param.choices and str(value) not in param.choices:
                    return False

            elif param.param_type == ParameterType.BOOLEAN:
                value = bool(value)

            return True

        except (ValueError, TypeError):
            return False

    def get_parameters_by_category(self, category: ParameterCategory) -> List[ParameterDefinition]:
        """Get all parameters in a category."""
        return [self.parameters[name] for name in self.parameter_groups.get(category, [])]

    def build_command_args(self) -> List[str]:
        """Build command line arguments from current parameter values."""
        args = []

        for name, value in self.current_values.items():
            param = self.parameters[name]

            if not param.command_flag:
                continue

            # Skip default values for optional parameters
            if not param.required and value == param.default_value:
                continue

            # Handle different parameter types
            if param.param_type == ParameterType.BOOLEAN:
                if value:
                    args.extend([param.command_flag, "1"])
            elif param.param_type == ParameterType.RANGE:
                if isinstance(value, (list, tuple)) and len(value) >= 2:
                    args.extend([param.command_flag, str(value[0]), str(value[1])])
            else:
                args.extend([param.command_flag, str(value)])

        return args

    def validate_all_parameters(self) -> Tuple[bool, List[str]]:
        """Validate all current parameter values."""
        errors = []

        for name, value in self.current_values.items():
            param = self.parameters[name]

            # Check required parameters
            if param.required and (value is None or value == ""):
                errors.append(f"Required parameter '{param.display_name}' is missing")
                continue

            # Validate value
            if not self._validate_value(param, value):
                errors.append(f"Invalid value for '{param.display_name}': {value}")

        return len(errors) == 0, errors
