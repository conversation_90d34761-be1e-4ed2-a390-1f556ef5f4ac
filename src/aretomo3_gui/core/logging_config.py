#!/usr/bin/env python3
"""
Logging Configuration for AreTomo3 GUI Application
===============================================

This module provides a centralized logging configuration system with:
- Console and file output handlers
- Log rotation with size limits
- Module-based filtering
- Different log levels for console and file output
- Structured log formatting
- Automatic log directory management

Example usage:
    from core.logging_config import setup_logging
    
    # Basic setup with defaults
    setup_logging()
    
    # Custom setup
    setup_logging(
        log_dir='custom/logs',
        console_level=logging.DEBUG,
        file_level=logging.INFO,
        exclude_modules=['noisy_module']
    )

Author: AreTomo3 GUI Team
Date: May 2025
"""

# =============================================================================
# Imports
# =============================================================================

# Standard library imports
import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Union

# =============================================================================
# Constants
# =============================================================================

# Path Configuration
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.dirname(
    os.path.abspath(__file__)))))
DEFAULT_LOG_DIR = PROJECT_ROOT / 'logs'

# Log File Configuration
LOG_FILENAME_FORMAT = 'aretomo3_gui_{}.log'
LOG_DATE_FORMAT = '%Y%m%d'

# Log Message Formatting
CONSOLE_FORMAT = '%(levelname)s: %(message)s'
FILE_FORMAT = (
    '%(asctime)s - %(name)s - %(levelname)s - '
    '%(filename)s:%(lineno)d - %(message)s'
)

# Log Rotation Settings
MAX_LOG_SIZE = 10 * 1024 * 1024  # 10 MB per file
BACKUP_COUNT = 5                  # Keep 5 backup files

# =============================================================================
# Logging Filter
# =============================================================================

class LogFilter(logging.Filter):
    """
    Custom filter for selectively excluding log messages.
    
    This filter allows excluding specific modules from logging output,
    useful for reducing noise from verbose modules or third-party
    libraries.
    
    Attributes:
        name (str): Name of the filter
        exclude_modules (List[str]): List of module names to exclude
    """
    
    def __init__(
        self,
        name: str = '',
        exclude_modules: Optional[List[str]] = None
    ) -> None:
        """
        Initialize the log filter.
        
        Args:
            name: Name of the filter
            exclude_modules: List of module names whose logs should be filtered out
        """
        super().__init__(name)
        self.exclude_modules = exclude_modules or []
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Check if a log record should be included.
        
        Args:
            record: The log record to check
            
        Returns:
            bool: True if the record should be included, False if filtered out
        """
        return not any(
            record.name.startswith(module)
            for module in self.exclude_modules
        )

# =============================================================================
# Logging Setup
# =============================================================================

def setup_logging(
    log_dir: Optional[Union[str, Path]] = None,
    console_level: int = logging.INFO,
    file_level: int = logging.DEBUG,
    exclude_modules: Optional[List[str]] = None
) -> logging.Logger:
    """
    Configure application-wide logging with file and console output.
    
    This function sets up a comprehensive logging system with:
    - Console output for user feedback
    - File output for debugging and auditing
    - Log rotation to manage disk usage
    - Module filtering to reduce noise
    - Different log levels for console and file
    
    Args:
        log_dir: Directory for log files (defaults to PROJECT_ROOT/logs)
        console_level: Minimum level for console output
        file_level: Minimum level for file output
        exclude_modules: List of module names to exclude from logging
        
    Returns:
        logging.Logger: The configured root logger
        
    Example:
        >>> logger = setup_logging(
        ...     log_dir='logs',
        ...     console_level=logging.INFO,
        ...     file_level=logging.DEBUG,
        ...     exclude_modules=['noisy_module']
        ... )
        >>> logger.info("Logging system initialized")
    """
    # Resolve log directory
    log_dir = Path(log_dir) if log_dir else DEFAULT_LOG_DIR
    os.makedirs(log_dir, exist_ok=True)
    
    # Generate dated log filename
    date_str = datetime.now().strftime(LOG_DATE_FORMAT)
    log_path = log_dir / LOG_FILENAME_FORMAT.format(date_str)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # Capture all logs
    
    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create and configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)
    console_handler.setFormatter(logging.Formatter(CONSOLE_FORMAT))
    
    # Create and configure file handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_path,
        maxBytes=MAX_LOG_SIZE,
        backupCount=BACKUP_COUNT,
        encoding='utf-8'
    )
    file_handler.setLevel(file_level)
    file_handler.setFormatter(logging.Formatter(FILE_FORMAT))
    
    # Apply module filters if specified
    if exclude_modules:
        log_filter = LogFilter(exclude_modules=exclude_modules)
        console_handler.addFilter(log_filter)
        file_handler.addFilter(log_filter)
    
    # Add handlers to root logger
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Log startup information
    root_logger.info("="*80)
    root_logger.info("Logging system initialized")
    root_logger.info(f"Log file: {log_path}")
    root_logger.info(f"Console level: {logging.getLevelName(console_level)}")
    root_logger.info(f"File level: {logging.getLevelName(file_level)}")
    if exclude_modules:
        root_logger.info(f"Excluded modules: {', '.join(exclude_modules)}")
    root_logger.info("="*80)
    
    return root_logger

def get_logger(name: str) -> logging.Logger:
    """
    Get a named logger with consistent formatting.
    
    Args:
        name: Name for the logger, typically __name__ of the module
        
    Returns:
        logging.Logger: Configured logger instance
        
    Example:
        >>> logger = get_logger(__name__)
        >>> logger.info("Message from my module")
    """
    return logging.getLogger(name)

def log_system_info() -> None:
    """
    Log detailed system information for debugging purposes.
    
    Logs information about:
    - Platform and OS
    - Python environment
    - CPU and memory
    - Disk space
    - Environment variables
    
    This is useful for debugging environment-specific issues.
    """
    # Lazy imports to avoid startup overhead
    import platform
    import psutil
    import sys
    
    logger = get_logger("system_info")
    logger.info("="*80)
    logger.info("System Information")
    logger.info("="*80)
    
    try:
        # Platform information
        logger.info("\nPlatform Information:")
        logger.info(f"OS: {platform.system()} {platform.release()}")
        logger.info(f"Platform: {platform.platform()}")
        logger.info(f"Machine: {platform.machine()}")
        logger.info(f"Processor: {platform.processor()}")
        
        # Python environment
        logger.info("\nPython Environment:")
        logger.info(f"Python version: {platform.python_version()}")
        logger.info(f"Python implementation: {platform.python_implementation()}")
        logger.info(f"Python path: {sys.executable}")
        
        # Memory information
        mem = psutil.virtual_memory()
        logger.info("\nMemory Information:")
        logger.info(f"Total memory: {mem.total / (1024**3):.2f} GB")
        logger.info(f"Available memory: {mem.available / (1024**3):.2f} GB")
        logger.info(f"Memory usage: {mem.percent}%")
        
        # Disk information
        disk = psutil.disk_usage('/')
        logger.info("\nDisk Information:")
        logger.info(f"Total disk space: {disk.total / (1024**3):.2f} GB")
        logger.info(f"Used disk space: {disk.used / (1024**3):.2f} GB")
        logger.info(f"Free disk space: {disk.free / (1024**3):.2f} GB")
        logger.info(f"Disk usage: {disk.percent}%")
        
        # CPU information
        logger.info("\nCPU Information:")
        logger.info(f"CPU cores (physical): {psutil.cpu_count(logical=False)}")
        logger.info(f"CPU cores (logical): {psutil.cpu_count()}")
        logger.info(f"CPU frequency: {psutil.cpu_freq().current:.2f} MHz")
        
        # Environment variables
        logger.info("\nRelevant Environment Variables:")
        for var in ['PATH', 'PYTHONPATH', 'CUDA_VISIBLE_DEVICES']:
            if var in os.environ:
                logger.info(f"{var}: {os.environ[var]}")
        
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"Error collecting system information: {e}")
        logger.debug("Error details:", exc_info=True)
        logger.info(f"Disk space: {disk.total / (1024**3):.2f} GB total, "
                   f"{disk.free / (1024**3):.2f} GB free")
        
        # GPU information (if available)
        try:
            import subprocess
            gpu_info = subprocess.check_output(['nvidia-smi', '--query-gpu=name,memory.total',
                                              '--format=csv,noheader,nounits'], 
                                             text=True, timeout=2)
            logger.info(f"GPU information: {gpu_info.strip()}")
        except (subprocess.SubprocessError, FileNotFoundError):
            logger.info("No NVIDIA GPU detected or nvidia-smi not available")
    
    except Exception as e:
        logger.error(f"Error logging system information: {str(e)}")

# Initialize logging if this module is run directly
if __name__ == "__main__":
    setup_logging()
    log_system_info()
