#!/usr/bin/env python3
"""
Secure Web API Implementation
Addresses critical security vulnerabilities in the web interface.
"""

import logging
import secrets
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import jwt
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import ipaddress

logger = logging.getLogger(__name__)


@dataclass
class SecurityConfig:
    """Security configuration for the web API."""
    allowed_origins: List[str] = field(default_factory=lambda: ["http://localhost:3000"])
    allowed_hosts: List[str] = field(default_factory=lambda: ["localhost", "127.0.0.1"])
    jwt_secret_key: str = ""
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24
    rate_limit_requests: int = 100
    rate_limit_window_minutes: int = 15
    max_request_size_mb: int = 100
    enable_api_key_auth: bool = True
    require_https: bool = False  # Set to True in production


@dataclass
class RateLimitInfo:
    """Rate limiting information for a client."""
    requests: List[datetime] = field(default_factory=list)
    blocked_until: Optional[datetime] = None


class SecurityManager:
    """Manages security features for the web API."""
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.rate_limits: Dict[str, RateLimitInfo] = {}
        self.blocked_ips: Set[str] = set()
        self.api_keys: Set[str] = set()
        
        # Generate JWT secret if not provided
        if not self.config.jwt_secret_key:
            self.config.jwt_secret_key = secrets.token_urlsafe(32)
            logger.warning("Generated new JWT secret key. This should be configured in production.")
        
        # Generate initial API key
        if self.config.enable_api_key_auth:
            initial_key = self._generate_api_key()
            self.api_keys.add(initial_key)
            logger.info(f"Generated initial API key: {initial_key}")
    
    def _generate_api_key(self) -> str:
        """Generate a secure API key."""
        return f"at3gui_{secrets.token_urlsafe(32)}"
    
    def validate_api_key(self, api_key: str) -> bool:
        """Validate an API key."""
        return api_key in self.api_keys
    
    def add_api_key(self, api_key: str = None) -> str:
        """Add a new API key."""
        if not api_key:
            api_key = self._generate_api_key()
        
        self.api_keys.add(api_key)
        logger.info("New API key added")
        return api_key
    
    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key."""
        if api_key in self.api_keys:
            self.api_keys.remove(api_key)
            logger.info("API key revoked")
            return True
        return False
    
    def generate_jwt_token(self, user_id: str, permissions: List[str] = None) -> str:
        """Generate a JWT token."""
        payload = {
            'user_id': user_id,
            'permissions': permissions or [],
            'iat': datetime.utcnow(),
            'exp': datetime.utcnow() + timedelta(hours=self.config.jwt_expiration_hours)
        }
        
        return jwt.encode(payload, self.config.jwt_secret_key, algorithm=self.config.jwt_algorithm)
    
    def validate_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate a JWT token."""
        try:
            payload = jwt.decode(token, self.config.jwt_secret_key, algorithms=[self.config.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
            return None
    
    def check_rate_limit(self, client_ip: str) -> bool:
        """Check if client is within rate limits."""
        if client_ip in self.blocked_ips:
            return False
        
        now = datetime.now()
        
        # Get or create rate limit info
        if client_ip not in self.rate_limits:
            self.rate_limits[client_ip] = RateLimitInfo()
        
        rate_info = self.rate_limits[client_ip]
        
        # Check if client is temporarily blocked
        if rate_info.blocked_until and now < rate_info.blocked_until:
            return False
        
        # Clean old requests
        window_start = now - timedelta(minutes=self.config.rate_limit_window_minutes)
        rate_info.requests = [req_time for req_time in rate_info.requests if req_time > window_start]
        
        # Check rate limit
        if len(rate_info.requests) >= self.config.rate_limit_requests:
            # Block client for double the window time
            rate_info.blocked_until = now + timedelta(minutes=self.config.rate_limit_window_minutes * 2)
            logger.warning(f"Rate limit exceeded for {client_ip}, blocking for {self.config.rate_limit_window_minutes * 2} minutes")
            return False
        
        # Add current request
        rate_info.requests.append(now)
        return True
    
    def is_safe_path(self, path: str, base_path: Path) -> bool:
        """Check if a file path is safe (no directory traversal)."""
        try:
            base_path = base_path.resolve()
            full_path = (base_path / path).resolve()
            return str(full_path).startswith(str(base_path))
        except Exception:
            return False


class SecureWebAPI:
    """Secure web API implementation with enhanced security features."""
    
    def __init__(self, security_config: SecurityConfig = None):
        self.security_config = security_config or SecurityConfig()
        self.security_manager = SecurityManager(self.security_config)
        self.app = self._create_app()
        self._setup_middleware()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application with security settings."""
        app = FastAPI(
            title="AreTomo3 GUI Secure API",
            description="Secure REST API for AreTomo3 tomographic reconstruction",
            version="2.1.0",
            docs_url="/api/docs" if not self.security_config.require_https else None,
            redoc_url="/api/redoc" if not self.security_config.require_https else None
        )
        
        return app
    
    def _setup_middleware(self):
        """Setup security middleware."""
        # Trusted host middleware
        self.app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=self.security_config.allowed_hosts
        )
        
        # CORS middleware with restricted origins
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.security_config.allowed_origins,
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE"],
            allow_headers=["Authorization", "Content-Type", "X-API-Key"],
            max_age=3600
        )
        
        # Custom security middleware
        @self.app.middleware("http")
        async def security_middleware(request: Request, call_next):
            # Check HTTPS requirement
            if self.security_config.require_https and request.url.scheme != "https":
                return JSONResponse(
                    status_code=status.HTTP_426_UPGRADE_REQUIRED,
                    content={"detail": "HTTPS required"}
                )
            
            # Get client IP
            client_ip = self._get_client_ip(request)
            
            # Check rate limiting
            if not self.security_manager.check_rate_limit(client_ip):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={"detail": "Rate limit exceeded"}
                )
            
            # Check request size
            content_length = request.headers.get("content-length")
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                if size_mb > self.security_config.max_request_size_mb:
                    return JSONResponse(
                        status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                        content={"detail": "Request too large"}
                    )
            
            response = await call_next(request)
            
            # Add security headers
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
            
            return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host
    
    def _setup_routes(self):
        """Setup API routes with authentication."""
        
        # Authentication dependency
        security = HTTPBearer()
        
        async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
            """Verify JWT token or API key."""
            token = credentials.credentials
            
            # Try API key first
            if self.security_config.enable_api_key_auth and self.security_manager.validate_api_key(token):
                return {"type": "api_key", "token": token}
            
            # Try JWT token
            payload = self.security_manager.validate_jwt_token(token)
            if payload:
                return {"type": "jwt", "payload": payload}
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Public endpoints (no authentication required)
        @self.app.get("/api/health")
        async def health_check():
            """Health check endpoint."""
            return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}
        
        @self.app.post("/api/auth/login")
        async def login(credentials: Dict[str, str]):
            """Login endpoint to get JWT token."""
            # In a real implementation, validate credentials against user database
            username = credentials.get("username")
            password = credentials.get("password")
            
            if not username or not password:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username and password required"
                )
            
            # Simple validation (replace with real authentication)
            if username == "admin" and password == "admin":  # Change in production!
                token = self.security_manager.generate_jwt_token(
                    user_id=username,
                    permissions=["read", "write", "admin"]
                )
                return {"access_token": token, "token_type": "bearer"}
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Protected endpoints
        @self.app.get("/api/status")
        async def get_status(auth: Dict = Depends(verify_token)):
            """Get system status (protected endpoint)."""
            return {
                "status": "running",
                "authenticated_as": auth.get("payload", {}).get("user_id", "api_key_user"),
                "timestamp": datetime.utcnow().isoformat()
            }
        
        @self.app.get("/api/files")
        async def list_files(path: str = "/", auth: Dict = Depends(verify_token)):
            """List files in a directory (with path traversal protection)."""
            base_path = Path("/safe/base/path")  # Configure appropriately
            
            if not self.security_manager.is_safe_path(path, base_path):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid path"
                )
            
            try:
                full_path = base_path / path
                if not full_path.exists():
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Path not found"
                    )
                
                files = []
                if full_path.is_dir():
                    for item in full_path.iterdir():
                        files.append({
                            "name": item.name,
                            "type": "directory" if item.is_dir() else "file",
                            "size": item.stat().st_size if item.is_file() else None
                        })
                
                return {"path": path, "files": files}
                
            except Exception as e:
                logger.error(f"Error listing files: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Internal server error"
                )
        
        # Admin endpoints
        @self.app.post("/api/admin/api-keys")
        async def create_api_key(auth: Dict = Depends(verify_token)):
            """Create a new API key (admin only)."""
            # Check admin permission
            if auth.get("type") == "jwt":
                permissions = auth.get("payload", {}).get("permissions", [])
                if "admin" not in permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Admin permission required"
                    )
            
            api_key = self.security_manager.add_api_key()
            return {"api_key": api_key}
        
        @self.app.delete("/api/admin/api-keys/{api_key}")
        async def revoke_api_key(api_key: str, auth: Dict = Depends(verify_token)):
            """Revoke an API key (admin only)."""
            # Check admin permission
            if auth.get("type") == "jwt":
                permissions = auth.get("payload", {}).get("permissions", [])
                if "admin" not in permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Admin permission required"
                    )
            
            success = self.security_manager.revoke_api_key(api_key)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="API key not found"
                )
            
            return {"message": "API key revoked"}
    
    def run(self, host: str = "127.0.0.1", port: int = 8080, debug: bool = False):
        """Run the secure web server."""
        import uvicorn
        
        logger.info(f"Starting secure AreTomo3 web server on {host}:{port}")
        
        # Security warning for debug mode
        if debug:
            logger.warning("Running in debug mode - not suitable for production!")
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="debug" if debug else "info",
            reload=debug,
            ssl_keyfile="server.key" if self.security_config.require_https else None,
            ssl_certfile="server.crt" if self.security_config.require_https else None
        )


# Example usage
if __name__ == "__main__":
    # Production security configuration
    production_config = SecurityConfig(
        allowed_origins=["https://yourdomain.com"],
        allowed_hosts=["yourdomain.com", "api.yourdomain.com"],
        require_https=True,
        rate_limit_requests=50,
        rate_limit_window_minutes=10
    )
    
    # Development configuration
    dev_config = SecurityConfig(
        allowed_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
        allowed_hosts=["localhost", "127.0.0.1"],
        require_https=False
    )
    
    # Use development config for now
    api = SecureWebAPI(dev_config)
    api.run(debug=True)
