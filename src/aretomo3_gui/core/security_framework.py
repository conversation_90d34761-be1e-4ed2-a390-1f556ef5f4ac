#!/usr/bin/env python3
"""
AreTomo3 GUI Security Framework
Comprehensive security framework for authentication, authorization, and data protection.
"""

import logging
import hashlib
import secrets
import jwt
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)


@dataclass
class SecurityConfig:
    """Security configuration settings."""
    encryption_enabled: bool = True
    session_timeout: int = 3600  # 1 hour
    max_login_attempts: int = 5
    password_min_length: int = 8
    require_strong_passwords: bool = True
    jwt_secret_key: str = ""
    jwt_algorithm: str = "HS256"
    jwt_expiration: int = 3600
    audit_logging: bool = True
    secure_file_permissions: bool = True


@dataclass
class UserSession:
    """User session information."""
    session_id: str
    user_id: str
    username: str
    created_at: datetime
    last_activity: datetime
    permissions: List[str] = field(default_factory=list)
    is_active: bool = True


@dataclass
class AuditEvent:
    """Security audit event."""
    timestamp: datetime
    event_type: str
    user_id: str
    action: str
    resource: str
    success: bool
    details: Dict[str, Any] = field(default_factory=dict)


class SecurityFramework:
    """
    Comprehensive security framework for AreTomo3 GUI.
    Handles authentication, authorization, encryption, and audit logging.
    """
    
    def __init__(self, config_dir: Union[str, Path] = None):
        """Initialize the security framework."""
        self.config_dir = Path(config_dir) if config_dir else Path.home() / ".aretomo3_gui" / "security"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Security configuration
        self.config = SecurityConfig()
        self.config_file = self.config_dir / "security_config.json"
        
        # Session management
        self.active_sessions: Dict[str, UserSession] = {}
        self.failed_login_attempts: Dict[str, int] = {}
        
        # Audit logging
        self.audit_events: List[AuditEvent] = []
        self.audit_file = self.config_dir / "audit.log"
        
        # Encryption
        self.encryption_key = None
        self.cipher_suite = None
        
        # Load configuration
        self._load_config()
        self._initialize_encryption()
        
        logger.info("Security Framework initialized")
    
    def _load_config(self):
        """Load security configuration."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update config with loaded values
                for key, value in config_data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
            
            # Generate JWT secret if not set
            if not self.config.jwt_secret_key:
                self.config.jwt_secret_key = secrets.token_urlsafe(32)
                self._save_config()
            
        except Exception as e:
            logger.error(f"Error loading security config: {e}")
    
    def _save_config(self):
        """Save security configuration."""
        try:
            config_data = {
                'encryption_enabled': self.config.encryption_enabled,
                'session_timeout': self.config.session_timeout,
                'max_login_attempts': self.config.max_login_attempts,
                'password_min_length': self.config.password_min_length,
                'require_strong_passwords': self.config.require_strong_passwords,
                'jwt_secret_key': self.config.jwt_secret_key,
                'jwt_algorithm': self.config.jwt_algorithm,
                'jwt_expiration': self.config.jwt_expiration,
                'audit_logging': self.config.audit_logging,
                'secure_file_permissions': self.config.secure_file_permissions
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            # Set secure file permissions
            if self.config.secure_file_permissions:
                self.config_file.chmod(0o600)
            
        except Exception as e:
            logger.error(f"Error saving security config: {e}")
    
    def _initialize_encryption(self):
        """Initialize encryption system."""
        try:
            if not self.config.encryption_enabled:
                return
            
            key_file = self.config_dir / "encryption.key"
            
            if key_file.exists():
                # Load existing key
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Generate new key
                self.encryption_key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                
                # Set secure permissions
                if self.config.secure_file_permissions:
                    key_file.chmod(0o600)
            
            self.cipher_suite = Fernet(self.encryption_key)
            logger.info("Encryption system initialized")
            
        except Exception as e:
            logger.error(f"Error initializing encryption: {e}")
            self.config.encryption_enabled = False
    
    def hash_password(self, password: str, salt: str = None) -> tuple:
        """Hash a password with salt."""
        if salt is None:
            salt = secrets.token_hex(16)
        
        # Use PBKDF2 for password hashing
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
        )
        
        password_hash = base64.urlsafe_b64encode(kdf.derive(password.encode())).decode()
        return password_hash, salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify a password against its hash."""
        try:
            computed_hash, _ = self.hash_password(password, salt)
            return secrets.compare_digest(password_hash, computed_hash)
        except Exception as e:
            logger.error(f"Error verifying password: {e}")
            return False
    
    def validate_password_strength(self, password: str) -> tuple:
        """Validate password strength."""
        if not self.config.require_strong_passwords:
            return True, "Password accepted"
        
        issues = []
        
        if len(password) < self.config.password_min_length:
            issues.append(f"Password must be at least {self.config.password_min_length} characters")
        
        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password must contain at least one special character")
        
        if issues:
            return False, "; ".join(issues)
        
        return True, "Password meets strength requirements"
    
    def create_session(self, user_id: str, username: str, permissions: List[str] = None) -> str:
        """Create a new user session."""
        session_id = secrets.token_urlsafe(32)
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            username=username,
            created_at=datetime.now(),
            last_activity=datetime.now(),
            permissions=permissions or [],
            is_active=True
        )
        
        self.active_sessions[session_id] = session
        
        # Log audit event
        self._log_audit_event(
            event_type="authentication",
            user_id=user_id,
            action="session_created",
            resource="session",
            success=True,
            details={"session_id": session_id}
        )
        
        logger.info(f"Session created for user: {username}")
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """Validate and update session."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # Check if session is expired
        if datetime.now() - session.last_activity > timedelta(seconds=self.config.session_timeout):
            self.invalidate_session(session_id)
            return None
        
        # Update last activity
        session.last_activity = datetime.now()
        return session
    
    def invalidate_session(self, session_id: str):
        """Invalidate a session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.is_active = False
            
            # Log audit event
            self._log_audit_event(
                event_type="authentication",
                user_id=session.user_id,
                action="session_invalidated",
                resource="session",
                success=True,
                details={"session_id": session_id}
            )
            
            del self.active_sessions[session_id]
            logger.info(f"Session invalidated: {session_id}")
    
    def generate_jwt_token(self, user_id: str, username: str, permissions: List[str] = None) -> str:
        """Generate JWT token for API authentication."""
        payload = {
            'user_id': user_id,
            'username': username,
            'permissions': permissions or [],
            'iat': int(time.time()),
            'exp': int(time.time()) + self.config.jwt_expiration
        }
        
        token = jwt.encode(payload, self.config.jwt_secret_key, algorithm=self.config.jwt_algorithm)
        
        # Log audit event
        self._log_audit_event(
            event_type="authentication",
            user_id=user_id,
            action="jwt_token_generated",
            resource="token",
            success=True
        )
        
        return token
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token."""
        try:
            payload = jwt.decode(token, self.config.jwt_secret_key, algorithms=[self.config.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {e}")
            return None
    
    def encrypt_data(self, data: Union[str, bytes]) -> Optional[bytes]:
        """Encrypt sensitive data."""
        if not self.config.encryption_enabled or not self.cipher_suite:
            return data.encode() if isinstance(data, str) else data
        
        try:
            if isinstance(data, str):
                data = data.encode()
            
            encrypted_data = self.cipher_suite.encrypt(data)
            return encrypted_data
        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            return None
    
    def decrypt_data(self, encrypted_data: bytes) -> Optional[str]:
        """Decrypt sensitive data."""
        if not self.config.encryption_enabled or not self.cipher_suite:
            return encrypted_data.decode() if isinstance(encrypted_data, bytes) else encrypted_data
        
        try:
            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            return None
    
    def check_permission(self, session_id: str, required_permission: str) -> bool:
        """Check if session has required permission."""
        session = self.validate_session(session_id)
        if not session:
            return False
        
        # Admin users have all permissions
        if 'admin' in session.permissions:
            return True
        
        return required_permission in session.permissions
    
    def _log_audit_event(self, event_type: str, user_id: str, action: str, 
                        resource: str, success: bool, details: Dict[str, Any] = None):
        """Log security audit event."""
        if not self.config.audit_logging:
            return
        
        event = AuditEvent(
            timestamp=datetime.now(),
            event_type=event_type,
            user_id=user_id,
            action=action,
            resource=resource,
            success=success,
            details=details or {}
        )
        
        self.audit_events.append(event)
        
        # Write to audit log file
        try:
            with open(self.audit_file, 'a') as f:
                log_entry = {
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'user_id': event.user_id,
                    'action': event.action,
                    'resource': event.resource,
                    'success': event.success,
                    'details': event.details
                }
                f.write(json.dumps(log_entry) + '\n')
        except Exception as e:
            logger.error(f"Error writing audit log: {e}")
    
    def get_audit_events(self, event_type: str = None, user_id: str = None, 
                        start_time: datetime = None, end_time: datetime = None) -> List[AuditEvent]:
        """Get filtered audit events."""
        filtered_events = self.audit_events
        
        if event_type:
            filtered_events = [e for e in filtered_events if e.event_type == event_type]
        
        if user_id:
            filtered_events = [e for e in filtered_events if e.user_id == user_id]
        
        if start_time:
            filtered_events = [e for e in filtered_events if e.timestamp >= start_time]
        
        if end_time:
            filtered_events = [e for e in filtered_events if e.timestamp <= end_time]
        
        return filtered_events
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get security framework status."""
        return {
            'encryption_enabled': self.config.encryption_enabled,
            'active_sessions': len(self.active_sessions),
            'audit_events_count': len(self.audit_events),
            'failed_login_attempts': len(self.failed_login_attempts),
            'session_timeout': self.config.session_timeout,
            'password_requirements': {
                'min_length': self.config.password_min_length,
                'require_strong': self.config.require_strong_passwords
            }
        }
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if current_time - session.last_activity > timedelta(seconds=self.config.session_timeout):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.invalidate_session(session_id)
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")


# Global security framework instance
security_framework = SecurityFramework()


def require_permission(permission: str):
    """Decorator to require specific permission for function access."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # This would need to be integrated with the actual session management
            # For now, it's a placeholder for the security framework
            return func(*args, **kwargs)
        return wrapper
    return decorator


def secure_file_operation(func):
    """Decorator to ensure secure file operations."""
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            # Log file operation
            security_framework._log_audit_event(
                event_type="file_operation",
                user_id="system",
                action=func.__name__,
                resource="file",
                success=True
            )
            return result
        except Exception as e:
            security_framework._log_audit_event(
                event_type="file_operation",
                user_id="system",
                action=func.__name__,
                resource="file",
                success=False,
                details={"error": str(e)}
            )
            raise
    return wrapper
