#!/usr/bin/env python3
"""
AreTomo3 GUI Comprehensive Documentation System
Automatic documentation generation, interactive help, and user guides.
"""

import logging
import inspect
import ast
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import json
from datetime import datetime
import subprocess

# Documentation generation
try:
    import sphinx
    from sphinx.application import Sphinx
    from sphinx.util.docutils import docutils_namespace
    SPHINX_AVAILABLE = True
except ImportError:
    SPHINX_AVAILABLE = False

# Markdown processing
try:
    import markdown
    from markdown.extensions import codehilite, toc
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False

logger = logging.getLogger(__name__)


class DocumentationGenerator:
    """
    Comprehensive documentation generator for AreTomo3 GUI.
    Generates API docs, user guides, and interactive help.
    """
    
    def __init__(self, source_dir: Path = None, output_dir: Path = None):
        """Initialize the documentation generator."""
        self.source_dir = source_dir or Path(__file__).parent.parent
        self.output_dir = output_dir or Path.cwd() / "docs"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Documentation structure
        self.doc_structure = {
            'api': self.output_dir / "api",
            'user_guide': self.output_dir / "user_guide",
            'tutorials': self.output_dir / "tutorials",
            'examples': self.output_dir / "examples",
            'reference': self.output_dir / "reference"
        }
        
        # Create documentation directories
        for doc_dir in self.doc_structure.values():
            doc_dir.mkdir(parents=True, exist_ok=True)
        
        # Documentation metadata
        self.metadata = {
            'title': 'AreTomo3 GUI Documentation',
            'version': '1.0.0',
            'author': 'AreTomo3 GUI Team',
            'description': 'Comprehensive documentation for AreTomo3 GUI',
            'generated_at': datetime.now().isoformat()
        }
        
        # Module analysis cache
        self.module_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"Documentation Generator initialized - Output: {self.output_dir}")
    
    def generate_full_documentation(self) -> bool:
        """Generate complete documentation suite."""
        try:
            logger.info("Starting full documentation generation")
            
            # Generate API documentation
            self.generate_api_documentation()
            
            # Generate user guide
            self.generate_user_guide()
            
            # Generate tutorials
            self.generate_tutorials()
            
            # Generate examples
            self.generate_examples()
            
            # Generate reference documentation
            self.generate_reference_docs()
            
            # Generate main index
            self.generate_main_index()
            
            # Generate search index
            self.generate_search_index()
            
            logger.info("Full documentation generation completed")
            return True
            
        except Exception as e:
            logger.error(f"Error generating documentation: {e}")
            return False
    
    def generate_api_documentation(self) -> bool:
        """Generate API documentation from source code."""
        try:
            logger.info("Generating API documentation")
            
            # Analyze source code
            modules = self._analyze_source_code()
            
            # Generate module documentation
            for module_name, module_info in modules.items():
                self._generate_module_doc(module_name, module_info)
            
            # Generate API index
            self._generate_api_index(modules)
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating API documentation: {e}")
            return False
    
    def _analyze_source_code(self) -> Dict[str, Dict[str, Any]]:
        """Analyze source code to extract documentation information."""
        modules = {}
        
        # Find all Python files
        python_files = list(self.source_dir.rglob("*.py"))
        
        for py_file in python_files:
            if py_file.name.startswith('__'):
                continue
            
            try:
                # Get module name
                relative_path = py_file.relative_to(self.source_dir)
                module_name = str(relative_path.with_suffix('')).replace('/', '.')
                
                # Parse the file
                with open(py_file, 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Analyze module
                module_info = self._analyze_module(source_code, py_file)
                modules[module_name] = module_info
                
            except Exception as e:
                logger.warning(f"Error analyzing {py_file}: {e}")
                continue
        
        return modules
    
    def _analyze_module(self, source_code: str, file_path: Path) -> Dict[str, Any]:
        """Analyze a single module."""
        try:
            tree = ast.parse(source_code)
            
            module_info = {
                'file_path': str(file_path),
                'docstring': ast.get_docstring(tree),
                'classes': {},
                'functions': {},
                'constants': {},
                'imports': []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = self._analyze_class(node)
                    module_info['classes'][node.name] = class_info
                
                elif isinstance(node, ast.FunctionDef):
                    if not node.name.startswith('_'):  # Skip private functions
                        func_info = self._analyze_function(node)
                        module_info['functions'][node.name] = func_info
                
                elif isinstance(node, ast.Assign):
                    # Extract constants
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id.isupper():
                            module_info['constants'][target.id] = {
                                'line': node.lineno,
                                'value': ast.unparse(node.value) if hasattr(ast, 'unparse') else 'N/A'
                            }
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    import_info = self._analyze_import(node)
                    module_info['imports'].append(import_info)
            
            return module_info
            
        except Exception as e:
            logger.error(f"Error analyzing module: {e}")
            return {}
    
    def _analyze_class(self, node: ast.ClassDef) -> Dict[str, Any]:
        """Analyze a class definition."""
        class_info = {
            'name': node.name,
            'docstring': ast.get_docstring(node),
            'line': node.lineno,
            'bases': [ast.unparse(base) if hasattr(ast, 'unparse') else 'N/A' for base in node.bases],
            'methods': {},
            'properties': {}
        }
        
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                method_info = self._analyze_function(item)
                if item.name.startswith('__') and item.name.endswith('__'):
                    method_info['type'] = 'magic'
                elif item.name.startswith('_'):
                    method_info['type'] = 'private'
                else:
                    method_info['type'] = 'public'
                
                class_info['methods'][item.name] = method_info
        
        return class_info
    
    def _analyze_function(self, node: ast.FunctionDef) -> Dict[str, Any]:
        """Analyze a function definition."""
        func_info = {
            'name': node.name,
            'docstring': ast.get_docstring(node),
            'line': node.lineno,
            'args': [],
            'returns': None,
            'decorators': []
        }
        
        # Analyze arguments
        for arg in node.args.args:
            arg_info = {'name': arg.arg}
            if arg.annotation:
                arg_info['type'] = ast.unparse(arg.annotation) if hasattr(ast, 'unparse') else 'N/A'
            func_info['args'].append(arg_info)
        
        # Analyze return type
        if node.returns:
            func_info['returns'] = ast.unparse(node.returns) if hasattr(ast, 'unparse') else 'N/A'
        
        # Analyze decorators
        for decorator in node.decorator_list:
            func_info['decorators'].append(ast.unparse(decorator) if hasattr(ast, 'unparse') else 'N/A')
        
        return func_info
    
    def _analyze_import(self, node: Union[ast.Import, ast.ImportFrom]) -> Dict[str, Any]:
        """Analyze an import statement."""
        if isinstance(node, ast.Import):
            return {
                'type': 'import',
                'module': node.names[0].name if node.names else 'unknown',
                'alias': node.names[0].asname if node.names and node.names[0].asname else None
            }
        else:  # ast.ImportFrom
            return {
                'type': 'from_import',
                'module': node.module or 'unknown',
                'names': [alias.name for alias in node.names] if node.names else []
            }
    
    def _generate_module_doc(self, module_name: str, module_info: Dict[str, Any]):
        """Generate documentation for a single module."""
        doc_content = f"# {module_name}\n\n"
        
        # Module docstring
        if module_info.get('docstring'):
            doc_content += f"{module_info['docstring']}\n\n"
        
        # File path
        doc_content += f"**File:** `{module_info['file_path']}`\n\n"
        
        # Classes
        if module_info['classes']:
            doc_content += "## Classes\n\n"
            for class_name, class_info in module_info['classes'].items():
                doc_content += self._format_class_doc(class_name, class_info)
        
        # Functions
        if module_info['functions']:
            doc_content += "## Functions\n\n"
            for func_name, func_info in module_info['functions'].items():
                doc_content += self._format_function_doc(func_name, func_info)
        
        # Constants
        if module_info['constants']:
            doc_content += "## Constants\n\n"
            for const_name, const_info in module_info['constants'].items():
                doc_content += f"### {const_name}\n\n"
                doc_content += f"**Value:** `{const_info['value']}`\n\n"
        
        # Save to file
        output_file = self.doc_structure['api'] / f"{module_name.replace('.', '_')}.md"
        output_file.write_text(doc_content, encoding='utf-8')
    
    def _format_class_doc(self, class_name: str, class_info: Dict[str, Any]) -> str:
        """Format class documentation."""
        doc = f"### {class_name}\n\n"
        
        if class_info.get('docstring'):
            doc += f"{class_info['docstring']}\n\n"
        
        # Inheritance
        if class_info['bases']:
            doc += f"**Inherits from:** {', '.join(class_info['bases'])}\n\n"
        
        # Methods
        if class_info['methods']:
            doc += "#### Methods\n\n"
            for method_name, method_info in class_info['methods'].items():
                if method_info.get('type') == 'public':
                    doc += self._format_function_doc(method_name, method_info, indent="##### ")
        
        return doc + "\n"
    
    def _format_function_doc(self, func_name: str, func_info: Dict[str, Any], indent: str = "### ") -> str:
        """Format function documentation."""
        doc = f"{indent}{func_name}\n\n"
        
        # Function signature
        args_str = ", ".join([
            f"{arg['name']}: {arg.get('type', 'Any')}" for arg in func_info['args']
        ])
        returns_str = f" -> {func_info['returns']}" if func_info['returns'] else ""
        doc += f"```python\n{func_name}({args_str}){returns_str}\n```\n\n"
        
        # Docstring
        if func_info.get('docstring'):
            doc += f"{func_info['docstring']}\n\n"
        
        # Parameters
        if func_info['args']:
            doc += "**Parameters:**\n\n"
            for arg in func_info['args']:
                doc += f"- `{arg['name']}` ({arg.get('type', 'Any')}): Parameter description\n"
            doc += "\n"
        
        # Returns
        if func_info['returns']:
            doc += f"**Returns:** {func_info['returns']}\n\n"
        
        return doc
    
    def _generate_api_index(self, modules: Dict[str, Dict[str, Any]]):
        """Generate API documentation index."""
        index_content = "# API Reference\n\n"
        index_content += "Complete API documentation for AreTomo3 GUI.\n\n"
        
        index_content += "## Modules\n\n"
        for module_name, module_info in sorted(modules.items()):
            module_file = f"{module_name.replace('.', '_')}.md"
            description = module_info.get('docstring', 'No description available').split('\n')[0]
            index_content += f"- [{module_name}]({module_file}) - {description}\n"
        
        # Save index
        index_file = self.doc_structure['api'] / "index.md"
        index_file.write_text(index_content, encoding='utf-8')
    
    def generate_user_guide(self) -> bool:
        """Generate user guide documentation."""
        try:
            logger.info("Generating user guide")
            
            # User guide sections
            sections = [
                ("installation", "Installation Guide"),
                ("getting_started", "Getting Started"),
                ("basic_usage", "Basic Usage"),
                ("advanced_features", "Advanced Features"),
                ("troubleshooting", "Troubleshooting"),
                ("faq", "Frequently Asked Questions")
            ]
            
            for section_id, section_title in sections:
                self._generate_user_guide_section(section_id, section_title)
            
            # Generate user guide index
            self._generate_user_guide_index(sections)
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating user guide: {e}")
            return False
    
    def _generate_user_guide_section(self, section_id: str, section_title: str):
        """Generate a user guide section."""
        content = f"# {section_title}\n\n"
        
        if section_id == "installation":
            content += self._get_installation_content()
        elif section_id == "getting_started":
            content += self._get_getting_started_content()
        elif section_id == "basic_usage":
            content += self._get_basic_usage_content()
        elif section_id == "advanced_features":
            content += self._get_advanced_features_content()
        elif section_id == "troubleshooting":
            content += self._get_troubleshooting_content()
        elif section_id == "faq":
            content += self._get_faq_content()
        
        # Save section
        section_file = self.doc_structure['user_guide'] / f"{section_id}.md"
        section_file.write_text(content, encoding='utf-8')
    
    def _get_installation_content(self) -> str:
        """Get installation guide content."""
        return """
## System Requirements

- Python 3.8 or higher
- PyQt6
- NumPy, SciPy, Matplotlib
- AreTomo3 software

## Installation Steps

1. **Install Python Dependencies:**
   ```bash
   pip install PyQt6 numpy scipy matplotlib plotly
   ```

2. **Install AreTomo3:**
   Download and install AreTomo3 from the official website.

3. **Install AreTomo3 GUI:**
   ```bash
   git clone https://github.com/your-repo/aretomo3-gui.git
   cd aretomo3-gui
   pip install -e .
   ```

4. **Verify Installation:**
   ```bash
   python -m aretomo3_gui.gui.main_window
   ```

## Configuration

Configure AreTomo3 path and other settings in the GUI preferences.
"""
    
    def _get_getting_started_content(self) -> str:
        """Get getting started content."""
        return """
## Quick Start

1. **Launch the GUI:**
   ```bash
   python -m aretomo3_gui.gui.main_window
   ```

2. **Load Your Data:**
   - Click "Browse" to select your tilt series
   - Set input and output directories
   - Configure basic parameters

3. **Run Processing:**
   - Click "Start Processing"
   - Monitor progress in real-time
   - View results in the Analysis tab

## Basic Workflow

1. Data preparation
2. Parameter configuration
3. Processing execution
4. Results analysis
5. Export results
"""
    
    def _get_basic_usage_content(self) -> str:
        """Get basic usage content."""
        return """
## Main Interface

The AreTomo3 GUI consists of several main tabs:

- **Control Center:** Main processing controls
- **Parameters:** Detailed parameter configuration
- **Analysis:** Results visualization and analysis
- **Live Processing:** Real-time monitoring

## Processing Steps

1. **Input Configuration**
2. **Parameter Setup**
3. **Processing Execution**
4. **Results Review**
"""
    
    def _get_advanced_features_content(self) -> str:
        """Get advanced features content."""
        return """
## Advanced Features

- **Batch Processing:** Process multiple datasets
- **Custom Parameters:** Advanced parameter tuning
- **Interactive Plots:** Real-time visualization
- **Export Options:** Multiple output formats
- **Plugin System:** Extensible functionality
"""
    
    def _get_troubleshooting_content(self) -> str:
        """Get troubleshooting content."""
        return """
## Common Issues

### GUI Won't Start
- Check Python version (3.8+)
- Verify PyQt6 installation
- Check system dependencies

### Processing Errors
- Verify AreTomo3 installation
- Check input file formats
- Review parameter settings

### Performance Issues
- Monitor system resources
- Adjust processing parameters
- Check available disk space
"""
    
    def _get_faq_content(self) -> str:
        """Get FAQ content."""
        return """
## Frequently Asked Questions

### Q: What file formats are supported?
A: MRC, TIFF, and other common cryo-EM formats.

### Q: How do I optimize processing parameters?
A: Use the parameter optimization wizard in the GUI.

### Q: Can I process multiple datasets simultaneously?
A: Yes, use the batch processing feature.

### Q: How do I export results?
A: Use the Export tab to save results in various formats.
"""
    
    def _generate_user_guide_index(self, sections: List[tuple]):
        """Generate user guide index."""
        index_content = "# User Guide\n\n"
        index_content += "Comprehensive user guide for AreTomo3 GUI.\n\n"
        
        index_content += "## Contents\n\n"
        for section_id, section_title in sections:
            index_content += f"- [{section_title}]({section_id}.md)\n"
        
        # Save index
        index_file = self.doc_structure['user_guide'] / "index.md"
        index_file.write_text(index_content, encoding='utf-8')
    
    def generate_tutorials(self) -> bool:
        """Generate tutorial documentation."""
        try:
            logger.info("Generating tutorials")
            
            tutorials = [
                ("basic_processing", "Basic Tilt Series Processing"),
                ("advanced_analysis", "Advanced Data Analysis"),
                ("batch_processing", "Batch Processing Workflow"),
                ("custom_parameters", "Custom Parameter Configuration")
            ]
            
            for tutorial_id, tutorial_title in tutorials:
                self._generate_tutorial(tutorial_id, tutorial_title)
            
            # Generate tutorials index
            self._generate_tutorials_index(tutorials)
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating tutorials: {e}")
            return False
    
    def _generate_tutorial(self, tutorial_id: str, tutorial_title: str):
        """Generate a single tutorial."""
        content = f"# {tutorial_title}\n\n"
        content += f"Step-by-step tutorial for {tutorial_title.lower()}.\n\n"
        content += "## Prerequisites\n\n"
        content += "## Steps\n\n"
        content += "## Expected Results\n\n"
        content += "## Troubleshooting\n\n"
        
        # Save tutorial
        tutorial_file = self.doc_structure['tutorials'] / f"{tutorial_id}.md"
        tutorial_file.write_text(content, encoding='utf-8')
    
    def _generate_tutorials_index(self, tutorials: List[tuple]):
        """Generate tutorials index."""
        index_content = "# Tutorials\n\n"
        index_content += "Step-by-step tutorials for AreTomo3 GUI.\n\n"
        
        for tutorial_id, tutorial_title in tutorials:
            index_content += f"- [{tutorial_title}]({tutorial_id}.md)\n"
        
        # Save index
        index_file = self.doc_structure['tutorials'] / "index.md"
        index_file.write_text(index_content, encoding='utf-8')
    
    def generate_examples(self) -> bool:
        """Generate example documentation."""
        try:
            logger.info("Generating examples")
            
            # Create example scripts and documentation
            examples = [
                ("basic_script", "Basic Processing Script"),
                ("batch_script", "Batch Processing Script"),
                ("custom_analysis", "Custom Analysis Example"),
                ("plugin_example", "Plugin Development Example")
            ]
            
            for example_id, example_title in examples:
                self._generate_example(example_id, example_title)
            
            # Generate examples index
            self._generate_examples_index(examples)
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating examples: {e}")
            return False
    
    def _generate_example(self, example_id: str, example_title: str):
        """Generate a single example."""
        content = f"# {example_title}\n\n"
        content += f"Example demonstrating {example_title.lower()}.\n\n"
        content += "## Code\n\n"
        content += "```python\n# Example code here\npass\n```\n\n"
        content += "## Explanation\n\n"
        content += "## Usage\n\n"
        
        # Save example
        example_file = self.doc_structure['examples'] / f"{example_id}.md"
        example_file.write_text(content, encoding='utf-8')
    
    def _generate_examples_index(self, examples: List[tuple]):
        """Generate examples index."""
        index_content = "# Examples\n\n"
        index_content += "Code examples for AreTomo3 GUI.\n\n"
        
        for example_id, example_title in examples:
            index_content += f"- [{example_title}]({example_id}.md)\n"
        
        # Save index
        index_file = self.doc_structure['examples'] / "index.md"
        index_file.write_text(index_content, encoding='utf-8')
    
    def generate_reference_docs(self) -> bool:
        """Generate reference documentation."""
        try:
            logger.info("Generating reference documentation")
            
            # Generate parameter reference
            self._generate_parameter_reference()
            
            # Generate file format reference
            self._generate_file_format_reference()
            
            # Generate error code reference
            self._generate_error_reference()
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating reference docs: {e}")
            return False
    
    def _generate_parameter_reference(self):
        """Generate parameter reference documentation."""
        content = """# Parameter Reference

Complete reference for all AreTomo3 processing parameters.

## Basic Parameters

### Input/Output
- **Input Directory:** Path to input tilt series
- **Output Directory:** Path for output files
- **Pixel Size:** Pixel size in Angstroms

### Processing
- **Tilt Axis:** Tilt axis angle
- **Alignment:** Alignment method
- **Reconstruction:** Reconstruction algorithm

## Advanced Parameters

### CTF Correction
- **CTF Estimation:** Enable CTF estimation
- **Defocus Range:** Defocus search range
- **Resolution Limit:** High-resolution limit

### Motion Correction
- **Motion Correction:** Enable motion correction
- **Patch Size:** Patch size for motion estimation
- **B-factor:** B-factor for motion correction
"""
        
        param_file = self.doc_structure['reference'] / "parameters.md"
        param_file.write_text(content, encoding='utf-8')
    
    def _generate_file_format_reference(self):
        """Generate file format reference."""
        content = """# File Format Reference

Supported file formats and their specifications.

## Input Formats

### MRC Files
- Extension: .mrc, .mrcs
- Description: Medical Research Council format
- Usage: Tilt series, tomograms

### TIFF Files
- Extension: .tif, .tiff
- Description: Tagged Image File Format
- Usage: Image stacks

## Output Formats

### Reconstructed Tomograms
- Format: MRC
- Description: 3D reconstructed volume

### Alignment Files
- Format: Text/JSON
- Description: Alignment parameters

### Analysis Results
- Format: JSON/CSV
- Description: Quality metrics and statistics
"""
        
        format_file = self.doc_structure['reference'] / "file_formats.md"
        format_file.write_text(content, encoding='utf-8')
    
    def _generate_error_reference(self):
        """Generate error code reference."""
        content = """# Error Reference

Common error codes and their solutions.

## Processing Errors

### E001: Input File Not Found
**Cause:** Specified input file does not exist
**Solution:** Check file path and permissions

### E002: Invalid Parameter
**Cause:** Parameter value is out of valid range
**Solution:** Check parameter documentation

### E003: Insufficient Memory
**Cause:** Not enough system memory for processing
**Solution:** Reduce dataset size or increase memory

## System Errors

### S001: AreTomo3 Not Found
**Cause:** AreTomo3 executable not found
**Solution:** Install AreTomo3 and configure path

### S002: Permission Denied
**Cause:** Insufficient file system permissions
**Solution:** Check file and directory permissions
"""
        
        error_file = self.doc_structure['reference'] / "errors.md"
        error_file.write_text(content, encoding='utf-8')
    
    def generate_main_index(self):
        """Generate main documentation index."""
        index_content = f"""# {self.metadata['title']}

{self.metadata['description']}

**Version:** {self.metadata['version']}  
**Generated:** {self.metadata['generated_at']}

## Documentation Sections

- [API Reference](api/index.md) - Complete API documentation
- [User Guide](user_guide/index.md) - User guide and tutorials
- [Tutorials](tutorials/index.md) - Step-by-step tutorials
- [Examples](examples/index.md) - Code examples
- [Reference](reference/parameters.md) - Parameter and format reference

## Quick Links

- [Installation Guide](user_guide/installation.md)
- [Getting Started](user_guide/getting_started.md)
- [Troubleshooting](user_guide/troubleshooting.md)
- [FAQ](user_guide/faq.md)

## About

AreTomo3 GUI is a comprehensive graphical user interface for AreTomo3 
cryo-electron tomography processing software.
"""
        
        # Save main index
        main_index = self.output_dir / "index.md"
        main_index.write_text(index_content, encoding='utf-8')
    
    def generate_search_index(self):
        """Generate search index for documentation."""
        try:
            search_data = []
            
            # Index all markdown files
            for md_file in self.output_dir.rglob("*.md"):
                try:
                    content = md_file.read_text(encoding='utf-8')
                    
                    # Extract title
                    title_match = re.search(r'^# (.+)$', content, re.MULTILINE)
                    title = title_match.group(1) if title_match else md_file.stem
                    
                    # Extract first paragraph as description
                    lines = content.split('\n')
                    description = ""
                    for line in lines[1:]:
                        if line.strip() and not line.startswith('#'):
                            description = line.strip()
                            break
                    
                    search_data.append({
                        'title': title,
                        'description': description,
                        'url': str(md_file.relative_to(self.output_dir)),
                        'content': content[:500]  # First 500 chars
                    })
                    
                except Exception as e:
                    logger.warning(f"Error indexing {md_file}: {e}")
            
            # Save search index
            search_file = self.output_dir / "search_index.json"
            with open(search_file, 'w', encoding='utf-8') as f:
                json.dump(search_data, f, indent=2)
            
            logger.info(f"Generated search index with {len(search_data)} entries")
            
        except Exception as e:
            logger.error(f"Error generating search index: {e}")


# Global documentation generator instance
doc_generator = DocumentationGenerator()


def generate_documentation(source_dir: Path = None, output_dir: Path = None) -> bool:
    """Convenience function to generate documentation."""
    generator = DocumentationGenerator(source_dir, output_dir)
    return generator.generate_full_documentation()
