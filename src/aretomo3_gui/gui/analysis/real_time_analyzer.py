#!/usr/bin/env python3
"""
Real-time Analysis Engine for AreTomo3 Results.
Parses result files, generates plots, and auto-saves them as PNG.
"""

import os
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.style as mplstyle
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime
import json

from PyQt6.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel

logger = logging.getLogger(__name__)

# Set matplotlib style for better plots
mplstyle.use('seaborn-v0_8')
plt.rcParams['figure.figsize'] = (10, 6)
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10

class AreTomo3ResultsParser:
    """Parser for AreTomo3 result files."""

    def __init__(self):
        self.supported_files = {
            'metrics': 'TiltSeries_Metrics.csv',
            'timestamps': 'TiltSeries_TimeStamp.csv',
            'ctf': '*_CTF.txt',
            'tilt_angles': '*_TLT.txt',
            'alignment': '*.aln'
        }

    def parse_metrics(self, file_path: str) -> Optional[Dict]:
        """Parse TiltSeries_Metrics.csv file."""
        try:
            df = pd.read_csv(file_path)
            if df.empty:
                return None

            row = df.iloc[0]
            return {
                'tilt_series': row['Tilt_Series'],
                'thickness_pix': float(row['Thickness(Pix)']),
                'tilt_axis': float(row['Tilt_Axis']),
                'global_shift': float(row['Global_Shift(Pix)']),
                'bad_patch_low': float(row['Bad_Patch_Low']),
                'bad_patch_all': float(row['Bad_Patch_All']),
                'ctf_resolution': float(row['CTF_Res(A)']),
                'ctf_score': float(row['CTF_Score']),
                'pixel_size': float(row['Pix_Size(A)']),
                'cs': float(row['Cs(nm)']),
                'voltage': float(row['Kv']),
                'alpha0': float(row['Alpha0']),
                'beta0': float(row['Beta0'])
            }
        except Exception as e:
            logger.error(f"Error parsing metrics file {file_path}: {e}")
            return None

    def parse_ctf_data(self, file_path: str) -> Optional[pd.DataFrame]:
        """Parse CTF data from *_CTF.txt file."""
        try:
            # Skip comment lines starting with #
            df = pd.read_csv(file_path, sep=r'\s+', comment='#',
                           names=['micrograph', 'defocus1', 'defocus2', 'astigmatism',
                                 'phase_shift', 'cross_correlation', 'ctf_resolution', 'df_hand'])
            return df
        except Exception as e:
            logger.error(f"Error parsing CTF file {file_path}: {e}")
            return None

    def parse_tilt_angles(self, file_path: str) -> Optional[List[float]]:
        """Parse tilt angles from *_TLT.txt file."""
        try:
            with open(file_path, 'r') as f:
                angles = [float(line.strip()) for line in f if line.strip()]
            return angles
        except Exception as e:
            logger.error(f"Error parsing tilt angles file {file_path}: {e}")
            return None

    def find_result_files(self, output_dir: str) -> Dict[str, List[str]]:
        """Find all AreTomo3 result files in output directory."""
        result_files = {}

        for file_type, pattern in self.supported_files.items():
            files = glob.glob(os.path.join(output_dir, "**", pattern), recursive=True)
            result_files[file_type] = files

        return result_files

class RealTimePlotGenerator:
    """Generates plots from AreTomo3 results with active/inactive visualization."""

    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        self.plots_dir = os.path.join(output_dir, "analysis_plots")
        os.makedirs(self.plots_dir, exist_ok=True)

        # Track all series for multi-series plotting
        self.all_series_data = {}
        self.active_series = None

        # Plot styling for active vs inactive
        self.active_style = {'linewidth': 3, 'alpha': 1.0, 'zorder': 10}
        self.inactive_style = {'linewidth': 1, 'alpha': 0.3, 'zorder': 1}

    def generate_ctf_analysis_plot(self, ctf_data: pd.DataFrame, tomo_name: str) -> str:
        """Generate CTF analysis plots."""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'CTF Analysis - {tomo_name}', fontsize=14, fontweight='bold')

        # Defocus vs tilt angle
        axes[0, 0].plot(ctf_data.index, ctf_data['defocus1'], 'b-', label='Defocus 1', linewidth=2)
        axes[0, 0].plot(ctf_data.index, ctf_data['defocus2'], 'r-', label='Defocus 2', linewidth=2)
        axes[0, 0].set_xlabel('Tilt Image Number')
        axes[0, 0].set_ylabel('Defocus (Å)')
        axes[0, 0].set_title('Defocus vs Tilt Image')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # CTF resolution
        axes[0, 1].plot(ctf_data.index, ctf_data['ctf_resolution'], 'g-', linewidth=2)
        axes[0, 1].set_xlabel('Tilt Image Number')
        axes[0, 1].set_ylabel('CTF Resolution (Å)')
        axes[0, 1].set_title('CTF Resolution vs Tilt Image')
        axes[0, 1].grid(True, alpha=0.3)

        # Cross correlation
        axes[1, 0].plot(ctf_data.index, ctf_data['cross_correlation'], 'purple', linewidth=2)
        axes[1, 0].set_xlabel('Tilt Image Number')
        axes[1, 0].set_ylabel('Cross Correlation')
        axes[1, 0].set_title('CTF Fit Quality')
        axes[1, 0].grid(True, alpha=0.3)

        # Astigmatism
        axes[1, 1].plot(ctf_data.index, ctf_data['astigmatism'], 'orange', linewidth=2)
        axes[1, 1].set_xlabel('Tilt Image Number')
        axes[1, 1].set_ylabel('Astigmatism Angle (°)')
        axes[1, 1].set_title('Astigmatism vs Tilt Image')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # Save plot
        plot_path = os.path.join(self.plots_dir, f"{tomo_name}_CTF_analysis.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Generated CTF analysis plot: {plot_path}")
        return plot_path

    def generate_quality_metrics_plot(self, metrics: Dict, tomo_name: str) -> str:
        """Generate quality metrics summary plot."""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'Quality Metrics - {tomo_name}', fontsize=14, fontweight='bold')

        # Quality metrics bar chart
        metrics_names = ['CTF Resolution', 'CTF Score', 'Global Shift', 'Thickness']
        metrics_values = [
            metrics['ctf_resolution'],
            metrics['ctf_score'] * 100,  # Convert to percentage
            metrics['global_shift'],
            metrics['thickness_pix']
        ]

        colors = ['skyblue', 'lightgreen', 'orange', 'lightcoral']

        axes[0, 0].bar(metrics_names, metrics_values, color=colors)
        axes[0, 0].set_title('Quality Metrics Overview')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # Tilt axis visualization
        axes[0, 1].arrow(0, 0, np.cos(np.radians(metrics['tilt_axis'])),
                        np.sin(np.radians(metrics['tilt_axis'])),
                        head_width=0.1, head_length=0.1, fc='red', ec='red')
        axes[0, 1].set_xlim(-1.5, 1.5)
        axes[0, 1].set_ylim(-1.5, 1.5)
        axes[0, 1].set_aspect('equal')
        axes[0, 1].set_title(f'Tilt Axis: {metrics["tilt_axis"]:.1f}°')
        axes[0, 1].grid(True, alpha=0.3)

        # Bad patches visualization
        patch_labels = ['Good Patches', 'Bad Patches (Low)', 'Bad Patches (All)']
        patch_values = [100 - metrics['bad_patch_all'],
                       metrics['bad_patch_low'],
                       metrics['bad_patch_all']]

        axes[1, 0].pie(patch_values, labels=patch_labels, autopct='%1.1f%%',
                      colors=['lightgreen', 'yellow', 'lightcoral'])
        axes[1, 0].set_title('Patch Quality Distribution')

        # Microscope parameters
        param_text = f"""Pixel Size: {metrics['pixel_size']:.2f} Å
Voltage: {metrics['voltage']:.0f} kV
Cs: {metrics['cs']:.1f} mm
Alpha0: {metrics['alpha0']:.1f}°
Beta0: {metrics['beta0']:.1f}°"""

        axes[1, 1].text(0.1, 0.5, param_text, fontsize=12,
                       verticalalignment='center', fontfamily='monospace')
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].set_title('Microscope Parameters')
        axes[1, 1].axis('off')

        plt.tight_layout()

        # Save plot
        plot_path = os.path.join(self.plots_dir, f"{tomo_name}_quality_metrics.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Generated quality metrics plot: {plot_path}")
        return plot_path

    def generate_processing_summary(self, all_results: List[Dict], mode: str) -> str:
        """Generate processing summary plot for batch/live mode."""
        if not all_results:
            return ""

        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'Processing Summary - {mode.title()} Mode ({len(all_results)} tomograms)',
                    fontsize=14, fontweight='bold')

        # Extract data for plotting
        tomo_names = [r['tomo_name'] for r in all_results]
        ctf_resolutions = [r['metrics']['ctf_resolution'] for r in all_results]
        ctf_scores = [r['metrics']['ctf_score'] for r in all_results]
        thicknesses = [r['metrics']['thickness_pix'] for r in all_results]
        tilt_axes = [r['metrics']['tilt_axis'] for r in all_results]

        # CTF resolution comparison
        axes[0, 0].bar(range(len(tomo_names)), ctf_resolutions, color='skyblue')
        axes[0, 0].set_xlabel('Tomogram')
        axes[0, 0].set_ylabel('CTF Resolution (Å)')
        axes[0, 0].set_title('CTF Resolution Comparison')
        axes[0, 0].set_xticks(range(len(tomo_names)))
        axes[0, 0].set_xticklabels([name[:8] for name in tomo_names], rotation=45)

        # CTF score comparison
        axes[0, 1].bar(range(len(tomo_names)), ctf_scores, color='lightgreen')
        axes[0, 1].set_xlabel('Tomogram')
        axes[0, 1].set_ylabel('CTF Score')
        axes[0, 1].set_title('CTF Score Comparison')
        axes[0, 1].set_xticks(range(len(tomo_names)))
        axes[0, 1].set_xticklabels([name[:8] for name in tomo_names], rotation=45)

        # Thickness distribution
        axes[1, 0].hist(thicknesses, bins=10, color='orange', alpha=0.7, edgecolor='black')
        axes[1, 0].set_xlabel('Thickness (pixels)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Thickness Distribution')

        # Tilt axis distribution
        axes[1, 1].hist(tilt_axes, bins=10, color='lightcoral', alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('Tilt Axis (degrees)')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title('Tilt Axis Distribution')

        plt.tight_layout()

        # Save plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plot_path = os.path.join(self.plots_dir, f"processing_summary_{mode}_{timestamp}.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Generated processing summary plot: {plot_path}")
        return plot_path

class RealTimeAnalysisMonitor(QThread):
    """Real-time monitor for AreTomo3 results with automatic analysis."""

    analysis_completed = pyqtSignal(str, dict)  # tomo_name, analysis_results
    new_plots_generated = pyqtSignal(list)  # list of plot paths

    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitoring_dirs = []
        self.parser = AreTomo3ResultsParser()
        self.known_results = set()
        self.running = False
        self.check_interval = 3.0  # seconds
        self.all_results = []  # Store all results for batch/live summaries

    def add_monitoring_directory(self, directory: str):
        """Add directory to monitor for new results."""
        if directory not in self.monitoring_dirs:
            self.monitoring_dirs.append(directory)
            logger.info(f"Added results monitoring directory: {directory}")

    def start_monitoring(self, mode: str = "single"):
        """Start monitoring for results."""
        self.processing_mode = mode
        self.running = True
        self.start()
        logger.info(f"Started real-time analysis monitoring in {mode} mode")

    def stop_monitoring(self):
        """Stop monitoring."""
        self.running = False
        self.wait()
        logger.info("Stopped real-time analysis monitoring")

    def run(self):
        """Main monitoring loop."""
        while self.running:
            try:
                self.check_for_new_results()
                self.msleep(int(self.check_interval * 1000))
            except Exception as e:
                logger.error(f"Error in analysis monitoring: {e}")

    def check_for_new_results(self):
        """Check for new AreTomo3 results and analyze them."""
        for monitor_dir in self.monitoring_dirs:
            if not os.path.exists(monitor_dir):
                continue

            # Find all result directories
            result_dirs = glob.glob(os.path.join(monitor_dir, "**/"), recursive=True)

            for result_dir in result_dirs:
                # Check if this directory has AreTomo3 results
                metrics_file = os.path.join(result_dir, "TiltSeries_Metrics.csv")

                if os.path.exists(metrics_file) and result_dir not in self.known_results:
                    self.analyze_new_result(result_dir)
                    self.known_results.add(result_dir)

    def analyze_new_result(self, result_dir: str):
        """Analyze a new AreTomo3 result directory."""
        try:
            tomo_name = os.path.basename(result_dir.rstrip('/'))
            logger.info(f"Analyzing new result: {tomo_name}")

            # Parse metrics
            metrics_file = os.path.join(result_dir, "TiltSeries_Metrics.csv")
            metrics = self.parser.parse_metrics(metrics_file)

            if not metrics:
                logger.warning(f"Could not parse metrics for {tomo_name}")
                return

            # Parse CTF data
            ctf_files = glob.glob(os.path.join(result_dir, "*_CTF.txt"))
            ctf_data = None
            if ctf_files:
                ctf_data = self.parser.parse_ctf_data(ctf_files[0])

            # Generate plots
            plot_generator = RealTimePlotGenerator(result_dir)
            generated_plots = []

            # Generate quality metrics plot
            quality_plot = plot_generator.generate_quality_metrics_plot(metrics, tomo_name)
            generated_plots.append(quality_plot)

            # Generate CTF analysis plot if CTF data available
            if ctf_data is not None and not ctf_data.empty:
                ctf_plot = plot_generator.generate_ctf_analysis_plot(ctf_data, tomo_name)
                generated_plots.append(ctf_plot)

            # Store result for batch/live summary
            result_data = {
                'tomo_name': tomo_name,
                'result_dir': result_dir,
                'metrics': metrics,
                'ctf_data': ctf_data,
                'plots': generated_plots,
                'timestamp': datetime.now().isoformat()
            }

            self.all_results.append(result_data)

            # Generate batch/live summary if in batch or live mode
            if self.processing_mode in ['batch', 'live'] and len(self.all_results) > 1:
                summary_plot = plot_generator.generate_processing_summary(
                    self.all_results, self.processing_mode)
                if summary_plot:
                    generated_plots.append(summary_plot)

            # Emit signals
            self.analysis_completed.emit(tomo_name, result_data)
            self.new_plots_generated.emit(generated_plots)

            logger.info(f"Analysis completed for {tomo_name}, generated {len(generated_plots)} plots")

        except Exception as e:
            logger.error(f"Error analyzing result {result_dir}: {e}")

class RealTimeAnalysisWidget(QWidget):
    """Widget for displaying real-time analysis results."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.analysis_monitor = RealTimeAnalysisMonitor(self)
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)

        # Status and controls
        status_layout = QHBoxLayout()
        self.status_label = QLabel("🔴 Analysis monitoring stopped")
        self.status_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        layout.addLayout(status_layout)

        # Analysis results tabs
        self.results_tabs = QTabWidget()

        # Recent analyses tab
        self.recent_tab = QWidget()
        self.recent_layout = QVBoxLayout(self.recent_tab)
        self.recent_label = QLabel("No analyses completed yet")
        self.recent_layout.addWidget(self.recent_label)
        self.results_tabs.addTab(self.recent_tab, "📊 Recent Analyses")

        # Summary tab
        self.summary_tab = QWidget()
        self.summary_layout = QVBoxLayout(self.summary_tab)
        self.summary_label = QLabel("Processing summary will appear here")
        self.summary_layout.addWidget(self.summary_label)
        self.results_tabs.addTab(self.summary_tab, "📈 Summary")

        layout.addWidget(self.results_tabs)

    def connect_signals(self):
        """Connect analysis monitor signals."""
        self.analysis_monitor.analysis_completed.connect(self.handle_analysis_completed)
        self.analysis_monitor.new_plots_generated.connect(self.handle_new_plots)

    def start_analysis_monitoring(self, output_dir: str, mode: str = "single"):
        """Start real-time analysis monitoring."""
        self.analysis_monitor.add_monitoring_directory(output_dir)
        self.analysis_monitor.start_monitoring(mode)

        self.status_label.setText("🟢 Analysis monitoring active")
        self.status_label.setStyleSheet("font-weight: bold; color: #28a745;")

    def stop_analysis_monitoring(self):
        """Stop real-time analysis monitoring."""
        self.analysis_monitor.stop_monitoring()

        self.status_label.setText("🔴 Analysis monitoring stopped")
        self.status_label.setStyleSheet("font-weight: bold; color: #dc3545;")

    def handle_analysis_completed(self, tomo_name: str, result_data: dict):
        """Handle completed analysis."""
        # Update recent analyses
        analysis_text = f"""
📊 Analysis completed for: {tomo_name}
🎯 CTF Resolution: {result_data['metrics']['ctf_resolution']:.1f} Å
📏 Thickness: {result_data['metrics']['thickness_pix']:.0f} pixels
📐 Tilt Axis: {result_data['metrics']['tilt_axis']:.1f}°
⭐ CTF Score: {result_data['metrics']['ctf_score']:.3f}
📁 Plots generated: {len(result_data['plots'])}
⏰ Time: {datetime.now().strftime('%H:%M:%S')}
"""

        current_text = self.recent_label.text()
        if current_text == "No analyses completed yet":
            self.recent_label.setText(analysis_text)
        else:
            self.recent_label.setText(analysis_text + "\n" + "="*50 + "\n" + current_text)

    def handle_new_plots(self, plot_paths: list):
        """Handle new plots generated."""
        logger.info(f"New plots generated: {plot_paths}")
        # Could add plot preview functionality here
