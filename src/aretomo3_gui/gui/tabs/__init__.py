"""
Tab modules for AreTomo3 GUI.
Each tab is implemented as a separate module for better organization.
"""

from .main_tab import MainTabManager
from .analysis_tab import AnalysisTabManager
from .batch_tab import BatchTabManager
from .monitor_tab import MonitorTabManager
from .viewer_tab import ViewerTabManager
from .export_tab import ExportTabManager
from .log_tab import LogTabManager

__all__ = [
    'MainTabManager',
    'AnalysisTabManager', 
    'BatchTabManager',
    'MonitorTabManager',
    'ViewerTabManager',
    'ExportTabManager',
    'LogTabManager'
]
