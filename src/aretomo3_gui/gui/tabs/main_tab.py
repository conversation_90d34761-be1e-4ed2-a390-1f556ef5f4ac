"""
Main tab manager for AreTomo3 GUI.
Handles the main processing tab with input/output settings and parameters.
"""
import os
import logging
from typing import Optional
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox, QLineEdit, QSpinBox, QDoubleSpinBox,
    QCheckBox, QComboBox, QScrollArea, QTextEdit
)
from PyQt6.QtCore import Qt

logger = logging.getLogger(__name__)


class MainTabManager:
    """Manages the main tab setup and functionality."""
    
    def __init__(self, main_window):
        """Initialize the main tab manager.
        
        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        
    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the main tab with input/output settings and parameters.

        Args:
            tab_widget: The parent widget for the main tab
            layout: The layout to add widgets to
        """
        try:
            # Create a scroll area and main container widget
            scroll = QScrollArea(tab_widget)
            scroll.setWidgetResizable(True)
            scroll_container = QWidget()
            main_layout = QVBoxLayout(scroll_container)
            main_layout.setSpacing(10)

            # Welcome message
            welcome_label = QLabel("Welcome to AreTomo3 GUI", tab_widget)
            welcome_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
            main_layout.addWidget(welcome_label)

            # Setup main sections
            self._setup_aretomo_settings(main_layout, tab_widget)
            self._setup_io_settings(main_layout, tab_widget)
            self._setup_quick_actions(main_layout, tab_widget)
            self._setup_command_preview(main_layout, tab_widget)
            self._setup_microscope_parameters(main_layout, tab_widget)
            self._setup_processing_parameters(main_layout, tab_widget)
            self._setup_processing_options(main_layout, tab_widget)

            # Add stretch to keep everything at the top
            main_layout.addStretch()

            # Set the scroll area widget and add to tab layout
            scroll.setWidget(scroll_container)
            layout.addWidget(scroll)

            logger.info("Main tab setup completed successfully")

        except Exception as e:
            logger.error(f"Error setting up main tab: {str(e)}", exc_info=True)
            raise

    def _setup_aretomo_settings(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup AreTomo3 basic settings section."""
        aretomo_group = QGroupBox("AreTomo3 Settings", parent)
        aretomo_layout = QGridLayout()
        aretomo_layout.setColumnStretch(1, 1)

        # AreTomo3 path
        self.main_window.aretomo_path = QLineEdit(aretomo_group)
        self.main_window.aretomo_path.setText(os.environ.get('ARETOMO3_PATH', ''))
        browse_aretomo_btn = QPushButton("Browse...", aretomo_group)
        browse_aretomo_btn.clicked.connect(self.main_window.on_browse_aretomo)
        aretomo_layout.addWidget(QLabel("AreTomo3 Path:", aretomo_group), 0, 0)
        aretomo_layout.addWidget(self.main_window.aretomo_path, 0, 1)
        aretomo_layout.addWidget(browse_aretomo_btn, 0, 2)

        # GPU index selector
        self.main_window.gpu_index = QSpinBox(aretomo_group)
        self.main_window.gpu_index.setRange(0, 7)
        self.main_window.gpu_index.setToolTip("Select which GPU to use (0-7)")
        aretomo_layout.addWidget(QLabel("GPU Index:", aretomo_group), 1, 0)
        aretomo_layout.addWidget(self.main_window.gpu_index, 1, 1)

        aretomo_group.setLayout(aretomo_layout)
        layout.addWidget(aretomo_group)

    def _setup_io_settings(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup input/output settings section."""
        io_group = QGroupBox("Input/Output Settings", parent)
        io_layout = QGridLayout()
        io_layout.setColumnStretch(1, 1)

        # Input directory
        self.main_window.input_dir = QLineEdit(io_group)
        browse_input_btn = QPushButton("Browse...", io_group)
        browse_input_btn.clicked.connect(self.main_window.on_browse_input)
        io_layout.addWidget(QLabel("Input Directory:", io_group), 0, 0)
        io_layout.addWidget(self.main_window.input_dir, 0, 1)
        io_layout.addWidget(browse_input_btn, 0, 2)

        # Output directory
        self.main_window.output_dir = QLineEdit(io_group)
        browse_output_btn = QPushButton("Browse...", io_group)
        browse_output_btn.clicked.connect(self.main_window.on_browse_output)
        io_layout.addWidget(QLabel("Output Directory:", io_group), 1, 0)
        io_layout.addWidget(self.main_window.output_dir, 1, 1)
        io_layout.addWidget(browse_output_btn, 1, 2)

        io_group.setLayout(io_layout)
        layout.addWidget(io_group)

    def _setup_quick_actions(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup quick actions section."""
        actions_group = QGroupBox("Quick Actions", parent)
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)

        load_btn = QPushButton("Load Tilt Series", actions_group)
        load_btn.clicked.connect(self.main_window.on_load_tilt_series)
        process_btn = QPushButton("Process", actions_group)
        process_btn.clicked.connect(self.main_window.on_process)
        stop_btn = QPushButton("Stop", actions_group)
        stop_btn.clicked.connect(self.main_window.on_stop)

        actions_layout.addWidget(load_btn)
        actions_layout.addWidget(process_btn)
        actions_layout.addWidget(stop_btn)
        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)

    def _setup_command_preview(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup command preview section."""
        command_group = QGroupBox("Command Preview", parent)
        command_layout = QVBoxLayout()
        command_layout.setSpacing(5)
        command_layout.setContentsMargins(10, 10, 10, 10)

        preview_container = QWidget()
        preview_hlayout = QHBoxLayout(preview_container)
        preview_hlayout.setContentsMargins(0, 0, 0, 0)
        preview_hlayout.setSpacing(10)

        self.main_window.command_preview = QTextEdit()
        self.main_window.command_preview.setReadOnly(True)
        self.main_window.command_preview.setMaximumHeight(60)
        self.main_window.command_preview.setStyleSheet("font-family: monospace;")
        preview_hlayout.addWidget(self.main_window.command_preview)

        preview_btn = QPushButton("Preview Command")
        preview_btn.setFixedWidth(150)
        preview_btn.setMinimumHeight(40)
        preview_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                padding: 8px;
            }
        """)
        preview_btn.clicked.connect(self.main_window._preview_command)
        preview_hlayout.addWidget(preview_btn)

        command_layout.addWidget(preview_container)
        command_group.setLayout(command_layout)
        layout.addWidget(command_group)

    def _setup_microscope_parameters(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup microscope parameters section."""
        microscope_group = QGroupBox("Microscope Parameters")
        microscope_layout = QHBoxLayout()

        # Left column
        left_params = QFormLayout()
        left_params.setSpacing(10)

        self.main_window.pixel_size = QDoubleSpinBox()
        self.main_window.pixel_size.setRange(0.1, 100.0)
        self.main_window.pixel_size.setValue(1.91)
        self.main_window.pixel_size.setSuffix(" Å")
        left_params.addRow("Pixel Size:", self.main_window.pixel_size)

        self.main_window.voltage = QSpinBox()
        self.main_window.voltage.setRange(60, 300)
        self.main_window.voltage.setValue(300)
        self.main_window.voltage.setSuffix(" kV")
        left_params.addRow("Voltage:", self.main_window.voltage)

        self.main_window.cs = QDoubleSpinBox()
        self.main_window.cs.setRange(0, 10)
        self.main_window.cs.setValue(2.7)
        self.main_window.cs.setSuffix(" mm")
        left_params.addRow("Spherical Aberration (Cs):", self.main_window.cs)

        self.main_window.frame_dose = QDoubleSpinBox()
        self.main_window.frame_dose.setRange(0.01, 10.0)
        self.main_window.frame_dose.setValue(0.14)
        self.main_window.frame_dose.setSingleStep(0.01)
        self.main_window.frame_dose.setSuffix(" e⁻/Å²")
        left_params.addRow("Frame Dose:", self.main_window.frame_dose)

        left_container = QWidget()
        left_container.setLayout(left_params)
        microscope_layout.addWidget(left_container)

        # Right column
        right_params = QFormLayout()
        right_params.setSpacing(10)

        self.main_window.tilt_axis = QDoubleSpinBox()
        self.main_window.tilt_axis.setRange(-180, 180)
        self.main_window.tilt_axis.setValue(-95.75)
        self.main_window.tilt_axis.setSuffix("°")
        right_params.addRow("Tilt Axis:", self.main_window.tilt_axis)

        self.main_window.amp_contrast = QDoubleSpinBox()
        self.main_window.amp_contrast.setRange(0, 1)
        self.main_window.amp_contrast.setValue(0.1)
        self.main_window.amp_contrast.setSingleStep(0.05)
        right_params.addRow("Amplitude Contrast:", self.main_window.amp_contrast)

        self.main_window.volume_z = QSpinBox()
        self.main_window.volume_z.setRange(64, 8192)
        self.main_window.volume_z.setValue(2048)
        right_params.addRow("Volume Z Size:", self.main_window.volume_z)

        self.main_window.lowpass = QSpinBox()
        self.main_window.lowpass.setRange(1, 100)
        self.main_window.lowpass.setValue(15)
        right_params.addRow("CTF Lowpass Filter:", self.main_window.lowpass)

        self.main_window.dark_tol = QDoubleSpinBox()
        self.main_window.dark_tol.setRange(0.0, 1.0)
        self.main_window.dark_tol.setValue(0.7)
        self.main_window.dark_tol.setSingleStep(0.1)
        right_params.addRow("Dark Tolerance:", self.main_window.dark_tol)

        right_container = QWidget()
        right_container.setLayout(right_params)
        microscope_layout.addWidget(right_container)

        microscope_group.setLayout(microscope_layout)
        layout.addWidget(microscope_group)

    def _setup_processing_parameters(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup processing parameters section."""
        processing_container = QWidget()
        processing_layout = QHBoxLayout(processing_container)
        processing_layout.setSpacing(10)
        processing_layout.setContentsMargins(0, 0, 0, 0)

        # Motion Correction Parameters
        motion_group = QGroupBox("Motion Correction")
        motion_layout = QFormLayout()
        motion_layout.setSpacing(10)
        motion_layout.setContentsMargins(10, 10, 10, 10)

        # Binning
        self.main_window.mc_bin = QDoubleSpinBox()
        self.main_window.mc_bin.setRange(0.5, 8)
        self.main_window.mc_bin.setValue(1)
        self.main_window.mc_bin.setSingleStep(0.5)
        self.main_window.mc_bin.setFixedWidth(80)
        motion_layout.addRow("Binning:", self.main_window.mc_bin)

        # Patch Size
        patch_widget = QWidget()
        patch_layout = QHBoxLayout(patch_widget)
        patch_layout.setContentsMargins(0, 0, 0, 0)
        patch_layout.setSpacing(3)

        self.main_window.mc_patch_x = QSpinBox()
        self.main_window.mc_patch_y = QSpinBox()
        for spin in (self.main_window.mc_patch_x, self.main_window.mc_patch_y):
            spin.setRange(1, 10)
            spin.setValue(1)
            spin.setFixedWidth(60)

        patch_layout.addWidget(self.main_window.mc_patch_x)
        patch_layout.addWidget(QLabel("×"))
        patch_layout.addWidget(self.main_window.mc_patch_y)
        patch_layout.addStretch()
        motion_layout.addRow("Patch Size:", patch_widget)

        # Frame Interval
        self.main_window.fm_int = QSpinBox()
        self.main_window.fm_int.setRange(1, 50)
        self.main_window.fm_int.setValue(12)
        self.main_window.fm_int.setFixedWidth(80)
        motion_layout.addRow("Frame Interval:", self.main_window.fm_int)

        motion_group.setLayout(motion_layout)
        processing_layout.addWidget(motion_group)

        # Tomogram Parameters
        tomo_group = QGroupBox("Tomogram Generation")
        tomo_layout = QVBoxLayout()
        tomo_layout.setSpacing(10)
        tomo_layout.setContentsMargins(10, 10, 10, 10)

        # Alignment Binning
        bin_form = QFormLayout()
        bin_form.setSpacing(10)
        self.main_window.at_bin = QDoubleSpinBox()
        self.main_window.at_bin.setRange(1, 8)
        self.main_window.at_bin.setValue(4)
        self.main_window.at_bin.setSingleStep(0.5)
        self.main_window.at_bin.setFixedWidth(80)
        bin_form.addRow("Alignment Binning:", self.main_window.at_bin)
        tomo_layout.addLayout(bin_form)

        # Options grid
        options_widget = QWidget()
        options_grid = QGridLayout(options_widget)
        options_grid.setContentsMargins(0, 5, 0, 5)
        options_grid.setSpacing(10)
        options_grid.setHorizontalSpacing(20)

        self.main_window.out_xf = QCheckBox("Output XF")
        self.main_window.out_imod = QCheckBox("Output IMOD")
        self.main_window.wbp = QCheckBox("WBP")
        self.main_window.tilt_cor = QCheckBox("Tilt Correction")

        for cb in (self.main_window.out_xf, self.main_window.out_imod, 
                   self.main_window.wbp, self.main_window.tilt_cor):
            cb.setChecked(True)

        options_grid.addWidget(self.main_window.out_xf, 0, 0)
        options_grid.addWidget(self.main_window.out_imod, 0, 1)
        options_grid.addWidget(self.main_window.wbp, 1, 0)
        options_grid.addWidget(self.main_window.tilt_cor, 1, 1)

        tomo_layout.addWidget(options_widget)
        tomo_group.setLayout(tomo_layout)
        processing_layout.addWidget(tomo_group)

        # Make both groups expand equally
        processing_layout.setStretchFactor(motion_group, 1)
        processing_layout.setStretchFactor(tomo_group, 1)

        layout.addWidget(processing_container)

    def _setup_processing_options(self, layout: QVBoxLayout, parent: QWidget) -> None:
        """Setup processing options section."""
        options_group = QGroupBox("Processing Options")
        options_layout = QHBoxLayout()
        options_layout.setSpacing(20)
        options_layout.setContentsMargins(10, 10, 10, 10)

        self.main_window.flip_gain = QCheckBox("Flip Gain")
        self.main_window.flip_gain.setChecked(True)
        self.main_window.flip_volume = QCheckBox("Flip Volume")
        self.main_window.flip_volume.setChecked(True)
        self.main_window.correct_ctf = QCheckBox("Correct CTF")
        self.main_window.correct_ctf.setChecked(True)

        options_layout.addWidget(self.main_window.flip_gain)
        options_layout.addWidget(self.main_window.flip_volume)
        options_layout.addWidget(self.main_window.correct_ctf)
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
