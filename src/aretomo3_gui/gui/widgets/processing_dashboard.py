#!/usr/bin/env python3
"""
Real-time Processing Dashboard for AT3GUI
Provides live processing statistics, GPU/CPU utilization, and ETA calculations.
"""

import sys
import time
import psutil
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                          QProgressBar, QGroupBox, QGridLayout, QFrame,
                          QScrollArea, QSplitter, QTabWidget)
from PyQt6.QtCore import QTimer, Qt, pyqtSignal, QThread, QMutex
from PyQt6.QtGui import QFont, QPalette, QColor
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from collections import deque
import logging

logger = logging.getLogger(__name__)

class SystemMonitorThread(QThread):
    """Background thread for system monitoring."""
    
    stats_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.mutex = QMutex()
        
    def run(self):
        self.running = True
        # Initialize CPU monitoring for non-blocking usage
        psutil.cpu_percent()  # Call once to initialize
        while self.running:
            try:
                # Collect system statistics (non-blocking)
                stats = {
                    'cpu_percent': psutil.cpu_percent(interval=None),  # Non-blocking
                    'memory_percent': psutil.virtual_memory().percent,
                    'memory_used': psutil.virtual_memory().used / (1024**3),  # GB
                    'memory_total': psutil.virtual_memory().total / (1024**3),  # GB
                    'disk_usage': psutil.disk_usage('/').percent,
                    'timestamp': time.time()
                }
                
                # Try to get GPU stats (if available)
                try:
                    import GPUtil
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu = gpus[0]  # Use first GPU
                        stats.update({
                            'gpu_percent': gpu.load * 100,
                            'gpu_memory_percent': gpu.memoryUtil * 100,
                            'gpu_memory_used': gpu.memoryUsed / 1024,  # GB
                            'gpu_memory_total': gpu.memoryTotal / 1024,  # GB
                            'gpu_temperature': gpu.temperature
                        })
                except ImportError:
                    # GPU monitoring not available
                    stats.update({
                        'gpu_percent': 0,
                        'gpu_memory_percent': 0,
                        'gpu_memory_used': 0,
                        'gpu_memory_total': 0,
                        'gpu_temperature': 0
                    })
                
                self.stats_updated.emit(stats)
                self.msleep(1000)  # Update every second
                
            except Exception as e:
                logger.error(f"Error in system monitoring: {e}")
                self.msleep(5000)  # Wait longer on error
                
    def stop(self):
        self.running = False
        self.wait()

class LiveGraphWidget(QWidget):
    """Widget for displaying live graphs."""
    
    def __init__(self, title, ylabel, color='#2196F3', max_points=60):
        super().__init__()
        self.title = title
        self.ylabel = ylabel
        self.color = color
        self.max_points = max_points
        self.data = deque(maxlen=max_points)
        self.timestamps = deque(maxlen=max_points)
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Create matplotlib figure
        self.figure = Figure(figsize=(6, 3), facecolor='white')
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        
        # Style the plot
        self.ax.set_facecolor('#f8f9fa')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_title(self.title, fontsize=10, fontweight='bold')
        self.ax.set_ylabel(self.ylabel, fontsize=9)
        
        # Set up the line plot
        self.line, = self.ax.plot([], [], color=self.color, linewidth=2)
        self.ax.set_ylim(0, 100)
        
        layout.addWidget(self.canvas)
        
    def add_data_point(self, value, timestamp=None):
        if timestamp is None:
            timestamp = time.time()
            
        self.data.append(value)
        self.timestamps.append(timestamp)
        self.update_plot()
        
    def update_plot(self):
        if len(self.data) < 2:
            return
            
        # Convert timestamps to relative seconds
        current_time = self.timestamps[-1]
        relative_times = [(t - current_time) for t in self.timestamps]
        
        # Update the line data
        self.line.set_data(relative_times, list(self.data))
        
        # Update axes
        self.ax.set_xlim(min(relative_times), 0)
        self.ax.set_ylim(0, max(100, max(self.data) * 1.1))
        
        # Update x-axis labels to show time
        self.ax.set_xlabel('Time (seconds ago)', fontsize=9)
        
        self.figure.tight_layout()
        self.canvas.draw()

class ProcessingStatsWidget(QWidget):
    """Widget for displaying processing statistics."""
    
    def __init__(self):
        super().__init__()
        self.start_time = None
        self.processed_files = 0
        self.total_files = 0
        self.current_file = ""
        self.processing_speed = 0.0
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Processing Status Group
        status_group = QGroupBox("Processing Status")
        status_layout = QGridLayout(status_group)
        
        # Current file
        self.current_file_label = QLabel("No active processing")
        self.current_file_label.setStyleSheet("font-weight: bold; color: #2e7d32;")
        status_layout.addWidget(QLabel("Current File:"), 0, 0)
        status_layout.addWidget(self.current_file_label, 0, 1)
        
        # Progress
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        status_layout.addWidget(QLabel("Progress:"), 1, 0)
        status_layout.addWidget(self.progress_bar, 1, 1)
        
        # Files processed
        self.files_label = QLabel("0 / 0 files")
        status_layout.addWidget(QLabel("Files:"), 2, 0)
        status_layout.addWidget(self.files_label, 2, 1)
        
        layout.addWidget(status_group)
        
        # Timing Information Group
        timing_group = QGroupBox("Timing Information")
        timing_layout = QGridLayout(timing_group)
        
        # Elapsed time
        self.elapsed_label = QLabel("00:00:00")
        timing_layout.addWidget(QLabel("Elapsed:"), 0, 0)
        timing_layout.addWidget(self.elapsed_label, 0, 1)
        
        # ETA
        self.eta_label = QLabel("N/A")
        timing_layout.addWidget(QLabel("ETA:"), 1, 0)
        timing_layout.addWidget(self.eta_label, 1, 1)
        
        # Processing speed
        self.speed_label = QLabel("0.0 files/min")
        timing_layout.addWidget(QLabel("Speed:"), 2, 0)
        timing_layout.addWidget(self.speed_label, 2, 1)
        
        layout.addWidget(timing_group)
        
    def start_processing(self, total_files):
        """Start processing with given total files."""
        self.start_time = time.time()
        self.processed_files = 0
        self.total_files = total_files
        self.update_display()
        
    def update_progress(self, current_file, processed_files):
        """Update processing progress."""
        self.current_file = current_file
        self.processed_files = processed_files
        self.update_display()
        
    def update_display(self):
        """Update all display elements."""
        # Update current file
        if self.current_file:
            self.current_file_label.setText(self.current_file)
        
        # Update progress bar
        if self.total_files > 0:
            progress = int((self.processed_files / self.total_files) * 100)
            self.progress_bar.setValue(progress)
        
        # Update files count
        self.files_label.setText(f"{self.processed_files} / {self.total_files} files")
        
        # Update timing information
        if self.start_time:
            elapsed = time.time() - self.start_time
            self.elapsed_label.setText(self.format_time(elapsed))
            
            # Calculate processing speed and ETA
            if self.processed_files > 0 and elapsed > 0:
                self.processing_speed = (self.processed_files / elapsed) * 60  # files per minute
                self.speed_label.setText(f"{self.processing_speed:.1f} files/min")
                
                # Calculate ETA
                remaining_files = self.total_files - self.processed_files
                if self.processing_speed > 0:
                    eta_seconds = (remaining_files / self.processing_speed) * 60
                    self.eta_label.setText(self.format_time(eta_seconds))
                else:
                    self.eta_label.setText("Calculating...")
            else:
                self.speed_label.setText("0.0 files/min")
                self.eta_label.setText("N/A")
    
    def format_time(self, seconds):
        """Format seconds into HH:MM:SS."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

class ProcessingDashboard(QWidget):
    """Main processing dashboard widget."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitor_thread = None
        self.setup_ui()
        self.start_monitoring()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - System monitoring graphs
        graphs_widget = QWidget()
        graphs_layout = QVBoxLayout(graphs_widget)
        
        # CPU and Memory graphs
        self.cpu_graph = LiveGraphWidget("CPU Usage", "CPU %", '#FF5722')
        self.memory_graph = LiveGraphWidget("Memory Usage", "Memory %", '#4CAF50')
        self.gpu_graph = LiveGraphWidget("GPU Usage", "GPU %", '#2196F3')
        
        graphs_layout.addWidget(self.cpu_graph)
        graphs_layout.addWidget(self.memory_graph)
        graphs_layout.addWidget(self.gpu_graph)
        
        main_splitter.addWidget(graphs_widget)
        
        # Right side - Processing statistics
        self.processing_stats = ProcessingStatsWidget()
        main_splitter.addWidget(self.processing_stats)
        
        # Set splitter proportions (70% graphs, 30% stats)
        main_splitter.setStretchFactor(0, 7)
        main_splitter.setStretchFactor(1, 3)
        
        layout.addWidget(main_splitter)
        
    def start_monitoring(self):
        """Start system monitoring."""
        if self.monitor_thread is None:
            self.monitor_thread = SystemMonitorThread()
            self.monitor_thread.stats_updated.connect(self.update_system_stats)
            self.monitor_thread.start()
            
    def stop_monitoring(self):
        """Stop system monitoring."""
        if self.monitor_thread:
            self.monitor_thread.stop()
            self.monitor_thread = None
            
    def update_system_stats(self, stats):
        """Update system statistics displays."""
        timestamp = stats['timestamp']
        
        # Update graphs
        self.cpu_graph.add_data_point(stats['cpu_percent'], timestamp)
        self.memory_graph.add_data_point(stats['memory_percent'], timestamp)
        self.gpu_graph.add_data_point(stats['gpu_percent'], timestamp)
        
    def start_processing(self, total_files):
        """Start processing session."""
        self.processing_stats.start_processing(total_files)
        
    def update_processing_progress(self, current_file, processed_files):
        """Update processing progress."""
        self.processing_stats.update_progress(current_file, processed_files)
        
    def closeEvent(self, event):
        """Clean up when widget is closed."""
        self.stop_monitoring()
        super().closeEvent(event)
