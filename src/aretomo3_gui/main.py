#!/usr/bin/env python3
"""
Main entry point for AreTomo3 GUI application.
"""

import sys
import os
import logging
import signal
from pathlib import Path
import argparse

def setup_logging(debug=False):
    """Set up logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []

    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")

    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")

    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")

    try:
        import mrcfile
    except ImportError:
        missing_deps.append("mrcfile")

    if missing_deps:
        print(f"❌ Missing required dependencies: {', '.join(missing_deps)}")
        print("Please install them with: pip install aretomo3-gui")
        return False

    return True

def check_eer_support():
    """Check EER support status - EER support has been removed."""
    print("ℹ️  EER support has been removed from AT3Gui")
    print("   Please convert EER files to MRC format using external tools")

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="AreTomo3 GUI - A comprehensive GUI for tomographic reconstruction",
        prog="aretomo3-gui"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    parser.add_argument(
        "--check-eer",
        action="store_true",
        help="Check EER support and exit"
    )
    parser.add_argument(
        "--version",
        action="version",
        version="AreTomo3 GUI v1.0.0"
    )
    parser.add_argument(
        "files",
        nargs="*",
        help="Files to open on startup"
    )

    args = parser.parse_args()

    # Set up logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)

    logger.info("Starting AreTomo3 GUI...")

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check EER support if requested
    if args.check_eer:
        check_eer_support()
        return

    try:
        # Import GUI components
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt

        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("AreTomo3 GUI")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("AreTomo3 GUI Development Team")

        # Enable high DPI support (not needed in PyQt6, removed for compatibility)
        # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)

        # Check EER support
        check_eer_support()

        # Create and show main window
        from aretomo3_gui.gui.main_window import AreTomoGUI
        main_window = AreTomoGUI()

        # Set up signal handlers for graceful shutdown
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down gracefully...")
            try:
                main_window.close()
            except:
                pass
            app.quit()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Load files if provided
        if args.files:
            for file_path in args.files:
                if os.path.exists(file_path):
                    logger.info(f"Loading file: {file_path}")
                    # TODO: Implement file loading
                else:
                    logger.warning(f"File not found: {file_path}")

        main_window.show()

        # Run application
        sys.exit(app.exec())

    except ImportError as e:
        logger.error(f"Failed to import GUI components: {e}")
        print("❌ GUI components could not be loaded.")
        print("Please ensure PyQt6 is properly installed.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ An unexpected error occurred: {e}")
        sys.exit(1)

# Expose AreTomoGUI for import
try:
    from aretomo3_gui.gui.main_window import AreTomoGUI as _AreTomoGUI
    AreTomoGUI = _AreTomoGUI
except ImportError:
    AreTomoGUI = None

__all__ = ['main', 'AreTomoGUI']

if __name__ == "__main__":
    main()
