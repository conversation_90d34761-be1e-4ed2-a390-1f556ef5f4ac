#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Security Framework
Comprehensive security management for data protection and access control.
"""

import logging
import hashlib
import secrets
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import base64
import os

# Cryptography imports with fallbacks
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

try:
    import keyring
    KEYRING_AVAILABLE = True
except ImportError:
    KEYRING_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    encryption_enabled: bool
    access_logging: bool
    session_timeout: int  # minutes
    max_failed_attempts: int
    require_authentication: bool
    data_retention_days: int
    backup_encryption: bool
    network_security: bool


@dataclass
class UserSession:
    """User session information."""
    session_id: str
    user_id: str
    created_at: datetime
    last_activity: datetime
    ip_address: str
    permissions: List[str]
    is_active: bool


@dataclass
class SecurityEvent:
    """Security event log entry."""
    event_id: str
    event_type: str
    timestamp: datetime
    user_id: str
    ip_address: str
    description: str
    severity: str  # low, medium, high, critical
    data: Dict[str, Any]


class SecurityManager:
    """
    Advanced security manager for AreTomo3 GUI.
    Handles encryption, authentication, access control, and security monitoring.
    """
    
    def __init__(self, config_dir: Path = None):
        """Initialize the security manager."""
        self.config_dir = config_dir or Path.home() / ".aretomo3_gui" / "security"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Security configuration
        self.security_policy = SecurityPolicy(
            encryption_enabled=True,
            access_logging=True,
            session_timeout=60,  # 1 hour
            max_failed_attempts=3,
            require_authentication=False,  # Default off for ease of use
            data_retention_days=90,
            backup_encryption=True,
            network_security=True
        )
        
        # Session management
        self.active_sessions: Dict[str, UserSession] = {}
        self.failed_attempts: Dict[str, int] = {}
        
        # Security event logging
        self.security_events: List[SecurityEvent] = []
        self.event_log_file = self.config_dir / "security_events.log"
        
        # Encryption
        self.encryption_key = None
        self.cipher_suite = None
        
        # Configuration files
        self.config_file = self.config_dir / "security_config.json"
        self.key_file = self.config_dir / "encryption.key"
        
        # Initialize security components
        self._load_config()
        self._initialize_encryption()
        
        logger.info("Security Manager initialized")
    
    def _load_config(self):
        """Load security configuration."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update security policy
                for key, value in config_data.get('security_policy', {}).items():
                    if hasattr(self.security_policy, key):
                        setattr(self.security_policy, key, value)
                
                logger.info("Security configuration loaded")
        
        except Exception as e:
            logger.error(f"Error loading security config: {e}")
    
    def _save_config(self):
        """Save security configuration."""
        try:
            config_data = {
                'security_policy': asdict(self.security_policy),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving security config: {e}")
    
    def _initialize_encryption(self):
        """Initialize encryption system."""
        if not CRYPTOGRAPHY_AVAILABLE:
            logger.warning("Cryptography library not available - encryption disabled")
            self.security_policy.encryption_enabled = False
            return
        
        try:
            if self.key_file.exists():
                # Load existing key
                with open(self.key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Generate new key
                self.encryption_key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(self.encryption_key)
                
                # Secure the key file
                os.chmod(self.key_file, 0o600)
            
            self.cipher_suite = Fernet(self.encryption_key)
            logger.info("Encryption system initialized")
            
        except Exception as e:
            logger.error(f"Error initializing encryption: {e}")
            self.security_policy.encryption_enabled = False
    
    def encrypt_data(self, data: Union[str, bytes]) -> Optional[bytes]:
        """Encrypt sensitive data."""
        if not self.security_policy.encryption_enabled or not self.cipher_suite:
            return data.encode() if isinstance(data, str) else data
        
        try:
            if isinstance(data, str):
                data = data.encode()
            
            encrypted_data = self.cipher_suite.encrypt(data)
            self._log_security_event("data_encrypted", "Data encryption performed", "low")
            
            return encrypted_data
            
        except Exception as e:
            logger.error(f"Error encrypting data: {e}")
            self._log_security_event("encryption_failed", f"Data encryption failed: {e}", "medium")
            return None
    
    def decrypt_data(self, encrypted_data: bytes) -> Optional[bytes]:
        """Decrypt sensitive data."""
        if not self.security_policy.encryption_enabled or not self.cipher_suite:
            return encrypted_data
        
        try:
            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            self._log_security_event("data_decrypted", "Data decryption performed", "low")
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"Error decrypting data: {e}")
            self._log_security_event("decryption_failed", f"Data decryption failed: {e}", "medium")
            return None
    
    def create_session(self, user_id: str, ip_address: str = "localhost", 
                      permissions: List[str] = None) -> Optional[str]:
        """Create a new user session."""
        try:
            session_id = secrets.token_urlsafe(32)
            
            session = UserSession(
                session_id=session_id,
                user_id=user_id,
                created_at=datetime.now(),
                last_activity=datetime.now(),
                ip_address=ip_address,
                permissions=permissions or ["read", "write"],
                is_active=True
            )
            
            self.active_sessions[session_id] = session
            
            self._log_security_event(
                "session_created",
                f"User session created for {user_id}",
                "low",
                {"user_id": user_id, "ip_address": ip_address}
            )
            
            logger.info(f"Session created for user: {user_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return None
    
    def validate_session(self, session_id: str) -> bool:
        """Validate user session."""
        try:
            if session_id not in self.active_sessions:
                self._log_security_event("invalid_session", f"Invalid session access: {session_id}", "medium")
                return False
            
            session = self.active_sessions[session_id]
            
            # Check if session is active
            if not session.is_active:
                return False
            
            # Check session timeout
            if self.security_policy.session_timeout > 0:
                timeout_delta = timedelta(minutes=self.security_policy.session_timeout)
                if datetime.now() - session.last_activity > timeout_delta:
                    self.invalidate_session(session_id)
                    return False
            
            # Update last activity
            session.last_activity = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return False
    
    def invalidate_session(self, session_id: str) -> bool:
        """Invalidate user session."""
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                session.is_active = False
                
                self._log_security_event(
                    "session_invalidated",
                    f"Session invalidated for user {session.user_id}",
                    "low",
                    {"user_id": session.user_id, "session_id": session_id}
                )
                
                del self.active_sessions[session_id]
                logger.info(f"Session invalidated: {session_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error invalidating session: {e}")
            return False
    
    def check_permissions(self, session_id: str, required_permission: str) -> bool:
        """Check if session has required permission."""
        try:
            if not self.validate_session(session_id):
                return False
            
            session = self.active_sessions[session_id]
            has_permission = required_permission in session.permissions or "admin" in session.permissions
            
            if not has_permission:
                self._log_security_event(
                    "permission_denied",
                    f"Permission denied for {session.user_id}: {required_permission}",
                    "medium",
                    {"user_id": session.user_id, "permission": required_permission}
                )
            
            return has_permission
            
        except Exception as e:
            logger.error(f"Error checking permissions: {e}")
            return False
    
    def secure_file_storage(self, file_path: Path, data: bytes) -> bool:
        """Securely store file with encryption if enabled."""
        try:
            # Encrypt data if encryption is enabled
            if self.security_policy.encryption_enabled:
                data = self.encrypt_data(data)
                if data is None:
                    return False
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(file_path, 'wb') as f:
                f.write(data)
            
            # Set secure permissions
            os.chmod(file_path, 0o600)
            
            self._log_security_event(
                "file_stored",
                f"File securely stored: {file_path.name}",
                "low",
                {"file_path": str(file_path)}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error in secure file storage: {e}")
            self._log_security_event("file_storage_failed", f"Secure file storage failed: {e}", "medium")
            return False
    
    def secure_file_retrieval(self, file_path: Path) -> Optional[bytes]:
        """Securely retrieve file with decryption if needed."""
        try:
            if not file_path.exists():
                return None
            
            # Read file
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # Decrypt data if encryption is enabled
            if self.security_policy.encryption_enabled:
                data = self.decrypt_data(data)
            
            self._log_security_event(
                "file_retrieved",
                f"File securely retrieved: {file_path.name}",
                "low",
                {"file_path": str(file_path)}
            )
            
            return data
            
        except Exception as e:
            logger.error(f"Error in secure file retrieval: {e}")
            self._log_security_event("file_retrieval_failed", f"Secure file retrieval failed: {e}", "medium")
            return None
    
    def _log_security_event(self, event_type: str, description: str, severity: str, 
                          data: Dict[str, Any] = None):
        """Log security event."""
        try:
            event = SecurityEvent(
                event_id=secrets.token_hex(8),
                event_type=event_type,
                timestamp=datetime.now(),
                user_id="system",  # Default to system
                ip_address="localhost",
                description=description,
                severity=severity,
                data=data or {}
            )
            
            self.security_events.append(event)
            
            # Write to log file if access logging is enabled
            if self.security_policy.access_logging:
                with open(self.event_log_file, 'a') as f:
                    log_entry = {
                        'timestamp': event.timestamp.isoformat(),
                        'event_type': event.event_type,
                        'severity': event.severity,
                        'description': event.description,
                        'data': event.data
                    }
                    f.write(json.dumps(log_entry) + '\n')
            
            # Log critical events immediately
            if severity == "critical":
                logger.critical(f"SECURITY ALERT: {description}")
            elif severity == "high":
                logger.warning(f"Security event: {description}")
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get current security status."""
        return {
            'encryption_enabled': self.security_policy.encryption_enabled,
            'authentication_required': self.security_policy.require_authentication,
            'active_sessions': len(self.active_sessions),
            'security_events_count': len(self.security_events),
            'last_event': self.security_events[-1].timestamp.isoformat() if self.security_events else None,
            'capabilities': {
                'cryptography': CRYPTOGRAPHY_AVAILABLE,
                'keyring': KEYRING_AVAILABLE
            }
        }
    
    def cleanup_old_events(self):
        """Clean up old security events based on retention policy."""
        try:
            if self.security_policy.data_retention_days <= 0:
                return
            
            cutoff_date = datetime.now() - timedelta(days=self.security_policy.data_retention_days)
            
            # Remove old events
            self.security_events = [
                event for event in self.security_events 
                if event.timestamp > cutoff_date
            ]
            
            logger.info(f"Cleaned up security events older than {self.security_policy.data_retention_days} days")
            
        except Exception as e:
            logger.error(f"Error cleaning up old events: {e}")
    
    def update_security_policy(self, policy_updates: Dict[str, Any]) -> bool:
        """Update security policy."""
        try:
            for key, value in policy_updates.items():
                if hasattr(self.security_policy, key):
                    setattr(self.security_policy, key, value)
            
            self._save_config()
            
            self._log_security_event(
                "policy_updated",
                "Security policy updated",
                "medium",
                {"updates": policy_updates}
            )
            
            logger.info("Security policy updated")
            return True
            
        except Exception as e:
            logger.error(f"Error updating security policy: {e}")
            return False


# Global security manager instance
security_manager = SecurityManager()


def encrypt_sensitive_data(data: Union[str, bytes]) -> Optional[bytes]:
    """Convenience function to encrypt sensitive data."""
    return security_manager.encrypt_data(data)


def decrypt_sensitive_data(encrypted_data: bytes) -> Optional[bytes]:
    """Convenience function to decrypt sensitive data."""
    return security_manager.decrypt_data(encrypted_data)
