#!/usr/bin/env python3
"""
Integration with AreTomo3's KmeanMetrics tool for dataset classification.
"""

import os
import subprocess
import tempfile
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import pandas as pd
import numpy as np

from PyQt6.QtCore import QObject, pyqtSignal, QThread
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
    QPushButton, QSpinBox, QTableWidget, QTableWidgetItem,
    QProgressBar, QTextEdit, QFileDialog, QMessageBox,
    QComboBox, QCheckBox
)

logger = logging.getLogger(__name__)


class KmeansWorker(QThread):
    """Worker thread for running KmeanMetrics analysis."""
    
    progress_updated = pyqtSignal(str)
    analysis_completed = pyqtSignal(bool, str, dict)  # success, message, results
    
    def __init__(self, csv_file: str, num_classes: int, output_file: str):
        super().__init__()
        self.csv_file = csv_file
        self.num_classes = num_classes
        self.output_file = output_file
        self.aretomo3_path = None
    
    def set_aretomo3_path(self, path: str):
        """Set the path to AreTomo3 installation."""
        self.aretomo3_path = path
    
    def run(self):
        """Run the KmeanMetrics analysis."""
        try:
            # Find KmeanMetrics.py script
            kmeans_script = self._find_kmeans_script()
            if not kmeans_script:
                self.analysis_completed.emit(
                    False, "KmeanMetrics.py script not found", {}
                )
                return
            
            self.progress_updated.emit("Starting KmeanMetrics analysis...")
            
            # Build command
            cmd = [
                "python", kmeans_script,
                "--in_csv", self.csv_file,
                "--num_classes", str(self.num_classes),
                "--out_csv", self.output_file
            ]
            
            self.progress_updated.emit(f"Running: {' '.join(cmd)}")
            
            # Run the analysis
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=300
            )
            
            if result.returncode == 0:
                # Parse results
                results = self._parse_results()
                self.analysis_completed.emit(
                    True, "Analysis completed successfully", results
                )
            else:
                error_msg = result.stderr or "Unknown error occurred"
                self.analysis_completed.emit(
                    False, f"Analysis failed: {error_msg}", {}
                )
                
        except subprocess.TimeoutExpired:
            self.analysis_completed.emit(
                False, "Analysis timed out", {}
            )
        except Exception as e:
            self.analysis_completed.emit(
                False, f"Error running analysis: {str(e)}", {}
            )
    
    def _find_kmeans_script(self) -> Optional[str]:
        """Find the KmeanMetrics.py script."""
        # Try different possible locations
        search_paths = []
        
        if self.aretomo3_path:
            # Look in AreTomo3 installation directory
            aretomo3_dir = Path(self.aretomo3_path).parent
            search_paths.extend([
                aretomo3_dir / "tools" / "KmeanMetrics.py",
                aretomo3_dir / "KmeanMetrics.py"
            ])
        
        # Look in current project
        current_dir = Path(__file__).parent.parent.parent
        search_paths.extend([
            current_dir / "AreTomo3-main" / "tools" / "KmeanMetrics.py",
            current_dir / "tools" / "KmeanMetrics.py"
        ])
        
        for path in search_paths:
            if path.exists():
                return str(path)
        
        return None
    
    def _parse_results(self) -> Dict:
        """Parse the analysis results."""
        results = {}
        
        try:
            if os.path.exists(self.output_file):
                df = pd.read_csv(self.output_file)
                results['classifications'] = df.to_dict('records')
                results['class_counts'] = df['Class'].value_counts().to_dict()
                results['total_series'] = len(df)
        except Exception as e:
            logger.error(f"Error parsing results: {e}")
        
        return results


class KmeansMetricsIntegration(QWidget):
    """Widget for integrating AreTomo3's KmeanMetrics tool."""
    
    analysis_completed = pyqtSignal(dict)  # results
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.worker = None
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("Dataset Classification with KmeanMetrics")
        header.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(header)
        
        # Input section
        input_group = QGroupBox("Input Settings")
        input_layout = QVBoxLayout(input_group)
        
        # CSV file selection
        csv_layout = QHBoxLayout()
        csv_layout.addWidget(QLabel("Metrics CSV File:"))
        self.csv_file_edit = QLineEdit()
        self.csv_file_edit.setPlaceholderText("Select AreTomo3 metrics CSV file...")
        csv_layout.addWidget(self.csv_file_edit)
        
        csv_browse_btn = QPushButton("Browse")
        csv_browse_btn.clicked.connect(self.browse_csv_file)
        csv_layout.addWidget(csv_browse_btn)
        input_layout.addLayout(csv_layout)
        
        # Number of classes
        classes_layout = QHBoxLayout()
        classes_layout.addWidget(QLabel("Number of Classes:"))
        self.num_classes_spin = QSpinBox()
        self.num_classes_spin.setRange(2, 10)
        self.num_classes_spin.setValue(3)
        self.num_classes_spin.setToolTip("Number of classes for K-means clustering")
        classes_layout.addWidget(self.num_classes_spin)
        classes_layout.addStretch()
        input_layout.addLayout(classes_layout)
        
        # Output file
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("Output File:"))
        self.output_file_edit = QLineEdit()
        self.output_file_edit.setPlaceholderText("Classification results will be saved here...")
        output_layout.addWidget(self.output_file_edit)
        
        output_browse_btn = QPushButton("Browse")
        output_browse_btn.clicked.connect(self.browse_output_file)
        output_layout.addWidget(output_browse_btn)
        input_layout.addLayout(output_layout)
        
        layout.addWidget(input_group)
        
        # Analysis controls
        controls_layout = QHBoxLayout()
        
        self.analyze_btn = QPushButton("Run Analysis")
        self.analyze_btn.clicked.connect(self.run_analysis)
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        controls_layout.addWidget(self.analyze_btn)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Progress and results
        self.progress_text = QTextEdit()
        self.progress_text.setMaximumHeight(100)
        self.progress_text.setReadOnly(True)
        layout.addWidget(self.progress_text)
        
        # Results section
        results_group = QGroupBox("Classification Results")
        results_layout = QVBoxLayout(results_group)
        
        # Summary
        self.summary_label = QLabel("No analysis performed yet")
        results_layout.addWidget(self.summary_label)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setAlternatingRowColors(True)
        results_layout.addWidget(self.results_table)
        
        # Export options
        export_layout = QHBoxLayout()
        
        export_btn = QPushButton("Export Results")
        export_btn.clicked.connect(self.export_results)
        export_layout.addWidget(export_btn)
        
        visualize_btn = QPushButton("Visualize Classes")
        visualize_btn.clicked.connect(self.visualize_classes)
        export_layout.addWidget(visualize_btn)
        
        export_layout.addStretch()
        results_layout.addLayout(export_layout)
        
        layout.addWidget(results_group)
    
    def browse_csv_file(self):
        """Browse for input CSV file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Metrics CSV File", "",
            "CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.csv_file_edit.setText(file_path)
            # Auto-generate output filename
            output_path = file_path.replace('.csv', '_classified.csv')
            self.output_file_edit.setText(output_path)
    
    def browse_output_file(self):
        """Browse for output file location."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Classification Results", "",
            "CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.output_file_edit.setText(file_path)
    
    def run_analysis(self):
        """Run the KmeanMetrics analysis."""
        # Validate inputs
        csv_file = self.csv_file_edit.text().strip()
        output_file = self.output_file_edit.text().strip()
        
        if not csv_file or not os.path.exists(csv_file):
            QMessageBox.warning(
                self, "Invalid Input",
                "Please select a valid metrics CSV file."
            )
            return
        
        if not output_file:
            QMessageBox.warning(
                self, "Invalid Output",
                "Please specify an output file location."
            )
            return
        
        # Disable controls
        self.analyze_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Clear previous results
        self.progress_text.clear()
        self.results_table.clear()
        
        # Start analysis
        self.worker = KmeansWorker(
            csv_file, self.num_classes_spin.value(), output_file
        )
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.analysis_completed.connect(self.on_analysis_completed)
        self.worker.start()
    
    def update_progress(self, message: str):
        """Update progress display."""
        self.progress_text.append(message)
        self.progress_text.ensureCursorVisible()
    
    def on_analysis_completed(self, success: bool, message: str, results: Dict):
        """Handle analysis completion."""
        # Re-enable controls
        self.analyze_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            self.update_progress(f"✓ {message}")
            self.display_results(results)
            self.analysis_completed.emit(results)
        else:
            self.update_progress(f"✗ {message}")
            QMessageBox.critical(
                self, "Analysis Failed", message
            )
    
    def display_results(self, results: Dict):
        """Display analysis results."""
        if not results:
            return
        
        # Update summary
        total_series = results.get('total_series', 0)
        class_counts = results.get('class_counts', {})
        
        summary_text = f"Classified {total_series} tilt series into {len(class_counts)} classes:\n"
        for class_id, count in class_counts.items():
            percentage = (count / total_series * 100) if total_series > 0 else 0
            summary_text += f"  Class {class_id}: {count} series ({percentage:.1f}%)\n"
        
        self.summary_label.setText(summary_text)
        
        # Update table
        classifications = results.get('classifications', [])
        if classifications:
            self.results_table.setRowCount(len(classifications))
            self.results_table.setColumnCount(2)
            self.results_table.setHorizontalHeaderLabels(['Tilt Series', 'Class'])
            
            for row, item in enumerate(classifications):
                self.results_table.setItem(
                    row, 0, QTableWidgetItem(str(item.get('Tilt_Series', '')))
                )
                self.results_table.setItem(
                    row, 1, QTableWidgetItem(str(item.get('Class', '')))
                )
            
            self.results_table.resizeColumnsToContents()
    
    def export_results(self):
        """Export classification results."""
        # TODO: Implement results export
        QMessageBox.information(
            self, "Export Results",
            "Results export functionality will be implemented."
        )
    
    def visualize_classes(self):
        """Visualize classification results."""
        # TODO: Implement visualization
        QMessageBox.information(
            self, "Visualize Classes",
            "Class visualization functionality will be implemented."
        )
    
    def set_aretomo3_path(self, path: str):
        """Set the AreTomo3 installation path."""
        if self.worker:
            self.worker.set_aretomo3_path(path)
