#!/bin/bash

# Purge and load required modules if available
if command -v ml &> /dev/null; then
    ml purge
    ml IMOD
    ml AreTomo3
fi

# Save original LD_LIBRARY_PATH
OLD_LD_LIBRARY_PATH=$LD_LIBRARY_PATH

# Filter out CryoSPARC paths from LD_LIBRARY_PATH
if [ ! -z "$LD_LIBRARY_PATH" ]; then
    export LD_LIBRARY_PATH=$(echo $LD_LIBRARY_PATH | tr ':' '\n' | grep -v "cryosparc" | tr '\n' ':' | sed 's/:$//')
fi

# Execute AreTomo3 with all arguments
# Keep the command string including quotes
command_str="$*"

# Execute the command as is, preserving quotes
eval "$command_str"
RESULT=$?

# Restore original LD_LIBRARY_PATH
export LD_LIBRARY_PATH=$OLD_LD_LIBRARY_PATH

# Exit with AreTomo3's exit code
exit $RESULT
