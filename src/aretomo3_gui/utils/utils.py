"""
Utility functions for AreTomo3 GUI.
"""
import os
from typing import Optional, Dict, Any
import json

from ..core.config.config import CACHE_DIR, GUI_SETTINGS

from pathlib import Path
from PyQt6.QtWidgets import QProgressDialog, QApplication
from PyQt6.QtCore import Qt
import matplotlib.pyplot as plt

class ProgressDialog:
    def __init__(self, title, message, min_val=0, max_val=100, parent=None):
        self.progress = QProgressDialog(message, "Cancel", min_val, max_val, parent)
        self.progress.setWindowTitle(title)
        self.progress.setWindowModality(Qt.WindowModality.WindowModal)
        self.progress.setMinimumDuration(500)  # Show after 500ms delay

    def update(self, value, message=None):
        self.progress.setValue(value)
        if message:
            self.progress.setLabelText(message)
        QApplication.processEvents()

    def close(self):
        self.progress.close()

def save_window_state(window):
    """Save window geometry and state"""
    state = {
        'geometry': bytes(window.saveGeometry()).hex(),
        'state': bytes(window.saveState()).hex()
    }
    with open(CACHE_DIR / 'window_state.json', 'w') as f:
        json.dump(state, f)

def load_window_state(window):
    """Load window geometry and state"""
    try:
        with open(CACHE_DIR / 'window_state.json', 'r') as f:
            state = json.load(f)
            window.restoreGeometry(bytes.fromhex(state['geometry']))
            window.restoreState(bytes.fromhex(state['state']))
    except (FileNotFoundError, json.JSONDecodeError, KeyError):
        pass

def apply_dark_mode(app):
    """Apply dark mode styling to the application"""
    if GUI_SETTINGS['dark_mode']:
        app.setStyle("Fusion")
        palette = app.palette()
        palette.setColor(palette.ColorRole.Window, Qt.GlobalColor.darkGray)
        palette.setColor(palette.ColorRole.WindowText, Qt.GlobalColor.white)
        palette.setColor(palette.ColorRole.Base, Qt.GlobalColor.darkGray)
        palette.setColor(palette.ColorRole.AlternateBase, Qt.GlobalColor.darkGray)
        palette.setColor(palette.ColorRole.ToolTipBase, Qt.GlobalColor.darkGray)
        palette.setColor(palette.ColorRole.ToolTipText, Qt.GlobalColor.white)
        palette.setColor(palette.ColorRole.Text, Qt.GlobalColor.white)
        palette.setColor(palette.ColorRole.Button, Qt.GlobalColor.darkGray)
        palette.setColor(palette.ColorRole.ButtonText, Qt.GlobalColor.white)
        palette.setColor(palette.ColorRole.BrightText, Qt.GlobalColor.red)
        palette.setColor(palette.ColorRole.Link, Qt.GlobalColor.cyan)
        palette.setColor(palette.ColorRole.Highlight, Qt.GlobalColor.blue)
        palette.setColor(palette.ColorRole.HighlightedText, Qt.GlobalColor.black)
        app.setPalette(palette)

        # Set dark style for matplotlib
        plt.style.use('dark_background')

def export_plot(figure, filename, dpi=300, format='png'):
    """Export a matplotlib figure to a file"""
    try:
        figure.savefig(filename, dpi=dpi, format=format, bbox_inches='tight')
        return True
    except Exception as e:
        print(f"Error exporting plot: {e}")
        return False

def format_file_size(size_bytes):
    """Format file size in human-readable format."""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024.0 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"

def format_duration(seconds):
    """Format duration in human-readable format."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def safe_filename(filename):
    """Create a safe filename by removing invalid characters."""
    import re
    # Remove invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    # Ensure it's not empty
    if not filename:
        filename = "untitled"
    return filename
