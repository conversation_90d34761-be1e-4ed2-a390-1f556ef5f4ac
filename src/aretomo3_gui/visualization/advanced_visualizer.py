#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Visualization Engine
Comprehensive visualization system for tomographic data, analysis results, and quality metrics.
"""

import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
import json
from datetime import datetime

# Import visualization libraries
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.colors import LinearSegmentedColormap
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import vispy
    from vispy import scene, app
    from vispy.color import Colormap
    VISPY_AVAILABLE = True
except ImportError:
    VISPY_AVAILABLE = False

logger = logging.getLogger(__name__)


class AdvancedVisualizer:
    """
    Advanced visualization engine for AreTomo3 GUI.
    Provides comprehensive visualization capabilities for tomographic data and analysis results.
    """
    
    def __init__(self, output_dir: Union[str, Path] = None):
        """Initialize the advanced visualizer."""
        self.output_dir = Path(output_dir) if output_dir else Path.cwd() / "visualizations"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Visualization themes
        self.themes = {
            'default': {
                'background': 'white',
                'grid': True,
                'colorscale': 'viridis',
                'font_family': 'Arial',
                'font_size': 12
            },
            'dark': {
                'background': '#2F2F2F',
                'grid': True,
                'colorscale': 'plasma',
                'font_family': 'Arial',
                'font_size': 12,
                'text_color': 'white'
            },
            'scientific': {
                'background': 'white',
                'grid': True,
                'colorscale': 'RdBu',
                'font_family': 'Times New Roman',
                'font_size': 10
            }
        }
        
        self.current_theme = 'default'
        
        # Check available libraries
        self.capabilities = {
            'plotly': PLOTLY_AVAILABLE,
            'matplotlib': MATPLOTLIB_AVAILABLE,
            'vispy': VISPY_AVAILABLE
        }
        
        logger.info(f"Advanced Visualizer initialized - Capabilities: {self.capabilities}")
    
    def create_ctf_analysis_dashboard(self, ctf_data: Dict[str, Any], 
                                    interactive: bool = True) -> str:
        """Create comprehensive CTF analysis dashboard."""
        if not PLOTLY_AVAILABLE and interactive:
            logger.warning("Plotly not available, falling back to matplotlib")
            interactive = False
        
        if interactive:
            return self._create_interactive_ctf_dashboard(ctf_data)
        else:
            return self._create_static_ctf_dashboard(ctf_data)
    
    def _create_interactive_ctf_dashboard(self, ctf_data: Dict[str, Any]) -> str:
        """Create interactive CTF analysis dashboard using Plotly."""
        try:
            # Create subplots
            fig = make_subplots(
                rows=2, cols=3,
                subplot_titles=[
                    'CTF Resolution vs Tilt Angle',
                    'Defocus Distribution',
                    'Astigmatism Analysis',
                    'Cross-Correlation Quality',
                    'CTF Fit Quality',
                    'Power Spectrum Gallery'
                ],
                specs=[
                    [{"type": "scatter"}, {"type": "histogram"}, {"type": "scatter"}],
                    [{"type": "scatter"}, {"type": "heatmap"}, {"type": "image"}]
                ]
            )
            
            # Extract data for plotting
            series_data = list(ctf_data.get('ctf_parameters', {}).values())[0]
            if 'parameters' not in series_data:
                return self._create_no_data_message("No CTF parameters available")
            
            df = series_data['parameters']
            
            # Plot 1: CTF Resolution vs Tilt Angle
            if 'tilt_angle' in df.columns and 'resolution_limit_A' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['tilt_angle'],
                        y=df['resolution_limit_A'],
                        mode='markers+lines',
                        name='Resolution',
                        marker=dict(
                            size=8,
                            color=df['cross_correlation'] if 'cross_correlation' in df.columns else 'blue',
                            colorscale='viridis',
                            showscale=True,
                            colorbar=dict(title="Cross Correlation")
                        )
                    ),
                    row=1, col=1
                )
            
            # Plot 2: Defocus Distribution
            if 'defocus1_A' in df.columns:
                defocus_um = df['defocus1_A'] / 10000  # Convert to micrometers
                fig.add_trace(
                    go.Histogram(
                        x=defocus_um,
                        nbinsx=20,
                        name='Defocus Distribution',
                        marker_color='lightblue'
                    ),
                    row=1, col=2
                )
            
            # Plot 3: Astigmatism Analysis
            if 'astigmatism_A' in df.columns and 'astigmatism_angle' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df['astigmatism_angle'],
                        y=df['astigmatism_A'],
                        mode='markers',
                        name='Astigmatism',
                        marker=dict(size=10, color='red', opacity=0.7)
                    ),
                    row=1, col=3
                )
            
            # Plot 4: Cross-Correlation Quality
            if 'cross_correlation' in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=list(range(len(df))),
                        y=df['cross_correlation'],
                        mode='lines+markers',
                        name='Cross Correlation',
                        line=dict(color='green', width=2)
                    ),
                    row=2, col=1
                )
            
            # Plot 5: CTF Fit Quality Heatmap
            if len(df) > 1:
                # Create correlation matrix of CTF parameters
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    fig.add_trace(
                        go.Heatmap(
                            z=corr_matrix.values,
                            x=corr_matrix.columns,
                            y=corr_matrix.columns,
                            colorscale='RdBu',
                            zmid=0
                        ),
                        row=2, col=2
                    )
            
            # Update layout
            fig.update_layout(
                title="CTF Analysis Dashboard",
                height=800,
                showlegend=True,
                template=self._get_plotly_template()
            )
            
            # Update axis labels
            fig.update_xaxes(title_text="Tilt Angle (°)", row=1, col=1)
            fig.update_yaxes(title_text="Resolution (Å)", row=1, col=1)
            
            fig.update_xaxes(title_text="Defocus (μm)", row=1, col=2)
            fig.update_yaxes(title_text="Count", row=1, col=2)
            
            fig.update_xaxes(title_text="Astigmatism Angle (°)", row=1, col=3)
            fig.update_yaxes(title_text="Astigmatism (Å)", row=1, col=3)
            
            fig.update_xaxes(title_text="Micrograph Index", row=2, col=1)
            fig.update_yaxes(title_text="Cross Correlation", row=2, col=1)
            
            # Save to file
            output_file = self.output_dir / "ctf_analysis_dashboard.html"
            fig.write_html(str(output_file))
            
            logger.info(f"Interactive CTF dashboard created: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"Error creating interactive CTF dashboard: {e}")
            return self._create_error_message(str(e))
    
    def _create_static_ctf_dashboard(self, ctf_data: Dict[str, Any]) -> str:
        """Create static CTF analysis dashboard using Matplotlib."""
        if not MATPLOTLIB_AVAILABLE:
            return self._create_no_data_message("Matplotlib not available")
        
        try:
            # Create figure with subplots
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            fig.suptitle('CTF Analysis Dashboard', fontsize=16)
            
            # Extract data
            series_data = list(ctf_data.get('ctf_parameters', {}).values())[0]
            if 'parameters' not in series_data:
                return self._create_no_data_message("No CTF parameters available")
            
            df = series_data['parameters']
            
            # Plot 1: CTF Resolution vs Tilt Angle
            if 'tilt_angle' in df.columns and 'resolution_limit_A' in df.columns:
                scatter = axes[0, 0].scatter(
                    df['tilt_angle'], 
                    df['resolution_limit_A'],
                    c=df['cross_correlation'] if 'cross_correlation' in df.columns else 'blue',
                    cmap='viridis',
                    alpha=0.7
                )
                axes[0, 0].set_xlabel('Tilt Angle (°)')
                axes[0, 0].set_ylabel('Resolution (Å)')
                axes[0, 0].set_title('CTF Resolution vs Tilt Angle')
                if 'cross_correlation' in df.columns:
                    plt.colorbar(scatter, ax=axes[0, 0], label='Cross Correlation')
            
            # Plot 2: Defocus Distribution
            if 'defocus1_A' in df.columns:
                defocus_um = df['defocus1_A'] / 10000
                axes[0, 1].hist(defocus_um, bins=20, alpha=0.7, color='lightblue')
                axes[0, 1].set_xlabel('Defocus (μm)')
                axes[0, 1].set_ylabel('Count')
                axes[0, 1].set_title('Defocus Distribution')
            
            # Plot 3: Astigmatism Analysis
            if 'astigmatism_A' in df.columns and 'astigmatism_angle' in df.columns:
                axes[0, 2].scatter(df['astigmatism_angle'], df['astigmatism_A'], 
                                 color='red', alpha=0.7)
                axes[0, 2].set_xlabel('Astigmatism Angle (°)')
                axes[0, 2].set_ylabel('Astigmatism (Å)')
                axes[0, 2].set_title('Astigmatism Analysis')
            
            # Plot 4: Cross-Correlation Quality
            if 'cross_correlation' in df.columns:
                axes[1, 0].plot(df['cross_correlation'], 'g-o', linewidth=2, markersize=4)
                axes[1, 0].set_xlabel('Micrograph Index')
                axes[1, 0].set_ylabel('Cross Correlation')
                axes[1, 0].set_title('Cross-Correlation Quality')
            
            # Plot 5: CTF Parameters Correlation
            if len(df) > 1:
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    im = axes[1, 1].imshow(corr_matrix, cmap='RdBu', vmin=-1, vmax=1)
                    axes[1, 1].set_xticks(range(len(corr_matrix.columns)))
                    axes[1, 1].set_yticks(range(len(corr_matrix.columns)))
                    axes[1, 1].set_xticklabels(corr_matrix.columns, rotation=45)
                    axes[1, 1].set_yticklabels(corr_matrix.columns)
                    axes[1, 1].set_title('Parameter Correlation')
                    plt.colorbar(im, ax=axes[1, 1])
            
            # Plot 6: Quality Summary
            if 'resolution_limit_A' in df.columns:
                quality_bins = [0, 5, 10, 15, 20, np.inf]
                quality_labels = ['Excellent', 'Good', 'Fair', 'Poor', 'Bad']
                quality_counts = pd.cut(df['resolution_limit_A'], bins=quality_bins, labels=quality_labels).value_counts()
                
                axes[1, 2].pie(quality_counts.values, labels=quality_counts.index, autopct='%1.1f%%')
                axes[1, 2].set_title('Resolution Quality Distribution')
            
            plt.tight_layout()
            
            # Save to file
            output_file = self.output_dir / "ctf_analysis_dashboard.png"
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Static CTF dashboard created: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"Error creating static CTF dashboard: {e}")
            return self._create_error_message(str(e))
    
    def create_motion_analysis_plot(self, motion_data: Dict[str, Any], 
                                  interactive: bool = True) -> str:
        """Create motion analysis visualization."""
        if not PLOTLY_AVAILABLE and interactive:
            interactive = False
        
        if interactive:
            return self._create_interactive_motion_plot(motion_data)
        else:
            return self._create_static_motion_plot(motion_data)
    
    def _create_interactive_motion_plot(self, motion_data: Dict[str, Any]) -> str:
        """Create interactive motion analysis plot."""
        try:
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=[
                    'Frame-by-Frame Motion',
                    'Cumulative Drift',
                    'Motion Magnitude Distribution',
                    'Motion Vector Field'
                ]
            )
            
            # Extract motion data
            for series_name, data in motion_data.get('motion_data', {}).items():
                if 'motion_vectors' not in data:
                    continue
                
                motion_vectors = data['motion_vectors']
                frames = list(range(len(motion_vectors)))
                
                # Calculate motion magnitudes
                magnitudes = [np.sqrt(mv['x']**2 + mv['y']**2) for mv in motion_vectors]
                
                # Plot 1: Frame-by-Frame Motion
                fig.add_trace(
                    go.Scatter(
                        x=frames,
                        y=magnitudes,
                        mode='lines+markers',
                        name=f'{series_name} Motion',
                        line=dict(width=2)
                    ),
                    row=1, col=1
                )
                
                # Plot 2: Cumulative Drift
                cumulative_x = np.cumsum([mv['x'] for mv in motion_vectors])
                cumulative_y = np.cumsum([mv['y'] for mv in motion_vectors])
                
                fig.add_trace(
                    go.Scatter(
                        x=cumulative_x,
                        y=cumulative_y,
                        mode='lines+markers',
                        name=f'{series_name} Drift Path',
                        line=dict(width=2)
                    ),
                    row=1, col=2
                )
                
                # Plot 3: Motion Magnitude Distribution
                fig.add_trace(
                    go.Histogram(
                        x=magnitudes,
                        name=f'{series_name} Distribution',
                        opacity=0.7
                    ),
                    row=2, col=1
                )
            
            # Update layout
            fig.update_layout(
                title="Motion Analysis Dashboard",
                height=800,
                showlegend=True,
                template=self._get_plotly_template()
            )
            
            # Save to file
            output_file = self.output_dir / "motion_analysis.html"
            fig.write_html(str(output_file))
            
            logger.info(f"Interactive motion plot created: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"Error creating interactive motion plot: {e}")
            return self._create_error_message(str(e))
    
    def _create_static_motion_plot(self, motion_data: Dict[str, Any]) -> str:
        """Create static motion analysis plot."""
        if not MATPLOTLIB_AVAILABLE:
            return self._create_no_data_message("Matplotlib not available")
        
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle('Motion Analysis Dashboard', fontsize=16)
            
            for series_name, data in motion_data.get('motion_data', {}).items():
                if 'motion_vectors' not in data:
                    continue
                
                motion_vectors = data['motion_vectors']
                frames = list(range(len(motion_vectors)))
                magnitudes = [np.sqrt(mv['x']**2 + mv['y']**2) for mv in motion_vectors]
                
                # Plot motion magnitude
                axes[0, 0].plot(frames, magnitudes, '-o', label=series_name, linewidth=2, markersize=4)
                
                # Plot cumulative drift
                cumulative_x = np.cumsum([mv['x'] for mv in motion_vectors])
                cumulative_y = np.cumsum([mv['y'] for mv in motion_vectors])
                axes[0, 1].plot(cumulative_x, cumulative_y, '-o', label=series_name, linewidth=2, markersize=4)
                
                # Motion distribution
                axes[1, 0].hist(magnitudes, alpha=0.7, label=series_name, bins=20)
            
            axes[0, 0].set_xlabel('Frame Number')
            axes[0, 0].set_ylabel('Motion Magnitude (pixels)')
            axes[0, 0].set_title('Frame-by-Frame Motion')
            axes[0, 0].legend()
            
            axes[0, 1].set_xlabel('Cumulative X Drift (pixels)')
            axes[0, 1].set_ylabel('Cumulative Y Drift (pixels)')
            axes[0, 1].set_title('Cumulative Drift Path')
            axes[0, 1].legend()
            
            axes[1, 0].set_xlabel('Motion Magnitude (pixels)')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].set_title('Motion Distribution')
            axes[1, 0].legend()
            
            # Remove empty subplot
            fig.delaxes(axes[1, 1])
            
            plt.tight_layout()
            
            output_file = self.output_dir / "motion_analysis.png"
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Static motion plot created: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"Error creating static motion plot: {e}")
            return self._create_error_message(str(e))
    
    def _get_plotly_template(self) -> str:
        """Get Plotly template based on current theme."""
        theme_mapping = {
            'default': 'plotly_white',
            'dark': 'plotly_dark',
            'scientific': 'simple_white'
        }
        return theme_mapping.get(self.current_theme, 'plotly_white')
    
    def _create_no_data_message(self, message: str) -> str:
        """Create a no data message file."""
        html_content = f"""
        <html>
        <head><title>No Data Available</title></head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>No Data Available</h2>
                <p>{message}</p>
            </div>
        </body>
        </html>
        """
        
        output_file = self.output_dir / "no_data.html"
        output_file.write_text(html_content)
        return str(output_file)
    
    def _create_error_message(self, error: str) -> str:
        """Create an error message file."""
        html_content = f"""
        <html>
        <head><title>Visualization Error</title></head>
        <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>Visualization Error</h2>
                <p>Error: {error}</p>
            </div>
        </body>
        </html>
        """
        
        output_file = self.output_dir / "error.html"
        output_file.write_text(html_content)
        return str(output_file)
    
    def set_theme(self, theme_name: str):
        """Set visualization theme."""
        if theme_name in self.themes:
            self.current_theme = theme_name
            logger.info(f"Visualization theme set to: {theme_name}")
        else:
            logger.warning(f"Unknown theme: {theme_name}")
    
    def get_available_themes(self) -> List[str]:
        """Get list of available themes."""
        return list(self.themes.keys())


# Global advanced visualizer instance
advanced_visualizer = AdvancedVisualizer()


def create_visualization(data: Dict[str, Any], viz_type: str, 
                        interactive: bool = True) -> str:
    """Convenience function to create visualizations."""
    if viz_type == "ctf_analysis":
        return advanced_visualizer.create_ctf_analysis_dashboard(data, interactive)
    elif viz_type == "motion_analysis":
        return advanced_visualizer.create_motion_analysis_plot(data, interactive)
    else:
        logger.error(f"Unknown visualization type: {viz_type}")
        return advanced_visualizer._create_error_message(f"Unknown visualization type: {viz_type}")
