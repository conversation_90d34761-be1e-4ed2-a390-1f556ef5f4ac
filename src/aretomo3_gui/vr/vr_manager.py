#!/usr/bin/env python3
"""
AreTomo3 GUI VR/AR Integration Framework
Virtual and Augmented Reality visualization for tomographic data.
"""

import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass
import json
from datetime import datetime

# VR/AR imports with fallbacks
try:
    import openvr
    OPENVR_AVAILABLE = True
except ImportError:
    OPENVR_AVAILABLE = False

try:
    import vispy
    from vispy import scene, app
    from vispy.color import Colormap
    from vispy.visuals.transforms import STTransform
    VISPY_AVAILABLE = True
except ImportError:
    VISPY_AVAILABLE = False

try:
    import moderngl
    MODERNGL_AVAILABLE = True
except ImportError:
    MODERNGL_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class VRConfig:
    """VR system configuration."""
    headset_type: str  # oculus, vive, index, mixed_reality
    render_resolution: Tuple[int, int]
    refresh_rate: int
    tracking_space: str  # seated, standing, room_scale
    controllers_enabled: bool
    hand_tracking: bool
    eye_tracking: bool


@dataclass
class VRScene:
    """VR scene configuration."""
    scene_id: str
    title: str
    description: str
    data_type: str  # tomogram, point_cloud, mesh, volume
    render_mode: str  # volume, isosurface, slice, points
    lighting: Dict[str, Any]
    camera_position: Tuple[float, float, float]
    scale: float
    interactive_elements: List[Dict[str, Any]]


class VRManager:
    """
    VR/AR integration manager for AreTomo3 GUI.
    Handles VR headset initialization, 3D rendering, and interaction.
    """
    
    def __init__(self):
        """Initialize the VR manager."""
        self.vr_system = None
        self.vr_available = False
        self.headset_connected = False
        
        # VR configuration
        self.config = VRConfig(
            headset_type="auto",
            render_resolution=(2160, 1200),
            refresh_rate=90,
            tracking_space="standing",
            controllers_enabled=True,
            hand_tracking=False,
            eye_tracking=False
        )
        
        # Scene management
        self.active_scenes: Dict[str, VRScene] = {}
        self.current_scene: Optional[str] = None
        
        # Rendering context
        self.render_context = None
        self.frame_buffer = None
        
        # Interaction state
        self.controller_states = {}
        self.hand_positions = {}
        self.eye_tracking_data = {}
        
        # Initialize VR system
        self._initialize_vr()
        
        logger.info(f"VR Manager initialized - VR available: {self.vr_available}")
    
    def _initialize_vr(self):
        """Initialize VR system."""
        try:
            if OPENVR_AVAILABLE:
                # Initialize OpenVR
                openvr.init(openvr.VRApplication_Scene)
                self.vr_system = openvr.VRSystem()
                
                if self.vr_system:
                    self.vr_available = True
                    self.headset_connected = self.vr_system.isDisplayOnDesktop()
                    
                    # Get headset info
                    headset_model = self.vr_system.getStringTrackedDeviceProperty(
                        openvr.k_unTrackedDeviceIndex_Hmd,
                        openvr.Prop_ModelNumber_String
                    )
                    
                    logger.info(f"VR headset detected: {headset_model}")
                else:
                    logger.warning("VR system initialized but no headset detected")
            else:
                logger.info("OpenVR not available - VR features disabled")
                
        except Exception as e:
            logger.error(f"Error initializing VR system: {e}")
            self.vr_available = False
    
    def create_tomogram_scene(self, tomogram_data: np.ndarray, 
                            scene_id: str = None) -> Optional[str]:
        """Create VR scene for tomogram visualization."""
        if not self.vr_available:
            logger.warning("VR not available for tomogram scene")
            return None
        
        try:
            scene_id = scene_id or f"tomogram_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create VR scene configuration
            scene = VRScene(
                scene_id=scene_id,
                title=f"Tomogram Visualization - {scene_id}",
                description="3D tomogram data in VR environment",
                data_type="tomogram",
                render_mode="volume",
                lighting={
                    "ambient": 0.3,
                    "directional": {
                        "intensity": 0.7,
                        "direction": [1, 1, 1]
                    }
                },
                camera_position=(0, 0, -5),
                scale=1.0,
                interactive_elements=[
                    {
                        "type": "volume_controls",
                        "position": [-2, 1, 0],
                        "controls": ["opacity", "threshold", "colormap"]
                    },
                    {
                        "type": "slice_plane",
                        "position": [0, 0, 0],
                        "orientation": "xy"
                    }
                ]
            )
            
            # Store scene
            self.active_scenes[scene_id] = scene
            
            # Initialize rendering for this scene
            self._setup_scene_rendering(scene, tomogram_data)
            
            logger.info(f"VR tomogram scene created: {scene_id}")
            return scene_id
            
        except Exception as e:
            logger.error(f"Error creating tomogram scene: {e}")
            return None
    
    def create_point_cloud_scene(self, points: np.ndarray, colors: np.ndarray = None,
                               scene_id: str = None) -> Optional[str]:
        """Create VR scene for point cloud visualization."""
        if not self.vr_available:
            logger.warning("VR not available for point cloud scene")
            return None
        
        try:
            scene_id = scene_id or f"pointcloud_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            scene = VRScene(
                scene_id=scene_id,
                title=f"Point Cloud Visualization - {scene_id}",
                description="3D point cloud data in VR environment",
                data_type="point_cloud",
                render_mode="points",
                lighting={
                    "ambient": 0.4,
                    "point_lights": [
                        {"position": [2, 2, 2], "intensity": 0.6},
                        {"position": [-2, 2, 2], "intensity": 0.6}
                    ]
                },
                camera_position=(0, 0, -3),
                scale=1.0,
                interactive_elements=[
                    {
                        "type": "point_controls",
                        "position": [-1.5, 1, 0],
                        "controls": ["size", "color", "density"]
                    }
                ]
            )
            
            self.active_scenes[scene_id] = scene
            self._setup_point_cloud_rendering(scene, points, colors)
            
            logger.info(f"VR point cloud scene created: {scene_id}")
            return scene_id
            
        except Exception as e:
            logger.error(f"Error creating point cloud scene: {e}")
            return None
    
    def _setup_scene_rendering(self, scene: VRScene, data: np.ndarray):
        """Set up rendering for a VR scene."""
        try:
            if not VISPY_AVAILABLE:
                logger.warning("VisPy not available for VR rendering")
                return
            
            # Create VisPy scene for VR rendering
            canvas = scene.Canvas(keys='interactive', size=self.config.render_resolution)
            view = canvas.central_widget.add_view()
            
            if scene.data_type == "tomogram":
                # Volume rendering
                volume_visual = scene.visuals.Volume(
                    data,
                    parent=view.scene,
                    threshold=0.225,
                    emulate_texture=False
                )
                
                # Set up camera
                view.camera = 'turntable'
                view.camera.fov = 60
                view.camera.distance = 5
            
            # Store rendering context
            scene_context = {
                'canvas': canvas,
                'view': view,
                'data': data
            }
            
            # This would be expanded to handle VR-specific rendering
            logger.info(f"Scene rendering setup completed: {scene.scene_id}")
            
        except Exception as e:
            logger.error(f"Error setting up scene rendering: {e}")
    
    def _setup_point_cloud_rendering(self, scene: VRScene, points: np.ndarray, colors: np.ndarray):
        """Set up point cloud rendering for VR."""
        try:
            if not VISPY_AVAILABLE:
                logger.warning("VisPy not available for point cloud rendering")
                return
            
            # Create point cloud visualization
            canvas = scene.Canvas(keys='interactive', size=self.config.render_resolution)
            view = canvas.central_widget.add_view()
            
            # Create scatter plot
            scatter = scene.visuals.Markers()
            scatter.set_data(points, edge_color=None, face_color=colors, size=5)
            
            view.add(scatter)
            view.camera = 'turntable'
            
            logger.info(f"Point cloud rendering setup completed: {scene.scene_id}")
            
        except Exception as e:
            logger.error(f"Error setting up point cloud rendering: {e}")
    
    def activate_scene(self, scene_id: str) -> bool:
        """Activate a VR scene."""
        if scene_id not in self.active_scenes:
            logger.error(f"Scene not found: {scene_id}")
            return False
        
        try:
            self.current_scene = scene_id
            scene = self.active_scenes[scene_id]
            
            # Set up VR rendering for this scene
            self._activate_vr_rendering(scene)
            
            logger.info(f"VR scene activated: {scene_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error activating scene: {e}")
            return False
    
    def _activate_vr_rendering(self, scene: VRScene):
        """Activate VR rendering for a scene."""
        try:
            if not self.vr_system:
                return
            
            # Set up VR compositor
            compositor = openvr.VRCompositor()
            if compositor:
                # Configure rendering parameters
                compositor.setTrackingSpace(openvr.TrackingUniverseStanding)
                
                # This would set up the actual VR rendering loop
                logger.info("VR rendering activated")
            
        except Exception as e:
            logger.error(f"Error activating VR rendering: {e}")
    
    def update_controller_input(self):
        """Update controller input state."""
        if not self.vr_system:
            return
        
        try:
            # Get controller states
            for device_id in range(openvr.k_unMaxTrackedDeviceCount):
                device_class = self.vr_system.getTrackedDeviceClass(device_id)
                
                if device_class == openvr.TrackedDeviceClass_Controller:
                    controller_state = self.vr_system.getControllerState(device_id)
                    
                    if controller_state:
                        self.controller_states[device_id] = {
                            'trigger': controller_state.rAxis[1].x,
                            'touchpad': (controller_state.rAxis[0].x, controller_state.rAxis[0].y),
                            'buttons': controller_state.ulButtonPressed,
                            'connected': controller_state.bPoseValid
                        }
            
        except Exception as e:
            logger.error(f"Error updating controller input: {e}")
    
    def handle_controller_interaction(self, device_id: int, interaction_type: str):
        """Handle controller interaction."""
        try:
            if device_id not in self.controller_states:
                return
            
            controller = self.controller_states[device_id]
            
            if interaction_type == "trigger_press":
                # Handle trigger press - could be used for selection
                self._handle_selection_interaction()
            
            elif interaction_type == "touchpad_touch":
                # Handle touchpad - could be used for navigation
                touchpad_pos = controller['touchpad']
                self._handle_navigation_interaction(touchpad_pos)
            
            elif interaction_type == "grip_press":
                # Handle grip - could be used for manipulation
                self._handle_manipulation_interaction()
            
        except Exception as e:
            logger.error(f"Error handling controller interaction: {e}")
    
    def _handle_selection_interaction(self):
        """Handle selection interaction in VR."""
        # Implement selection logic
        logger.info("VR selection interaction")
    
    def _handle_navigation_interaction(self, touchpad_pos: Tuple[float, float]):
        """Handle navigation interaction in VR."""
        # Implement navigation logic
        logger.info(f"VR navigation interaction: {touchpad_pos}")
    
    def _handle_manipulation_interaction(self):
        """Handle manipulation interaction in VR."""
        # Implement manipulation logic
        logger.info("VR manipulation interaction")
    
    def export_vr_session(self, output_path: Path) -> bool:
        """Export VR session data."""
        try:
            session_data = {
                'timestamp': datetime.now().isoformat(),
                'config': {
                    'headset_type': self.config.headset_type,
                    'render_resolution': self.config.render_resolution,
                    'refresh_rate': self.config.refresh_rate
                },
                'scenes': {
                    scene_id: {
                        'title': scene.title,
                        'description': scene.description,
                        'data_type': scene.data_type,
                        'render_mode': scene.render_mode
                    }
                    for scene_id, scene in self.active_scenes.items()
                },
                'interaction_log': self.controller_states
            }
            
            with open(output_path, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            logger.info(f"VR session exported: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting VR session: {e}")
            return False
    
    def get_vr_status(self) -> Dict[str, Any]:
        """Get VR system status."""
        status = {
            'vr_available': self.vr_available,
            'headset_connected': self.headset_connected,
            'active_scenes': len(self.active_scenes),
            'current_scene': self.current_scene,
            'controllers_detected': len(self.controller_states),
            'capabilities': {
                'openvr': OPENVR_AVAILABLE,
                'vispy': VISPY_AVAILABLE,
                'moderngl': MODERNGL_AVAILABLE
            }
        }
        
        if self.vr_system:
            try:
                # Get additional VR system info
                status['headset_model'] = self.vr_system.getStringTrackedDeviceProperty(
                    openvr.k_unTrackedDeviceIndex_Hmd,
                    openvr.Prop_ModelNumber_String
                )
                status['tracking_system'] = self.vr_system.getStringTrackedDeviceProperty(
                    openvr.k_unTrackedDeviceIndex_Hmd,
                    openvr.Prop_TrackingSystemName_String
                )
            except:
                pass
        
        return status
    
    def cleanup(self):
        """Clean up VR resources."""
        try:
            if self.vr_system and OPENVR_AVAILABLE:
                openvr.shutdown()
            
            self.active_scenes.clear()
            self.controller_states.clear()
            
            logger.info("VR system cleaned up")
            
        except Exception as e:
            logger.error(f"Error cleaning up VR system: {e}")


# Global VR manager instance
vr_manager = VRManager()


def create_vr_tomogram_view(tomogram_data: np.ndarray) -> Optional[str]:
    """Convenience function to create VR tomogram view."""
    return vr_manager.create_tomogram_scene(tomogram_data)


def get_vr_status() -> Dict[str, Any]:
    """Convenience function to get VR status."""
    return vr_manager.get_vr_status()
