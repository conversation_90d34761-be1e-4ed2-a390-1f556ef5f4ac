#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Workflow Engine
Comprehensive workflow management with dependency tracking, parallel execution, and error recovery.
"""

import logging
import asyncio
import threading
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SKIPPED = "skipped"


class WorkflowStatus(Enum):
    """Workflow execution status."""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


@dataclass
class TaskResult:
    """Task execution result."""
    task_id: str
    status: TaskStatus
    output_data: Dict[str, Any]
    error_message: Optional[str]
    execution_time: float
    timestamp: datetime


@dataclass
class WorkflowTask:
    """Individual workflow task definition."""
    task_id: str
    name: str
    description: str
    function: Callable
    input_parameters: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    retry_count: int = 3
    timeout_seconds: int = 3600
    parallel_allowed: bool = True
    critical: bool = False
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[TaskResult] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class Workflow:
    """Workflow definition and execution state."""
    workflow_id: str
    name: str
    description: str
    tasks: Dict[str, WorkflowTask] = field(default_factory=dict)
    status: WorkflowStatus = WorkflowStatus.CREATED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class WorkflowEngine:
    """
    Advanced workflow engine for AreTomo3 GUI.
    Supports parallel execution, dependency management, and error recovery.
    """
    
    def __init__(self, max_parallel_tasks: int = 4):
        """Initialize the workflow engine."""
        self.max_parallel_tasks = max_parallel_tasks
        self.workflows: Dict[str, Workflow] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_registry: Dict[str, Callable] = {}
        self.event_callbacks: Dict[str, List[Callable]] = {}
        
        # Threading for async execution
        self.loop = None
        self.thread = None
        self.running = False
        
        # Register built-in tasks
        self._register_builtin_tasks()
        
        logger.info(f"Workflow Engine initialized with max {max_parallel_tasks} parallel tasks")
    
    def start(self):
        """Start the workflow engine."""
        if self.running:
            logger.warning("Workflow engine already running")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self.thread.start()
        
        logger.info("Workflow engine started")
    
    def stop(self):
        """Stop the workflow engine."""
        if not self.running:
            return
        
        self.running = False
        
        if self.loop:
            # Cancel all running tasks
            for task in self.running_tasks.values():
                task.cancel()
            
            # Stop the event loop
            self.loop.call_soon_threadsafe(self.loop.stop)
        
        if self.thread:
            self.thread.join(timeout=5.0)
        
        logger.info("Workflow engine stopped")
    
    def _run_event_loop(self):
        """Run the asyncio event loop in a separate thread."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_forever()
        except Exception as e:
            logger.error(f"Error in workflow event loop: {e}")
        finally:
            self.loop.close()
    
    def register_task(self, task_name: str, task_function: Callable):
        """Register a task function."""
        self.task_registry[task_name] = task_function
        logger.info(f"Registered task: {task_name}")
    
    def create_workflow(self, name: str, description: str = "") -> str:
        """Create a new workflow."""
        workflow_id = str(uuid.uuid4())
        workflow = Workflow(
            workflow_id=workflow_id,
            name=name,
            description=description
        )
        
        self.workflows[workflow_id] = workflow
        logger.info(f"Created workflow: {name} ({workflow_id})")
        
        return workflow_id
    
    def add_task(self, workflow_id: str, task_name: str, task_function: str,
                input_parameters: Dict[str, Any], dependencies: List[str] = None,
                **kwargs) -> str:
        """Add a task to a workflow."""
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")
        
        if task_function not in self.task_registry:
            raise ValueError(f"Task function not registered: {task_function}")
        
        task_id = str(uuid.uuid4())
        task = WorkflowTask(
            task_id=task_id,
            name=task_name,
            description=kwargs.get('description', ''),
            function=self.task_registry[task_function],
            input_parameters=input_parameters,
            dependencies=dependencies or [],
            retry_count=kwargs.get('retry_count', 3),
            timeout_seconds=kwargs.get('timeout_seconds', 3600),
            parallel_allowed=kwargs.get('parallel_allowed', True),
            critical=kwargs.get('critical', False)
        )
        
        self.workflows[workflow_id].tasks[task_id] = task
        logger.info(f"Added task {task_name} to workflow {workflow_id}")
        
        return task_id
    
    def execute_workflow(self, workflow_id: str) -> bool:
        """Execute a workflow asynchronously."""
        if workflow_id not in self.workflows:
            logger.error(f"Workflow not found: {workflow_id}")
            return False
        
        if not self.running:
            logger.error("Workflow engine not running")
            return False
        
        # Schedule workflow execution
        future = asyncio.run_coroutine_threadsafe(
            self._execute_workflow_async(workflow_id),
            self.loop
        )
        
        return True
    
    async def _execute_workflow_async(self, workflow_id: str):
        """Execute workflow asynchronously."""
        workflow = self.workflows[workflow_id]
        
        try:
            workflow.status = WorkflowStatus.RUNNING
            workflow.start_time = datetime.now()
            
            self._emit_event('workflow_started', workflow_id, workflow)
            
            # Build dependency graph
            dependency_graph = self._build_dependency_graph(workflow)
            
            # Execute tasks in dependency order
            completed_tasks = set()
            running_tasks = {}
            
            while len(completed_tasks) < len(workflow.tasks):
                # Find ready tasks
                ready_tasks = self._get_ready_tasks(workflow, dependency_graph, completed_tasks, running_tasks)
                
                # Start new tasks (respecting parallel limit)
                while (ready_tasks and 
                       len(running_tasks) < self.max_parallel_tasks):
                    task_id = ready_tasks.pop(0)
                    task = workflow.tasks[task_id]
                    
                    # Start task execution
                    task_coroutine = self._execute_task_async(workflow_id, task_id)
                    running_tasks[task_id] = asyncio.create_task(task_coroutine)
                    
                    logger.info(f"Started task: {task.name}")
                
                # Wait for at least one task to complete
                if running_tasks:
                    done, pending = await asyncio.wait(
                        running_tasks.values(),
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # Process completed tasks
                    for task_future in done:
                        task_id = None
                        for tid, future in running_tasks.items():
                            if future == task_future:
                                task_id = tid
                                break
                        
                        if task_id:
                            del running_tasks[task_id]
                            completed_tasks.add(task_id)
                            
                            # Update progress
                            workflow.progress = len(completed_tasks) / len(workflow.tasks) * 100
                            
                            # Check if critical task failed
                            task = workflow.tasks[task_id]
                            if task.critical and task.status == TaskStatus.FAILED:
                                logger.error(f"Critical task failed: {task.name}")
                                workflow.status = WorkflowStatus.FAILED
                                workflow.error_message = f"Critical task failed: {task.name}"
                                return
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.1)
            
            # Check final status
            failed_tasks = [t for t in workflow.tasks.values() if t.status == TaskStatus.FAILED]
            if failed_tasks:
                workflow.status = WorkflowStatus.FAILED
                workflow.error_message = f"{len(failed_tasks)} tasks failed"
            else:
                workflow.status = WorkflowStatus.COMPLETED
                workflow.progress = 100.0
            
        except Exception as e:
            logger.error(f"Error executing workflow: {e}")
            workflow.status = WorkflowStatus.FAILED
            workflow.error_message = str(e)
        
        finally:
            workflow.end_time = datetime.now()
            self._emit_event('workflow_completed', workflow_id, workflow)
    
    async def _execute_task_async(self, workflow_id: str, task_id: str):
        """Execute a single task asynchronously."""
        workflow = self.workflows[workflow_id]
        task = workflow.tasks[task_id]
        
        start_time = time.time()
        
        try:
            task.status = TaskStatus.RUNNING
            self._emit_event('task_started', workflow_id, task)
            
            # Execute task with timeout
            result = await asyncio.wait_for(
                self._run_task_function(task),
                timeout=task.timeout_seconds
            )
            
            # Create result
            execution_time = time.time() - start_time
            task.result = TaskResult(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                output_data=result,
                error_message=None,
                execution_time=execution_time,
                timestamp=datetime.now()
            )
            task.status = TaskStatus.COMPLETED
            
            logger.info(f"Task completed: {task.name} ({execution_time:.2f}s)")
            
        except asyncio.TimeoutError:
            task.status = TaskStatus.FAILED
            task.result = TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                output_data={},
                error_message="Task timeout",
                execution_time=time.time() - start_time,
                timestamp=datetime.now()
            )
            logger.error(f"Task timeout: {task.name}")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.result = TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                output_data={},
                error_message=str(e),
                execution_time=time.time() - start_time,
                timestamp=datetime.now()
            )
            logger.error(f"Task failed: {task.name} - {e}")
        
        finally:
            self._emit_event('task_completed', workflow_id, task)
    
    async def _run_task_function(self, task: WorkflowTask) -> Dict[str, Any]:
        """Run task function in executor."""
        loop = asyncio.get_event_loop()
        
        # Run in thread pool to avoid blocking
        result = await loop.run_in_executor(
            None,
            task.function,
            task.input_parameters
        )
        
        return result if isinstance(result, dict) else {'result': result}
    
    def _build_dependency_graph(self, workflow: Workflow) -> Dict[str, List[str]]:
        """Build dependency graph for workflow."""
        graph = {}
        for task_id, task in workflow.tasks.items():
            graph[task_id] = task.dependencies.copy()
        return graph
    
    def _get_ready_tasks(self, workflow: Workflow, dependency_graph: Dict[str, List[str]],
                        completed_tasks: set, running_tasks: Dict[str, Any]) -> List[str]:
        """Get tasks that are ready to execute."""
        ready_tasks = []
        
        for task_id, task in workflow.tasks.items():
            if (task_id not in completed_tasks and 
                task_id not in running_tasks and
                task.status == TaskStatus.PENDING):
                
                # Check if all dependencies are completed
                dependencies_met = all(
                    dep_id in completed_tasks 
                    for dep_id in dependency_graph[task_id]
                )
                
                if dependencies_met:
                    ready_tasks.append(task_id)
        
        return ready_tasks
    
    def _register_builtin_tasks(self):
        """Register built-in task functions."""
        self.register_task('aretomo3_processing', self._aretomo3_processing_task)
        self.register_task('ctf_estimation', self._ctf_estimation_task)
        self.register_task('motion_correction', self._motion_correction_task)
        self.register_task('alignment', self._alignment_task)
        self.register_task('quality_assessment', self._quality_assessment_task)
        self.register_task('export_results', self._export_results_task)
    
    def _aretomo3_processing_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in AreTomo3 processing task."""
        # This would integrate with the actual AreTomo3 processing
        logger.info("Executing AreTomo3 processing task")
        time.sleep(1)  # Simulate processing
        return {'status': 'completed', 'output_files': ['output.mrc']}
    
    def _ctf_estimation_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in CTF estimation task."""
        logger.info("Executing CTF estimation task")
        time.sleep(0.5)  # Simulate processing
        return {'status': 'completed', 'ctf_parameters': {'defocus': 2.0}}
    
    def _motion_correction_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in motion correction task."""
        logger.info("Executing motion correction task")
        time.sleep(0.5)  # Simulate processing
        return {'status': 'completed', 'motion_corrected': True}
    
    def _alignment_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in alignment task."""
        logger.info("Executing alignment task")
        time.sleep(0.5)  # Simulate processing
        return {'status': 'completed', 'alignment_quality': 0.95}
    
    def _quality_assessment_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in quality assessment task."""
        logger.info("Executing quality assessment task")
        time.sleep(0.3)  # Simulate processing
        return {'status': 'completed', 'quality_score': 0.85}
    
    def _export_results_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Built-in export results task."""
        logger.info("Executing export results task")
        time.sleep(0.2)  # Simulate processing
        return {'status': 'completed', 'exported_files': ['results.star']}
    
    def add_event_callback(self, event_type: str, callback: Callable):
        """Add event callback."""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        self.event_callbacks[event_type].append(callback)
    
    def _emit_event(self, event_type: str, workflow_id: str, data: Any):
        """Emit workflow event."""
        if event_type in self.event_callbacks:
            for callback in self.event_callbacks[event_type]:
                try:
                    callback(workflow_id, data)
                except Exception as e:
                    logger.error(f"Error in event callback: {e}")
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow status."""
        if workflow_id not in self.workflows:
            return None
        
        workflow = self.workflows[workflow_id]
        return {
            'workflow_id': workflow_id,
            'name': workflow.name,
            'status': workflow.status.value,
            'progress': workflow.progress,
            'start_time': workflow.start_time.isoformat() if workflow.start_time else None,
            'end_time': workflow.end_time.isoformat() if workflow.end_time else None,
            'error_message': workflow.error_message,
            'task_count': len(workflow.tasks),
            'completed_tasks': len([t for t in workflow.tasks.values() if t.status == TaskStatus.COMPLETED]),
            'failed_tasks': len([t for t in workflow.tasks.values() if t.status == TaskStatus.FAILED])
        }
    
    def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        if workflow_id not in self.workflows:
            return False
        
        workflow = self.workflows[workflow_id]
        if workflow.status == WorkflowStatus.RUNNING:
            workflow.status = WorkflowStatus.CANCELLED
            
            # Cancel running tasks
            for task in workflow.tasks.values():
                if task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.CANCELLED
            
            logger.info(f"Cancelled workflow: {workflow.name}")
            return True
        
        return False


# Global workflow engine instance
workflow_engine = WorkflowEngine()


def get_workflow_engine() -> WorkflowEngine:
    """Get the global workflow engine instance."""
    return workflow_engine
