#!/usr/bin/env python3
"""
Standalone CTF Analysis Module Test

This script tests all components of the CTF analysis module:
- CTF data parsing
- Interactive 2D visualization
- Quality assessment
- Comprehensive dashboard
- Export functionality

Usage:
    python test_ctf_standalone.py [path_to_aretomo_output]
    
If no path is provided, it will use the default sample data path.
"""

import sys
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from aretomo3_gui.analysis.ctf_analysis import (
    CTFDataParser,
    CTF2DVisualizer,
    CTFQualityAssessment,
    CTFAnalysisDashboard,
    CTFUtils
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_ctf_parser(test_path):
    """Test CTF data parsing."""
    print("\n" + "="*60)
    print("🔍 TESTING CTF DATA PARSER")
    print("="*60)
    
    try:
        parser = CTFDataParser(test_path)
        ctf_data = parser.parse_all()
        
        print(f"✅ Successfully parsed CTF data for: {ctf_data['series_name']}")
        print(f"   📊 Number of tilts: {ctf_data['n_tilts']}")
        print(f"   📈 Has power spectra: {ctf_data['has_power_spectra']}")
        
        if not ctf_data['parameters'].empty:
            summary = parser.get_ctf_summary()
            print(f"   🎯 Defocus range: {summary['defocus_range_um'][0]:.2f} - {summary['defocus_range_um'][1]:.2f} μm")
            print(f"   🔬 Resolution range: {summary['resolution_range_A'][0]:.1f} - {summary['resolution_range_A'][1]:.1f} Å")
            print(f"   📏 Mean cross-correlation: {summary['mean_cross_correlation']:.3f}")
            
            if ctf_data['tilt_angles']:
                print(f"   📐 Tilt range: {summary['tilt_range'][0]:.1f}° to {summary['tilt_range'][1]:.1f}°")
        
        if ctf_data['power_spectra'] is not None:
            print(f"   🖼️  Power spectra shape: {ctf_data['power_spectra'].shape}")
            print(f"   📊 Data range: {ctf_data['power_spectra'].min():.3f} to {ctf_data['power_spectra'].max():.3f}")
        
        return ctf_data
        
    except Exception as e:
        print(f"❌ Error in CTF parser test: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_ctf_quality(ctf_data):
    """Test CTF quality assessment."""
    print("\n" + "="*60)
    print("⭐ TESTING CTF QUALITY ASSESSMENT")
    print("="*60)
    
    try:
        quality_assessor = CTFQualityAssessment(ctf_data)
        quality_summary = quality_assessor.assess_all_quality()
        
        overall = quality_summary['overall_quality']
        print(f"✅ Quality assessment completed")
        print(f"   🏆 Overall grade: {overall['grade']}")
        print(f"   📊 Overall score: {overall['score']:.1f}/100")
        print(f"   📈 Mean individual score: {overall['mean_individual_score']:.1f}")
        print(f"   📉 Score std deviation: {overall['score_std']:.1f}")
        print(f"   ⚠️  Number of outliers: {overall['outlier_count']}/{quality_summary['n_tilts']}")
        
        if quality_summary.get('resolution_analysis'):
            res_analysis = quality_summary['resolution_analysis']
            print(f"   🔬 Mean resolution: {res_analysis['mean_resolution']:.1f} Å")
            print(f"   🎯 Best resolution: {res_analysis['best_resolution']:.1f} Å")
            print(f"   📈 Resolution trend: {res_analysis['trend_quality']}")
        
        if quality_summary.get('recommendations'):
            print(f"   💡 Recommendations ({len(quality_summary['recommendations'])}):")
            for i, rec in enumerate(quality_summary['recommendations'][:3], 1):
                print(f"      {i}. {rec}")
        
        return quality_summary
        
    except Exception as e:
        print(f"❌ Error in quality assessment test: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_ctf_utils(ctf_data, quality_summary):
    """Test CTF utilities."""
    print("\n" + "="*60)
    print("🛠️  TESTING CTF UTILITIES")
    print("="*60)
    
    try:
        # Test data validation
        validation = CTFUtils.validate_ctf_data(ctf_data)
        print(f"✅ Data validation completed")
        
        for level, messages in validation.items():
            if messages:
                print(f"   {level.upper()} ({len(messages)}):")
                for msg in messages[:2]:  # Show first 2
                    print(f"      • {msg}")
        
        # Test export
        export_success = CTFUtils.export_ctf_data(ctf_data, "test_ctf_export.json", "json")
        print(f"   📤 JSON export: {'✅ Success' if export_success else '❌ Failed'}")
        
        if not ctf_data['parameters'].empty:
            csv_success = CTFUtils.export_ctf_data(ctf_data, "test_ctf_export.csv", "csv")
            print(f"   📊 CSV export: {'✅ Success' if csv_success else '❌ Failed'}")
        
        # Test report generation
        report = CTFUtils.create_ctf_report(ctf_data, quality_summary, "test_ctf_report.txt")
        print(f"   📄 Report generated: {len(report)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in utilities test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_interactive_viewer(ctf_data):
    """Test interactive CTF viewer."""
    print("\n" + "="*60)
    print("🖼️  TESTING INTERACTIVE CTF VIEWER")
    print("="*60)
    
    if ctf_data.get('power_spectra') is None:
        print("⚠️  No power spectra available - skipping interactive viewer test")
        return False
    
    try:
        print("🎮 Creating interactive CTF viewer...")
        print("   📋 Features:")
        print("      • Slider navigation through tilt series")
        print("      • Real-time 2D FFT display updates")
        print("      • CTF ring overlay (toggle with button)")
        print("      • Zoom and pan with mouse")
        print("      • Log scale toggle")
        print("      • Previous/Next buttons")
        
        visualizer = CTF2DVisualizer(ctf_data)
        
        print(f"✅ Visualizer created for {ctf_data['series_name']}")
        print(f"   📊 {ctf_data['n_tilts']} tilts available")
        print("   🎯 Use slider to navigate, mouse wheel to zoom")
        print("   🔘 Use buttons to toggle rings and log scale")
        
        # Show the interactive viewer
        print("\n🚀 Launching interactive viewer...")
        print("   (Close the window to continue with dashboard test)")
        
        visualizer.show()
        
        return True
        
    except Exception as e:
        print(f"❌ Error in interactive viewer test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comprehensive_dashboard(ctf_data):
    """Test comprehensive CTF dashboard."""
    print("\n" + "="*60)
    print("📊 TESTING COMPREHENSIVE CTF DASHBOARD")
    print("="*60)
    
    try:
        print("🎮 Creating comprehensive CTF dashboard...")
        print("   📋 Features:")
        print("      • Interactive 2D CTF viewer")
        print("      • Quality assessment plots")
        print("      • Real-time tilt information")
        print("      • Summary statistics")
        print("      • Recommendations panel")
        print("      • Export functionality")
        
        dashboard = CTFAnalysisDashboard(ctf_data)
        
        print(f"✅ Dashboard created for {ctf_data['series_name']}")
        
        if ctf_data.get('power_spectra') is not None:
            print(f"   🖼️  2D viewer: {ctf_data['n_tilts']} tilts")
        
        if dashboard.quality_summary:
            overall = dashboard.quality_summary['overall_quality']
            print(f"   ⭐ Quality: {overall['grade']} ({overall['score']:.1f}/100)")
        
        print("   📈 Analysis plots: Defocus, Resolution, Quality, Astigmatism")
        print("   📊 Real-time updates with slider navigation")
        
        # Show the dashboard
        print("\n🚀 Launching comprehensive dashboard...")
        print("   (Close the window to continue)")
        
        dashboard.show()
        
        # Export summary
        dashboard.export_summary("test_dashboard_summary.txt")
        print("   📤 Exported dashboard summary")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in dashboard test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🔬 AreTomo3 CTF Analysis Module - Standalone Test")
    print("=" * 60)
    
    # Get test path
    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"
    
    print(f"📁 Test data path: {test_path}")
    
    if not Path(test_path).exists():
        print(f"❌ Test path does not exist: {test_path}")
        print("   Please provide a valid path to AreTomo3 output directory")
        return False
    
    # Test sequence
    test_results = {}
    
    # 1. Test CTF parser
    ctf_data = test_ctf_parser(test_path)
    test_results['parser'] = ctf_data is not None
    
    if not ctf_data:
        print("❌ Cannot continue without parsed CTF data")
        return False
    
    # 2. Test quality assessment
    quality_summary = test_ctf_quality(ctf_data)
    test_results['quality'] = quality_summary is not None
    
    # 3. Test utilities
    test_results['utils'] = test_ctf_utils(ctf_data, quality_summary)
    
    # 4. Test interactive viewer (optional - requires display)
    try:
        test_results['viewer'] = test_interactive_viewer(ctf_data)
    except Exception as e:
        print(f"⚠️  Interactive viewer test skipped: {e}")
        test_results['viewer'] = False
    
    # 5. Test comprehensive dashboard (optional - requires display)
    try:
        test_results['dashboard'] = test_comprehensive_dashboard(ctf_data)
    except Exception as e:
        print(f"⚠️  Dashboard test skipped: {e}")
        test_results['dashboard'] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    
    for test_name, success in test_results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {test_name.capitalize()}: {status}")
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n🎯 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests completed successfully!")
        print("   The CTF analysis module is ready for integration.")
    else:
        print("⚠️  Some tests failed - check the output above for details.")
    
    print("\n📁 Generated files:")
    generated_files = [
        "test_ctf_export.json",
        "test_ctf_export.csv", 
        "test_ctf_report.txt",
        "test_dashboard_summary.txt"
    ]
    
    for filename in generated_files:
        if Path(filename).exists():
            print(f"   ✅ {filename}")
        else:
            print(f"   ❌ {filename} (not created)")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
