#!/usr/bin/env python3
"""
Test script for the themed CTF visualizer

This script tests the improved CTF visualizer with:
- Dark theme matching the GUI
- Better CTF ring visualization
- Improved 2D FFT display
"""

import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import matplotlib
matplotlib.use('TkAgg')  # Use Tk backend to avoid Qt conflicts

from aretomo3_gui.analysis.ctf_analysis.ctf_parser import CTFDataParser
from aretomo3_gui.analysis.ctf_analysis.ctf_visualizer import CTF2DVisualizer

def test_themed_ctf_visualizer():
    """Test the themed CTF visualizer."""

    # Use sample data
    test_path = "sample_data/test_batch/aretomo_output"

    if not Path(test_path).exists():
        print(f"❌ Test path does not exist: {test_path}")
        print("Please provide a valid path to AreTomo3 output directory")
        return False

    try:
        print("🔬 Testing Themed CTF Visualizer")
        print("=" * 50)

        # Parse CTF data
        print("📊 Parsing CTF data...")
        parser = CTFDataParser(test_path)
        ctf_data = parser.parse_all()

        print(f"✅ Successfully parsed CTF data for: {ctf_data['series_name']}")
        print(f"   📈 Number of tilts: {ctf_data['n_tilts']}")
        print(f"   🖼️  Has power spectra: {ctf_data['has_power_spectra']}")

        if ctf_data['power_spectra'] is None:
            print("⚠️  No power spectra available - cannot test visualizer")
            return False

        print(f"   📊 Power spectra shape: {ctf_data['power_spectra'].shape}")

        # Create themed visualizer
        print("\n🎨 Creating themed CTF visualizer...")
        visualizer = CTF2DVisualizer(ctf_data)

        print("✅ Themed visualizer created successfully")
        print("\n🎮 Features:")
        print("   • Dark theme matching GUI with proper fonts")
        print("   • Interactive slider navigation")
        print("   • Real-time 2D FFT updates (log scale)")
        print("   • Real-time 1D CTF plot with theoretical fit")
        print("   • Radial profile extraction from 2D data")
        print("   • CTF ring visualization")
        print("   • Quality-based color coding")
        print("   • Mouse zoom and pan")
        print("   • Themed buttons and controls")

        print("\n🚀 Launching interactive viewer...")
        print("   📋 Layout:")
        print("      • Left: 2D Power Spectrum with CTF rings")
        print("      • Top Right: 1D Radial Profile (extracted from 2D)")
        print("      • Middle Right: Tilt Information")
        print("      • Bottom Right: Controls")
        print("      • Bottom: Slider Navigation")

        print("\n   🎮 Controls:")
        print("      • Use slider to navigate through tilts")
        print("      • Mouse wheel to zoom in/out on 2D plot")
        print("      • Drag to pan around 2D plot")
        print("      • 🔴 Rings: Toggle CTF ring overlay")
        print("      • 📈 CTF Fit: Toggle theoretical CTF curve")
        print("      • 🔍 Reset: Reset zoom and pan")
        print("      • ◀ ▶ : Previous/Next tilt")

        print("\n💡 What to look for:")
        print("   • Dark theme matching your GUI")
        print("   • 2D power spectrum (log scale) with CTF rings")
        print("   • 1D plot: radial profile + theoretical CTF fit")
        print("   • Both plots updating together with slider")
        print("   • Real-time parameter and quality updates")
        print("   • Resolution limits and defocus information")

        # Show the interactive viewer
        visualizer.show()

        print("\n✅ Test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error testing themed CTF visualizer: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_themed_ctf_visualizer()
    if success:
        print("\n🎉 Themed CTF visualizer is working!")
        print("   The module now matches your GUI theme")
        print("   and shows proper CTF ring visualization.")
    else:
        print("\n❌ Test failed - check the output above")

    sys.exit(0 if success else 1)
