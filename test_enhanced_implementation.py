#!/usr/bin/env python3
"""
Test script to verify the enhanced GUI implementation.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "AT3GUI_working" / "src"))

def test_imports():
    """Test if all modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import aretomo3_gui
        print("✅ aretomo3_gui package imported")
    except Exception as e:
        print(f"❌ aretomo3_gui package failed: {e}")
        return False
    
    try:
        from aretomo3_gui.gui.tabs.enhanced_analysis_tab import EnhancedAnalysisTab
        print("✅ Enhanced analysis tab imported")
    except Exception as e:
        print(f"❌ Enhanced analysis tab failed: {e}")
        return False
    
    try:
        from aretomo3_gui.gui.tabs.enhanced_parameters_tab import EnhancedParametersTab
        print("✅ Enhanced parameters tab imported")
    except Exception as e:
        print(f"❌ Enhanced parameters tab failed: {e}")
        return False
    
    try:
        from aretomo3_gui.gui.tabs.live_processing_tab import LiveProcessingTab
        print("✅ Live processing tab imported")
    except Exception as e:
        print(f"❌ Live processing tab failed: {e}")
        return False
    
    return True

def test_backup_system():
    """Test backup system."""
    print("\n🧪 Testing backup system...")
    
    backup_script = Path("backups_AT3GUI/create_backup.sh")
    if backup_script.exists():
        print("✅ Backup script exists")
        
        # Check if it's executable
        if os.access(backup_script, os.X_OK):
            print("✅ Backup script is executable")
        else:
            print("❌ Backup script is not executable")
            
        # Check for compressed backup functionality
        with open(backup_script, 'r') as f:
            content = f.read()
            if "tar -czf" in content:
                print("✅ Compressed backup functionality found")
            else:
                print("❌ Compressed backup functionality missing")
                
        return True
    else:
        print("❌ Backup script missing")
        return False

def main():
    """Run all tests."""
    print("🚀 ENHANCED AT3GUI IMPLEMENTATION TEST")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test backup system
    backup_ok = test_backup_system()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 50)
    
    total_tests = 2
    passed_tests = sum([imports_ok, backup_ok])
    
    print(f"Tests passed: {passed_tests}/{total_tests}")
    
    if imports_ok:
        print("✅ All imports working")
    else:
        print("❌ Import issues detected")
        
    if backup_ok:
        print("✅ Backup system operational")
    else:
        print("❌ Backup system issues")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! Implementation is ready.")
        return 0
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please review issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
