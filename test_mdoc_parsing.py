#!/usr/bin/env python3
"""
Test script for enhanced MDOC parsing with tolerance-based file matching.
"""

import sys
import os
sys.path.insert(0, 'src')

def test_mdoc_parsing():
    """Test the enhanced MDOC parsing functionality."""
    try:
        from aretomo3_gui.gui.main_window import TiltSeries
        
        print("🧪 Testing Enhanced MDOC Parsing...")
        print("=" * 50)
        
        # Test directories
        test_cases = [
            ("../sample_data/test_batch/tomo25", "EER with bracket notation"),
            ("../sample_data/test_batch/test_tiff", "SerialEM TIFF files"),
            ("../sample_data/test_batch/tomo34", "EER tomo34 series"),
        ]
        
        for test_dir, description in test_cases:
            print(f"\n📂 Testing {description}: {test_dir}")
            
            if not os.path.exists(test_dir):
                print(f"   ⚠️  Directory not found: {test_dir}")
                continue
                
            try:
                # Parse tilt series
                series_dict = TiltSeries.parse_tilt_series(test_dir)
                
                if series_dict:
                    print(f"   ✅ Found {len(series_dict)} tilt series:")
                    for name, series in series_dict.items():
                        print(f"      📊 {name}:")
                        print(f"         Files: {len(series.files)}")
                        if series.files:
                            print(f"         Angles: {min(series.angles):.1f}° to {max(series.angles):.1f}°")
                            print(f"         MDOC: {'✅' if series.mdoc_file else '❌'}")
                            print(f"         Sample: {os.path.basename(series.files[0]) if series.files else 'None'}")
                            
                            # Check if we found a reasonable number of files
                            if len(series.files) >= 20:
                                print(f"         🎉 Successfully matched {len(series.files)} files!")
                            elif len(series.files) >= 5:
                                print(f"         ⚠️  Partial match: {len(series.files)} files")
                            else:
                                print(f"         ❌ Poor match: only {len(series.files)} files")
                        else:
                            print(f"         ❌ No files found")
                else:
                    print("   ❌ No tilt series found")
                    
            except Exception as e:
                print(f"   ❌ Error parsing {test_dir}: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n🎉 MDOC parsing test completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_mdoc_parsing()
    sys.exit(0 if success else 1)
