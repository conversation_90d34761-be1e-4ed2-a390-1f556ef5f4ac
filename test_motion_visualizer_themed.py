#!/usr/bin/env python3
"""
Test script for Motion Correction Visualizer with themed interface.

This script demonstrates the motion correction visualizer with:
- Motion corrected image display
- Motion trajectory visualization
- Interactive navigation
- Quality assessment
- Dark theme matching the GUI
"""

import sys
import numpy as np
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from aretomo3_gui.analysis.motion_analysis import MotionCorrectionParser, MotionCorrectionVisualizer


def create_test_theme():
    """Create a test theme matching the AreTomo3 GUI."""
    return {
        'bg_color': '#2b2b2b',
        'text_color': '#ffffff',
        'accent_color': '#4a9eff',
        'button_color': '#404040',
        'button_hover': '#505050',
        'grid_color': '#404040',
        'motion_good': '#4CAF50',
        'motion_fair': '#FF9800',
        'motion_poor': '#F44336',
        'trajectory_color': '#00BCD4'
    }


def test_motion_visualizer():
    """Test the motion correction visualizer with themed interface."""
    print("🔬 Testing Themed Motion Correction Visualizer")
    print("=" * 50)
    
    # Test data path
    test_path = "sample_data/test_batch/aretomo_output"
    
    try:
        print("📊 Parsing motion correction data...")
        
        # Parse motion correction data
        parser = MotionCorrectionParser(test_path)
        motion_data = parser.parse_all()
        
        print(f"✅ Successfully parsed motion data for: {motion_data['series_name']}")
        print(f"   📈 Number of tilts: {motion_data['n_tilts']}")
        print(f"   🖼️  Has motion images: {motion_data['has_motion_images']}")
        print(f"   📊 Has motion data: {motion_data['has_motion_data']}")
        
        if motion_data['motion_images'] is not None:
            print(f"   📊 Motion images shape: {motion_data['motion_images'].shape}")
        
        if motion_data['statistics']:
            print(f"   📈 Motion quality: {motion_data['statistics'].get('quality_assessment', 'Unknown')}")
            print(f"   📊 Mean motion: {motion_data['statistics'].get('mean_total_motion', 0):.2f} Å")
        
        print("\n🎨 Creating themed motion visualizer...")
        
        # Create themed visualizer
        theme = create_test_theme()
        visualizer = MotionCorrectionVisualizer(motion_data, theme=theme)
        
        print("✅ Themed visualizer created successfully")
        
        print("\n🎮 Features:")
        print("   • Dark theme matching GUI with proper fonts")
        print("   • Interactive slider navigation")
        print("   • Motion corrected image display")
        print("   • Motion trajectory visualization")
        print("   • Real-time statistics and quality metrics")
        print("   • Mouse zoom and pan on images")
        print("   • Themed buttons and controls")
        
        print("\n🚀 Launching interactive viewer...")
        print("   📋 Layout:")
        print("      • Top Left: Motion Corrected Image")
        print("      • Top Right: Motion Trajectory Plot")
        print("      • Middle Right: Motion Statistics")
        print("      • Bottom Left: Motion Information Panel")
        print("      • Bottom: Slider Navigation")
        
        print("\n   🎮 Controls:")
        print("      • Use slider to navigate through tilts")
        print("      • Mouse wheel to zoom in/out on images")
        print("      • Drag to pan around images")
        print("      • Trajectories: Toggle motion trajectory display")
        print("      • Statistics: Toggle statistics visualization")
        print("      • ◀ ▶ : Previous/Next tilt")
        
        print("\n💡 What to look for:")
        print("   • Dark theme matching your GUI")
        print("   • Motion corrected images (if available)")
        print("   • Motion trajectory plots")
        print("   • Quality-based color coding")
        print("   • Real-time parameter updates")
        print("   • Motion statistics across tilt series")
        
        # Show interactive viewer
        visualizer.show()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing motion visualizer: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_motion_visualizer()
    if success:
        print("\n✅ Motion visualizer test completed successfully!")
    else:
        print("\n❌ Motion visualizer test failed!")
        sys.exit(1)
