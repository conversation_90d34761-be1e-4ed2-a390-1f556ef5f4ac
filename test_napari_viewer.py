#!/usr/bin/env python3
"""
Test script for the embedded Napari MRC viewer.
"""

import sys
from pathlib import Path

# Add the source directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt6.QtWidgets import QApplication
from aretomo3_gui.gui.viewers.napari_mrc_viewer import Napar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_embedded_napari_viewer():
    """Test the embedded Napari MRC viewer widget."""
    app = QApplication(sys.argv)

    # Create the viewer widget
    viewer = NapariMRCViewer()
    viewer.setWindowTitle("🔬 Embedded Napari MRC Viewer Test")
    viewer.resize(1200, 800)
    viewer.show()

    print("✅ Embedded Napari MRC Viewer created successfully!")
    print("🔬 Professional scientific imaging embedded directly in the GUI")
    print("📁 Use the Browse button to select an MRC file")
    print("🎯 The file will load directly into the embedded Napari viewer")
    print("🚀 No external processes - everything runs smoothly integrated!")

    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_embedded_napari_viewer()
