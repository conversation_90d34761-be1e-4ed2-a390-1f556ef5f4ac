{"timestamp": "2025-05-31T12:11:57.785234", "summary": {"total_suites": 3, "passed_suites": 1, "failed_suites": 2, "total_time": 10.831716775894165, "success_rate": 33.33333333333333}, "results": [{"test_file": "test_comprehensive_integration.py", "success": false, "elapsed_time": 3.117356061935425, "stdout": "....F\n=================================== FAILURES ===================================\n____________ TestComprehensiveIntegration.test_web_api_integration _____________\ntests/test_comprehensive_integration.py:253: in test_web_api_integration\n    web_api.add_log_entry(\"Test log message\", \"INFO\")\nsrc/aretomo3_gui/web/api_server.py:213: in add_log_entry\n    asyncio.create_task(self.broadcast_log_entry(log_entry))\n/home/<USER>/miniforge3/lib/python3.12/asyncio/tasks.py:417: in create_task\n    loop = events.get_running_loop()\nE   RuntimeError: no running event loop\n=========================== short test summary info ============================\nFAILED tests/test_comprehensive_integration.py::TestComprehensiveIntegration::test_web_api_integration\n!!!!!!!!!!!!!!!!!!!!!!!!!! stopping after 1 failures !!!!!!!!!!!!!!!!!!!!!!!!!!!\n1 failed, 4 passed in 1.61s\n", "stderr": "", "return_code": 1}, {"test_file": "test_comprehensive_system.py", "success": false, "elapsed_time": 4.773451328277588, "stdout": "", "stderr": "Fatal Python error: Aborted\n\nCurrent thread 0x00007ff00da00740 (most recent call first):\n  File \"/mnt/HDD/ak_devel/AT3GUI_working/tests/../src/aretomo3_gui/gui/tabs/realtime_analysis_tab.py\", line 51 in __init__\n  File \"/mnt/HDD/ak_devel/AT3GUI_working/tests/test_comprehensive_system.py\", line 269 in setup_method\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/python.py\", line 717 in _call_with_optional_argument\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/python.py\", line 821 in xunit_setup_method_fixture\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/fixtures.py\", line 891 in call_fixture_func\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1140 in pytest_fixture_setup\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_callers.py\", line 103 in _multicall\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_manager.py\", line 120 in _hookexec\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_hooks.py\", line 513 in __call__\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1091 in execute\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/fixtures.py\", line 617 in _get_active_fixturedef\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/fixtures.py\", line 532 in getfixturevalue\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/fixtures.py\", line 697 in _fillfixtures\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/python.py\", line 1630 in setup\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 514 in setup\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 160 in pytest_runtest_setup\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_callers.py\", line 103 in _multicall\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_manager.py\", line 120 in _hookexec\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_hooks.py\", line 513 in __call__\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 242 in <lambda>\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 341 in from_call\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 241 in call_and_report\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 126 in runtestprotocol\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/runner.py\", line 113 in pytest_runtest_protocol\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_callers.py\", line 103 in _multicall\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_manager.py\", line 120 in _hookexec\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_hooks.py\", line 513 in __call__\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/main.py\", line 362 in pytest_runtestloop\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_callers.py\", line 103 in _multicall\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_manager.py\", line 120 in _hookexec\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_hooks.py\", line 513 in __call__\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/main.py\", line 337 in _main\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/main.py\", line 283 in wrap_session\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/main.py\", line 330 in pytest_cmdline_main\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_callers.py\", line 103 in _multicall\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_manager.py\", line 120 in _hookexec\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pluggy/_hooks.py\", line 513 in __call__\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/config/__init__.py\", line 175 in main\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/_pytest/config/__init__.py\", line 201 in console_main\n  File \"/home/<USER>/miniforge3/lib/python3.12/site-packages/pytest/__main__.py\", line 9 in <module>\n  File \"<frozen runpy>\", line 88 in _run_code\n  File \"<frozen runpy>\", line 198 in _run_module_as_main\n\nExtension modules: numpy._core._multiarray_umath, numpy.linalg._umath_linalg, numpy.random._common, numpy.random.bit_generator, numpy.random._bounded_integers, numpy.random._mt19937, numpy.random.mtrand, numpy.random._philox, numpy.random._pcg64, numpy.random._sfc64, numpy.random._generator, psygnal._dataclass_utils, psygnal._exceptions, psygnal._mypyc, psygnal._weak_callback, psygnal._queue, psygnal._signal, psygnal._group, psygnal._group_descriptor, psygnal._evented_decorator, yaml._yaml, _brotli, zstandard.backend_c, psutil._psutil_linux, psutil._psutil_posix, markupsafe._speedups, scipy._lib._ccallback_c, scipy.sparse._sparsetools, _csparsetools, scipy.sparse._csparsetools, scipy.linalg._fblas, scipy.linalg._flapack, scipy.linalg.cython_lapack, scipy.linalg._cythonized_array_utils, scipy.linalg._solve_toeplitz, scipy.linalg._decomp_lu_cython, scipy.linalg._matfuncs_sqrtm_triu, scipy.linalg._matfuncs_expm, scipy.linalg._linalg_pythran, scipy.linalg.cython_blas, scipy.linalg._decomp_update, scipy.sparse.linalg._dsolve._superlu, scipy.sparse.linalg._eigen.arpack._arpack, scipy.sparse.linalg._propack._spropack, scipy.sparse.linalg._propack._dpropack, scipy.sparse.linalg._propack._cpropack, scipy.sparse.linalg._propack._zpropack, scipy.sparse.csgraph._tools, scipy.sparse.csgraph._shortest_path, scipy.sparse.csgraph._traversal, scipy.sparse.csgraph._min_spanning_tree, scipy.sparse.csgraph._flow, scipy.sparse.csgraph._matching, scipy.sparse.csgraph._reordering, scipy._lib._uarray._uarray, scipy.special._ufuncs_cxx, scipy.special._ufuncs, scipy.special._specfun, scipy.special._comb, scipy.special._ellip_harm_2, scipy.fftpack.convolve, numba.core.typeconv._typeconv, numba._helperlib, numba._dynfunc, numba._dispatcher, numba.core.typing.builtins.itertools, numba.cpython.builtins.math, numba.core.runtime._nrt_python, numba.np.ufunc._internal, numba.experimental.jitclass._box, PyQt6.QtCore, PyQt6.QtGui, PyQt6.QtWidgets, PyQt6.QtTest, PIL._imaging, kiwisolver._cext, h5py._errors, h5py.defs, h5py._objects, h5py.h5, h5py.utils, h5py.h5t, h5py.h5s, h5py.h5ac, h5py.h5p, h5py.h5r, h5py._proxy, h5py._conv, h5py.h5z, h5py.h5a, h5py.h5d, h5py.h5ds, h5py.h5g, h5py.h5i, h5py.h5o, h5py.h5f, h5py.h5fd, h5py.h5pl, h5py.h5l, h5py._selector, PyQt6.QtOpenGL, PyQt6.QtOpenGLWidgets, scipy.spatial._ckdtree, scipy._lib.messagestream, scipy.spatial._qhull, scipy.spatial._voronoi, scipy.spatial._distance_wrap, scipy.spatial._hausdorff, scipy.spatial.transform._rotation, pyarrow.lib, pandas._libs.tslibs.ccalendar, pandas._libs.tslibs.np_datetime, pandas._libs.tslibs.dtypes, pandas._libs.tslibs.base, pandas._libs.tslibs.nattype, pandas._libs.tslibs.timezones, pandas._libs.tslibs.fields, pandas._libs.tslibs.timedeltas, pandas._libs.tslibs.tzconversion, pandas._libs.tslibs.timestamps, pandas._libs.properties, pandas._libs.tslibs.offsets, pandas._libs.tslibs.strptime, pandas._libs.tslibs.parsing, pandas._libs.tslibs.conversion, pandas._libs.tslibs.period, pandas._libs.tslibs.vectorized, pandas._libs.ops_dispatch, pandas._libs.missing, pandas._libs.hashtable, pandas._libs.algos, pandas._libs.interval, pandas._libs.lib, pyarrow._compute, pandas._libs.ops, numexpr.interpreter, pandas._libs.hashing, pandas._libs.arrays, pandas._libs.tslib, pandas._libs.sparse, pandas._libs.internals, pandas._libs.indexing, pandas._libs.index, pandas._libs.writers, pandas._libs.join, pandas._libs.window.aggregations, pandas._libs.window.indexers, pandas._libs.reshape, pandas._libs.groupby, pandas._libs.json, pandas._libs.parsers, pandas._libs.testing, scipy.ndimage._nd_image, scipy.ndimage._rank_filter_1d, _ni_label, scipy.ndimage._ni_label, skimage._shared.geometry, skimage.draw._draw, scipy.optimize._group_columns, scipy.optimize._trlib._trlib, scipy.optimize._lbfgsb, _moduleTNC, scipy.optimize._moduleTNC, scipy.optimize._cobyla, scipy.optimize._slsqp, scipy.optimize._minpack, scipy.optimize._lsq.givens_elimination, scipy.optimize._zeros, scipy.optimize._cython_nnls, scipy.linalg._decomp_interpolative, scipy.optimize._bglu_dense, scipy.optimize._lsap, scipy.optimize._direct, scipy.integrate._odepack, scipy.integrate._quadpack, scipy.integrate._vode, scipy.integrate._dop, scipy.integrate._lsoda, scipy.interpolate._fitpack, scipy.interpolate._dfitpack, scipy.interpolate._dierckx, scipy.interpolate._ppoly, scipy.interpolate._interpnd, scipy.interpolate._rbfinterp_pythran, scipy.interpolate._rgi_cython, scipy.interpolate._bspl, scipy.special.cython_special, scipy.stats._stats, scipy.stats._sobol, scipy.stats._qmc_cy, scipy.stats._biasedurn, scipy.stats._stats_pythran, scipy.stats._levy_stable.levyst, scipy.stats._ansari_swilk_statistics, scipy.stats._mvn, scipy.stats._rcont.rcont, vispy.visuals.text._sdf_cpu, triangle.core, PartSegCore_compiled_backend.triangulate, PyQt6.QtSvg, PyQt6.QtNetwork (total: 201)\n", "return_code": -6}, {"test_file": "test_core_components.py", "success": true, "elapsed_time": 2.9409093856811523, "stdout": "......                                                                   [100%]\n6 passed in 1.43s\n", "stderr": "", "return_code": 0}]}