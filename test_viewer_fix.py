#!/usr/bin/env python3
"""
Simple test to verify the tomogram viewer integration fix.
"""

import sys
import os
sys.path.insert(0, 'src')

def test_batch_processing_import():
    """Test that BatchProcessingWidget can be imported and has view_tomogram method."""
    try:
        from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
        print("✅ BatchProcessingWidget import successful")
        
        # Check if view_tomogram method exists
        if hasattr(BatchProcessingWidget, 'view_tomogram'):
            print("✅ view_tomogram method exists")
            
            # Check method signature
            import inspect
            sig = inspect.signature(BatchProcessingWidget.view_tomogram)
            print(f"✅ Method signature: {sig}")
            
            return True
        else:
            print("❌ view_tomogram method not found")
            return False
            
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_napari_viewer_import():
    """Test that NapariMRCViewer can be imported and has load_file method."""
    try:
        from aretomo3_gui.gui.viewers.napari_mrc_viewer import NapariMRCViewer
        print("✅ NapariMRCViewer import successful")
        
        # Check if load_file method exists
        if hasattr(NapariMRCViewer, 'load_file'):
            print("✅ load_file method exists")
            
            # Check method signature
            import inspect
            sig = inspect.signature(NapariMRCViewer.load_file)
            print(f"✅ Method signature: {sig}")
            
            return True
        else:
            print("❌ load_file method not found")
            return False
            
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_integration():
    """Test the integration between batch processing and viewer."""
    try:
        # Test that the integration code is syntactically correct
        from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
        from aretomo3_gui.gui.viewers.napari_mrc_viewer import NapariMRCViewer
        
        # Create a mock widget to test method existence
        widget = BatchProcessingWidget(None)
        
        # Test that the method can be called (will fail due to no file, but should not crash)
        try:
            widget.view_tomogram("/nonexistent/file.mrc")
            print("✅ view_tomogram method callable (expected to show warning)")
        except Exception as e:
            if "File Not Found" in str(e) or "QMessageBox" in str(e):
                print("✅ view_tomogram method works (shows expected file not found behavior)")
            else:
                print(f"❌ Unexpected error in view_tomogram: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🔧 TESTING TOMOGRAM VIEWER INTEGRATION FIX")
    print("=" * 50)
    
    tests = [
        ("Batch Processing Import", test_batch_processing_import),
        ("Napari Viewer Import", test_napari_viewer_import),
        ("Integration Test", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        if test_func():
            print(f"✅ {test_name} PASSED")
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Tomogram viewer integration is working.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
