#!/usr/bin/env python3
"""
Test script for the AreTomo3 Analysis Dashboard.
This script demonstrates the new web dashboard functionality.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from aretomo3_gui.core.realtime_processor import RealTimeProcessor
from aretomo3_gui.web.api_server import AreTomo3WebAPI

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_sample_processor():
    """Create a sample processor with mock data for testing."""
    processor = RealTimeProcessor()
    
    # Add some sample jobs for demonstration
    from aretomo3_gui.core.realtime_processor import ProcessingJob
    from datetime import datetime, timedelta
    import uuid
    
    sample_jobs = [
        {
            "job_id": str(uuid.uuid4()),
            "file_path": Path("/data/tilt_series_001.mdoc"),
            "status": "completed",
            "progress": 1.0,
            "created_at": datetime.now() - timedelta(hours=2),
            "processing_time": 1800,  # 30 minutes
            "result_path": Path("/output/tilt_series_001.mrc")
        },
        {
            "job_id": str(uuid.uuid4()),
            "file_path": Path("/data/tilt_series_002.mdoc"),
            "status": "processing",
            "progress": 0.65,
            "created_at": datetime.now() - timedelta(minutes=45),
            "processing_time": None,
            "result_path": None
        },
        {
            "job_id": str(uuid.uuid4()),
            "file_path": Path("/data/tilt_series_003.mdoc"),
            "status": "queued",
            "progress": 0.0,
            "created_at": datetime.now() - timedelta(minutes=10),
            "processing_time": None,
            "result_path": None
        },
        {
            "job_id": str(uuid.uuid4()),
            "file_path": Path("/data/tilt_series_004.mdoc"),
            "status": "failed",
            "progress": 0.25,
            "created_at": datetime.now() - timedelta(hours=1),
            "processing_time": None,
            "result_path": None
        },
        {
            "job_id": str(uuid.uuid4()),
            "file_path": Path("/data/tilt_series_005.mdoc"),
            "status": "completed",
            "progress": 1.0,
            "created_at": datetime.now() - timedelta(hours=3),
            "processing_time": 2100,  # 35 minutes
            "result_path": Path("/output/tilt_series_005.mrc")
        }
    ]
    
    # Mock the processor's job storage
    processor._jobs = []
    for job_data in sample_jobs:
        job = ProcessingJob(
            job_id=job_data["job_id"],
            file_path=job_data["file_path"],
            output_directory=Path("/output"),
            parameters={}
        )
        job.status = job_data["status"]
        job.progress = job_data["progress"]
        job.created_at = job_data["created_at"]
        job.processing_time = job_data["processing_time"]
        job.result_path = job_data["result_path"]
        processor._jobs.append(job)
    
    return processor

async def main():
    """Main function to run the web dashboard test."""
    logger.info("🚀 Starting AreTomo3 Analysis Dashboard Test")
    
    try:
        # Create sample processor with mock data
        processor = create_sample_processor()
        logger.info(f"Created sample processor with {len(processor._jobs)} mock jobs")
        
        # Create web API server
        web_api = AreTomo3WebAPI(processor)
        logger.info("Created web API server")
        
        # Print dashboard information
        print("\n" + "="*60)
        print("🔬 AreTomo3 Analysis Dashboard")
        print("="*60)
        print(f"📊 Dashboard URL: http://localhost:8080")
        print(f"📈 Legacy Dashboard: http://localhost:8080/legacy")
        print(f"🔧 API Documentation: http://localhost:8080/api/docs")
        print(f"📝 API Redoc: http://localhost:8080/api/redoc")
        print("="*60)
        print("\n🎯 Features Available:")
        print("  • Real-time tilt series monitoring")
        print("  • Quality assessment visualization")
        print("  • Interactive analysis panels")
        print("  • Processing controls")
        print("  • Tomogram viewer integration")
        print("  • WebSocket real-time updates")
        print("  • Session management")
        print("  • Comprehensive API endpoints")
        print("\n💡 Sample Data:")
        print(f"  • {len([j for j in processor._jobs if j.status == 'completed'])} completed tilt series")
        print(f"  • {len([j for j in processor._jobs if j.status == 'processing'])} processing")
        print(f"  • {len([j for j in processor._jobs if j.status == 'queued'])} queued")
        print(f"  • {len([j for j in processor._jobs if j.status == 'failed'])} failed")
        print("\n🌐 Open your browser and navigate to http://localhost:8080")
        print("Press Ctrl+C to stop the server")
        print("="*60)
        
        # Run the web server
        web_api.run(host="0.0.0.0", port=8080, debug=True)
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Error running dashboard: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
