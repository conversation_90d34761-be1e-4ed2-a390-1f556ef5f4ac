#!/usr/bin/env python3
"""
Test script to verify the batch processing directory fix.
"""

import sys
import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, MagicMock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from aretomo3_gui.core.tilt_series import TiltSeries


def test_batch_directory_logic():
    """Test that batch processing creates directories in the correct locations."""
    print("🧪 Testing batch processing directory fix...")
    
    # Create temporary directories for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Using temp directory: {temp_dir}")
        
        # Create test structure
        input_dir_1 = os.path.join(temp_dir, "input1")
        input_dir_2 = os.path.join(temp_dir, "input2")
        global_output = os.path.join(temp_dir, "global_output")
        
        os.makedirs(input_dir_1, exist_ok=True)
        os.makedirs(input_dir_2, exist_ok=True)
        os.makedirs(global_output, exist_ok=True)
        
        # Create mock tilt series files
        series1_files = [os.path.join(input_dir_1, "Position_1.mrc")]
        series2_files = [os.path.join(input_dir_2, "Position_2.mrc")]
        
        for file_path in series1_files + series2_files:
            with open(file_path, 'w') as f:
                f.write("mock file")
        
        # Create mock tilt series
        series1 = TiltSeries("Position_1")
        series1.files = series1_files
        series1.series_params = {
            'pixel_size': 1.91,
            'voltage': 300,
            'tilt_axis': -95.75,
            'cs': 2.7,
            'amp_contrast': 0.1,
            'frame_dose': 0.14
        }
        
        series2 = TiltSeries("Position_2", series2_files)
        series2.series_params = {
            'pixel_size': 1.91,
            'voltage': 300,
            'tilt_axis': -95.75,
            'cs': 2.7,
            'amp_contrast': 0.1,
            'frame_dose': 0.14
        }
        
        # Mock GUI components
        mock_gui = Mock()
        mock_gui.output_dir = Mock()
        mock_gui.output_dir.text.return_value = global_output
        
        # Mock batch widget
        mock_batch_widget = Mock()
        mock_batch_widget.output_dir_override_chk = Mock()
        mock_batch_widget.output_dir_override_chk.isChecked.return_value = False
        mock_gui.batch_widget = mock_batch_widget
        
        # Mock GUI parameter controls
        for param in ['pixel_size', 'tilt_axis', 'mc_bin', 'mc_patch_x', 'mc_patch_y', 
                     'fm_int', 'volume_z', 'dark_tol', 'at_bin', 'gpu_index', 'lowpass']:
            mock_control = Mock()
            mock_control.value.return_value = 1
            setattr(mock_gui, param, mock_control)
        
        # Mock checkbox controls
        for checkbox in ['flip_volume', 'out_xf', 'out_imod', 'wbp', 'tilt_cor', 'correct_ctf']:
            mock_checkbox = Mock()
            mock_checkbox.isChecked.return_value = False
            setattr(mock_gui, checkbox, mock_checkbox)
        
        # Mock the batch queue
        mock_gui.batch_queue = [
            {
                'series': series1,
                'input_dir': input_dir_1,
                'position': 'Position_1'
            },
            {
                'series': series2, 
                'input_dir': input_dir_2,
                'position': 'Position_2'
            }
        ]
        
        # Import and test the _build_command method
        from aretomo3_gui.gui.main_window import AreTomoGUI
        
        # Test without override (should create relative to input)
        print("🔧 Testing batch processing without override...")
        command1 = AreTomoGUI._build_command(mock_gui, "/fake/aretomo3", series1, input_dir_1)
        
        # Check if the relative output directory was created
        expected_outdir1 = os.path.join(input_dir_1, "aretomo_output", "Position_1")
        if os.path.exists(expected_outdir1):
            print(f"✅ Correct relative directory created: {expected_outdir1}")
        else:
            print(f"❌ Expected directory not created: {expected_outdir1}")
            pytest.fail("Test failed")
        # Test with override (should use global output)
        print("🔧 Testing batch processing with override...")
        mock_batch_widget.output_dir_override_chk.isChecked.return_value = True
        command2 = AreTomoGUI._build_command(mock_gui, "/fake/aretomo3", series2, input_dir_2)
        
        # Check if the global output directory was used
        expected_outdir2 = os.path.join(global_output, "Position_2")
        if os.path.exists(expected_outdir2):
            print(f"✅ Correct global directory created: {expected_outdir2}")
        else:
            print(f"❌ Expected global directory not created: {expected_outdir2}")
            pytest.fail("Test failed")
        # Test individual processing (no batch_input_dir)
        print("🔧 Testing individual processing...")
        mock_gui.batch_queue = []  # Clear batch queue
        command3 = AreTomoGUI._build_command(mock_gui, "/fake/aretomo3", series1, None)
        
        # Should use global output directory for individual processing
        expected_outdir3 = os.path.join(global_output, "Position_1")
        if os.path.exists(expected_outdir3):
            print(f"✅ Correct individual processing directory created: {expected_outdir3}")
        else:
            print(f"❌ Expected individual directory not created: {expected_outdir3}")
            pytest.fail("Test failed")
        print("🎉 All directory logic tests passed!")
        # Test passed
if __name__ == "__main__":
    success = test_batch_directory_logic()
    if success:
        print("\n✅ BATCH FIX VERIFICATION SUCCESSFUL!")
        print("The batch processing now correctly creates directories:")
        print("  • Relative to input directory when override is disabled")
        print("  • In global output directory when override is enabled")
        print("  • In global output directory for individual processing")
    else:
        print("\n❌ BATCH FIX VERIFICATION FAILED!")
        assert False, "Test failed"
