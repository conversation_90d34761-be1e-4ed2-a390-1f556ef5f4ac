#!/usr/bin/env python3
"""
Test case for investigating batch processing hang issue
where "nothing happens" after finding tilt series.
"""

import sys
import os
import pytest
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Import from the installed package
from aretomo3_gui.gui.main_window import AreT<PERSON><PERSON><PERSON>
from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
from aretomo3_gui.core.config.config_validation import AreTomo3Config

def test_basic():
    """Basic test to verify test discovery."""
    assert True

class TestBatchProcessingHang:
    """Test suite for investigating batch processing hang."""
    
    @pytest.fixture
    def qtbot(self, qapp):
        """Create a Qt Test Robot instance."""
        from pytestqt.qtbot import QtBot
        return QtBot(qapp)
    
    @pytest.fixture
    def main_window(self, qtbot):
        """Create main window instance."""
        from aretomo3_gui.gui.main_window import AreTomoGUI
        window = AreTomoGUI()
        window.show()
        qtbot.addWidget(window)
        return window
    
    @pytest.fixture
    def test_data_dir(self):
        """Create test data directory with sample tilt series."""
        data_dir = Path(__file__).parent.parent / 'data' / 'realistic_tilt_series'
        if not data_dir.exists():
            data_dir.mkdir(parents=True)
            # Create sample tilt series files
            for angle in [-60, -30, 0, 30, 60]:
                filename = f"Position_1_{abs(angle):03d}_{angle:.2f}_EER.eer"
                (data_dir / filename).touch()
        return data_dir
    
    def test_batch_widget_signals(self, main_window):
        """Test if batch widget signals are properly connected."""
        batch_widget = main_window.findChild(BatchProcessingWidget)
        assert batch_widget is not None, "Batch widget not found"
        
        # Check signal connections
        signals = batch_widget.metaObject().signals()
        signal_names = [s.name().data().decode() for s in signals]
        assert 'batchStarted' in signal_names, "batchStarted signal not found"
        assert 'batchFinished' in signal_names, "batchFinished signal not found"
    
    def test_batch_processing_workflow(self, main_window, test_data_dir):
        """Test complete batch processing workflow."""
        batch_widget = main_window.findChild(BatchProcessingWidget)
        assert batch_widget is not None, "Batch widget not found"
        
        # Monitor signal emissions
        batch_started = False
        batch_finished = False
        
        def on_batch_started():
            nonlocal batch_started
            batch_started = True
            logger.info("Batch processing started")
        
        def on_batch_finished():
            nonlocal batch_finished
            batch_finished = True
            logger.info("Batch processing finished")
        
        # Connect signals
        batch_widget.batchStarted.connect(on_batch_started)
        batch_widget.batchFinished.connect(on_batch_finished)
        
        # Add test directory
        batch_widget.add_directory(str(test_data_dir))
        
        # Start processing
        batch_widget.start_processing()
        
        # Use QTimer to check status
        timer = QTimer()
        timeout = 5000  # 5 seconds
        
        def check_status():
            assert batch_started, "Batch processing never started"
            if batch_widget.is_processing():
                logger.info("Still processing...")
            else:
                timer.stop()
                assert batch_finished, "Batch processing never finished"
        
        timer.timeout.connect(check_status)
        timer.start(100)  # Check every 100ms
        
        # Run event loop for timeout duration
        QTimer.singleShot(timeout, lambda: sys.exit())
        sys.exit(app.exec())

if __name__ == '__main__':
    pytest.main(['-v', __file__])
