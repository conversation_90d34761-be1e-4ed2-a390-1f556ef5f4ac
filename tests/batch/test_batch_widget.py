#!/usr/bin/env python3
"""
Test script for BatchProcessingWidget logging functionality.
"""

import sys
import os

# Add src to path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
from PyQt6.QtWidgets import QWidget

# Mock parent class similar to AreTomoGUI
class MockAreTomoGUI(QWidget):
    def __init__(self):
        super().__init__()
        print("DEBUG: MockAreTomoGUI created")
    
    # Note: This class intentionally DOESN'T have log_message method
    # to reproduce the warning

# Mock parent class WITH log_message method
class MockAreTomoGUIWithLogging(QWidget):
    def __init__(self):
        super().__init__()
        print("DEBUG: MockAreTomoGUIWithLogging created")
    
    def log_message(self, message, level="INFO"):
        print(f"PARENT LOG [{level}]: {message}")

def test_batch_widget_without_parent_logging():
    """Test BatchProcessingWidget with parent that doesn't have log_message"""
    print("\n=== Test 1: Parent without log_message method ===")
    
    parent = MockAreTomoGUI()
    widget = BatchProcessingWidget(parent)
    
    print("Testing log_message calls:")
    widget.log_message("Test message 1")
    widget.log_message("Test warning", "WARNING")
    widget.log_message("Test error", "ERROR")

def test_batch_widget_with_parent_logging():
    """Test BatchProcessingWidget with parent that has log_message"""
    print("\n=== Test 2: Parent with log_message method ===")
    
    parent = MockAreTomoGUIWithLogging()
    widget = BatchProcessingWidget(parent)
    
    print("Testing log_message calls:")
    widget.log_message("Test message 1")
    widget.log_message("Test warning", "WARNING")
    widget.log_message("Test error", "ERROR")

if __name__ == "__main__":
    print("Testing BatchProcessingWidget logging functionality...")
    
    test_batch_widget_without_parent_logging()
    test_batch_widget_with_parent_logging()
    
    print("\n=== Test completed ===")
