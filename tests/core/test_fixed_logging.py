import pytest
#!/usr/bin/env python3
"""
Simple test to verify the log_message method fix.
"""

print("Testing BatchProcessingWidget logging...")

# Create a mock class that represents AreTomoGUI with the log_message method
class MockAreTomoGUIFixed:
    def __init__(self):
        print("DEBUG: MockAreTomoGUIFixed created")
        # Simulate the log_text widget
        self.log_messages = []
    
    def log_message(self, message, level="INFO"):
        """Mock implementation of log_message"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        self.log_messages.append(formatted_message)
        print(f"GUI LOG: {formatted_message}")

# Simple mock of BatchProcessingWidget functionality
class MockBatchProcessingWidget:
    def __init__(self, parent):
        self.parent = parent
        print("DEBUG: BatchProcessingWidget created with parent class:", parent.__class__.__name__)
        
        # Check if parent has log_message method
        if hasattr(parent, 'log_message') and callable(getattr(parent, 'log_message')):
            print("SUCCESS: Parent has a callable log_message method")
        else:
            print("WARNING: Parent does not have a callable log_message method")
    
    def log_message(self, message, level="INFO"):
        """Safe logging that checks if parent has log_message method"""
        if hasattr(self.parent, 'log_message') and callable(getattr(self.parent, 'log_message')):
            self.parent.log_message(message, level)
        else:
            # Fallback to console logging
            print(f"{level}: {message}")

def test_fixed_logging():
    """Test the fixed logging functionality"""
    print("\n=== Testing Fixed Implementation ===")
    
    # Create parent with log_message method
    parent = MockAreTomoGUIFixed()
    
    # Create widget with the parent
    widget = MockBatchProcessingWidget(parent)
    
    # Test logging functionality
    print("\nTesting various log levels:")
    widget.log_message("BatchProcessingWidget initialized successfully")
    widget.log_message("This is a warning message", "WARNING")
    widget.log_message("This is an error message", "ERROR")
    widget.log_message("Processing completed", "INFO")
    
    print(f"\nTotal messages logged: {len(parent.log_messages)}")
    # Test passed
if __name__ == "__main__":
    success = test_fixed_logging()
    if success:
        print("\n✓ Test passed - BatchProcessingWidget logging works correctly!")
    else:
        print("\n✗ Test failed")
