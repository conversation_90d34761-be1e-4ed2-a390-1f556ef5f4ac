#!/usr/bin/env python3
"""
Enhanced Spinbox Design Test - Side Buttons Layout
=================================================

This test demonstrates the new spinbox design with:
- Larger, more prominent buttons
- Buttons positioned on left (down) and right (up) sides of the number
- More spacing and better visibility
- Larger arrow symbols

The new layout provides:
- Down button (−) on the LEFT side
- Up button (+) on the RIGHT side
- More space for the number in the center
- Larger, more visible buttons and arrows
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QSpinBox, QDoubleSpinBox, QLabel, QPushButton,
                             QGroupBox, QGridLayout)
from PyQt6.QtCore import Qt

# Import the theme manager
try:
    from src.gui.theme_manager import ThemeManager
except ImportError:
    try:
        from aretomo3_gui.gui.theme_manager import ThemeManager
    except ImportError:
        print("Could not import ThemeManager")
        ThemeManager = None

class EnhancedSpinboxTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager() if ThemeManager else None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("Enhanced Spinbox Design - Side Buttons Layout")
        self.setGeometry(100, 100, 700, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("Enhanced Spinbox Design with Side Buttons")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Theme toggle
        if self.theme_manager:
            theme_layout = QHBoxLayout()
            self.theme_button = QPushButton("Toggle Theme (Current: Light)")
            self.theme_button.clicked.connect(self.toggle_theme)
            theme_layout.addWidget(self.theme_button)
            theme_layout.addStretch()
            layout.addLayout(theme_layout)
        
        # Create demonstration grid
        demo_group = QGroupBox("Enhanced Spinbox Controls")
        demo_layout = QGridLayout(demo_group)
        
        # Description
        desc = QLabel(
            "New Layout: [−] Number [+]\n"
            "• Down button (−) on LEFT side\n"
            "• Up button (+) on RIGHT side\n"
            "• Larger, more visible buttons\n"
            "• Better spacing and arrows"
        )
        desc.setWordWrap(True)
        desc.setStyleSheet("margin: 10px; padding: 10px; background-color: rgba(128,128,128,0.1);")
        demo_layout.addWidget(desc, 0, 0, 1, 2)
        
        # Integer spinboxes
        demo_layout.addWidget(QLabel("Small Integer Range:"), 1, 0)
        int_small = QSpinBox()
        int_small.setMinimum(0)
        int_small.setMaximum(10)
        int_small.setValue(5)
        demo_layout.addWidget(int_small, 1, 1)
        
        demo_layout.addWidget(QLabel("Large Integer Range:"), 2, 0)
        int_large = QSpinBox()
        int_large.setMinimum(-1000)
        int_large.setMaximum(1000)
        int_large.setValue(42)
        demo_layout.addWidget(int_large, 2, 1)
        
        # Double spinboxes
        demo_layout.addWidget(QLabel("Decimal Values:"), 3, 0)
        double_precision = QDoubleSpinBox()
        double_precision.setMinimum(0.0)
        double_precision.setMaximum(100.0)
        double_precision.setValue(12.34)
        double_precision.setDecimals(2)
        double_precision.setSingleStep(0.1)
        demo_layout.addWidget(double_precision, 3, 1)
        
        demo_layout.addWidget(QLabel("Scientific Values:"), 4, 0)
        double_scientific = QDoubleSpinBox()
        double_scientific.setMinimum(-999.999)
        double_scientific.setMaximum(999.999)
        double_scientific.setValue(3.14159)
        double_scientific.setDecimals(5)
        double_scientific.setSingleStep(0.001)
        demo_layout.addWidget(double_scientific, 4, 1)
        
        layout.addWidget(demo_group)
        
        # Comparison with standard spinboxes
        comparison_group = QGroupBox("Comparison: Standard vs Enhanced")
        comparison_layout = QGridLayout(comparison_group)
        
        comparison_layout.addWidget(QLabel("Standard Spinbox:"), 0, 0)
        standard_spinbox = QSpinBox()
        standard_spinbox.setMinimum(0)
        standard_spinbox.setMaximum(100)
        standard_spinbox.setValue(50)
        standard_spinbox.setStyleSheet("")  # No theme styling
        comparison_layout.addWidget(standard_spinbox, 0, 1)
        
        comparison_layout.addWidget(QLabel("Enhanced Spinbox:"), 1, 0)
        enhanced_spinbox = QSpinBox()
        enhanced_spinbox.setMinimum(0)
        enhanced_spinbox.setMaximum(100)
        enhanced_spinbox.setValue(50)
        comparison_layout.addWidget(enhanced_spinbox, 1, 1)
        
        layout.addWidget(comparison_group)
        
        # Instructions
        instructions = QLabel(
            "Test Instructions:\n"
            "1. Click the [−] button (LEFT side) to decrease values\n"
            "2. Click the [+] button (RIGHT side) to increase values\n"
            "3. Compare enhanced layout with standard spinbox\n"
            "4. Test both themes to see button visibility\n"
            "5. Try keyboard input in the center area\n"
            "6. Notice larger buttons and better spacing"
        )
        instructions.setWordWrap(True)
        instructions.setStyleSheet("margin: 10px; padding: 10px; border: 1px solid gray;")
        layout.addWidget(instructions)
        
        # Apply initial theme
        if self.theme_manager:
            self.apply_current_theme()
            
    def toggle_theme(self):
        if not self.theme_manager:
            return
            
        # Toggle theme
        if self.theme_manager.current_theme == "light":
            self.theme_manager.set_theme("dark")
            self.theme_button.setText("Toggle Theme (Current: Dark)")
        else:
            self.theme_manager.set_theme("light")
            self.theme_button.setText("Toggle Theme (Current: Light)")
        
        self.apply_current_theme()
        
    def apply_current_theme(self):
        if not self.theme_manager:
            return
            
        stylesheet = self.theme_manager.get_theme_stylesheet()
        self.setStyleSheet(stylesheet)

def main():
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = EnhancedSpinboxTestWindow()
    window.show()
    
    print("Enhanced Spinbox Design Test Window opened.")
    print("New layout: [−] Number [+]")
    print("- Down button (−) on LEFT side")
    print("- Up button (+) on RIGHT side") 
    print("- Larger, more visible buttons and arrows")
    print("- Better spacing for improved usability")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
