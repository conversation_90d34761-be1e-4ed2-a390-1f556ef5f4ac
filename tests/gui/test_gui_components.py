"""
Test module for GUI components.
"""
import pytest
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QMainWindow, QMenu
from aretomo3_gui.gui.main_window import AreT<PERSON><PERSON><PERSON>
from aretomo3_gui.gui.viewers.analysis_viewer import <PERSON><PERSON>iewer
from aretomo3_gui.gui.viewers.mrc_viewer import <PERSON><PERSON><PERSON>iewer

@pytest.mark.gui
def test_main_window(qtbot):
    """Test main window initialization and basic functionality."""
    window = AreTomoGUI()
    qtbot.addWidget(window)

    # Test window properties
    assert window.windowTitle() == 'AreTomo3 GUI'
    assert isinstance(window, QMainWindow)

    # Test menu creation
    menubar = window.menuBar()
    assert menubar is not None
    assert 'File' in [menu.title() for menu in menubar.findChildren(QMenu)]
    assert 'Help' in [menu.title() for menu in menubar.findChildren(QMenu)]

@pytest.mark.gui
def test_analysis_viewer(qtbot, tmp_path):
    """Test analysis viewer functionality."""
    viewer = AnalysisViewer(None)
    qtbot.addWidget(viewer)

    # Create a test file
    test_file = tmp_path / "test.mrc"
    test_file.touch()
    
    # Test file loading
    viewer.load_file(str(test_file))
    assert viewer.current_file == str(test_file)

@pytest.mark.gui
def test_mrc_viewer(qtbot):
    """Test MRC viewer functionality."""
    viewer = MRCViewer(None)
    qtbot.addWidget(viewer)

    # Test viewer initialization
    assert viewer is not None
    assert hasattr(viewer, 'view')
