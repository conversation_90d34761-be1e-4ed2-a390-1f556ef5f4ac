#!/usr/bin/env python3
"""Basic Qt test to verify PyQt6 installation."""

import pytest
from PyQt6.QtWidgets import QApplication, QMainWindow

class TestQtBasic:
    """Basic Qt functionality tests."""
    
    @pytest.fixture
    def app(self):
        """Create QApplication instance."""
        return QApplication([])
    
    def test_window_creation(self, app):
        """Test that we can create a window."""
        window = QMainWindow()
        assert window is not None
        window.show()
        window.close()
