#!/usr/bin/env python3
"""
Simple Test for AreTomo3 GUI
"""

import sys
import os
sys.path.insert(0, 'src')

print("=" * 60)
print(" AreTomo3 GUI Simple Test")
print("=" * 60)

# Test 1: Import main components
print("\n[1] Testing imports...")
try:
    from aretomo3_gui.gui.main_window import AreTomoGUI
    print("PASS: Main window import")
except Exception as e:
    print(f"FAIL: Main window import - {e}")
    sys.exit(1)

# Test 2: Create GUI instance (headless)
print("\n[2] Testing GUI creation...")
try:
    os.environ["QT_QPA_PLATFORM"] = "offscreen"
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication([])
    gui = AreTomoGUI()
    print("PASS: GUI creation")
    
    # Test that new methods exist
    if hasattr(gui, '_refresh_analysis'):
        print("PASS: _refresh_analysis method exists")
    else:
        print("FAIL: _refresh_analysis method missing")
    
    if hasattr(gui, 'on_start_batch_processing'):
        print("PASS: on_start_batch_processing method exists")
    else:
        print("FAIL: on_start_batch_processing method missing")
    
    app.quit()
    
except Exception as e:
    print(f"FAIL: GUI creation - {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test 3: File utilities
print("\n[3] Testing file utilities...")
try:
    from aretomo3_gui.utils.file_utils import analyze_directory
    import tempfile
    
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files
        test_file = os.path.join(tmpdir, "test.eer")
        with open(test_file, 'w') as f:
            f.write("test")
        
        result = analyze_directory(tmpdir)
        if result['total_files'] > 0:
            print("PASS: File utilities working")
        else:
            print("FAIL: File utilities not detecting files")
            
except Exception as e:
    print(f"FAIL: File utilities - {e}")

print("\n" + "=" * 60)
print(" Test Complete")
print("=" * 60)
