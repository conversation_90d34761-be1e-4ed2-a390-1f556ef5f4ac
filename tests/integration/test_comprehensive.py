#!/usr/bin/env python3
"""
Comprehensive test for BatchProcessingWidget logging functionality.
"""

import sys
import pytest
import os
from datetime import datetime
from unittest.mock import Mock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """Test basic import functionality."""
    print("Testing import functionality...")
    
    try:
        # Test core import
        import aretomo3_gui
        print("✅ aretomo3_gui imported successfully")
        
        # Test GUI import
        from aretomo3_gui.gui.main_window import AreTomoGUI
        print("✅ AreTomoGUI imported successfully")
        
        # Test passed
        assert True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        # Skip if imports not available
        pytest.skip(f"Required modules not available: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        assert False, f"Import test failed: {e}"

def test_with_mock_parent():
    """Test BatchProcessingWidget with mock parent."""
    print("Testing BatchProcessingWidget with mock parent...")
    
    try:
        # Import and create widget
        from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        
        # Create mock parent with log_message method
        mock_parent = Mock()
        mock_parent.log_message = Mock()
        
        widget = BatchProcessingWidget()  # Use None parent
        widget.main_window = mock_parent  # Set the main_window attribute directly
        
        # Test logging functionality
        widget.log_message("Test message")
        
        # Verify log_message was called on the main_window
        mock_parent.log_message.assert_called_once()
        print("✅ BatchProcessingWidget working with mock parent")
        
        # Test passed
        assert True
        
    except ImportError as e:
        print(f"⚠️  GUI components not available: {e}")
        pytest.skip(f"GUI dependencies not available: {e}")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        assert False, f"Mock parent test failed: {e}"

def test_without_log_message():
    """Test BatchProcessingWidget without log_message method."""
    print("Testing BatchProcessingWidget without log_message method...")
    
    try:
        # Import and create widget
        from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        widget = BatchProcessingWidget()  # Use None parent
        
        # Create mock parent WITHOUT log_message method
        mock_parent = Mock()
        # Explicitly remove log_message if it exists
        if hasattr(mock_parent, 'log_message'):
            delattr(mock_parent, 'log_message')
        widget.main_window = mock_parent  # Set the main_window attribute directly
        
        # Test logging functionality - should not crash and fall back to console
        widget.log_message("Test message without parent log_message")
        
        print("✅ BatchProcessingWidget handled missing log_message gracefully")
        
        # Test passed
        assert True
        
    except ImportError as e:
        print(f"⚠️  GUI components not available: {e}")
        pytest.skip(f"GUI dependencies not available: {e}")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        assert False, f"Missing log_message test failed: {e}"

def main():
    """Run all tests"""
    print("Testing BatchProcessingWidget logging functionality...")
    
    tests = [
        test_import,
        test_with_mock_parent,
        test_without_log_message
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! BatchProcessingWidget logging is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
