#!/usr/bin/env python3
"""
AT3GUI Test Runner
==================

This script runs the AT3GUI test suite to validate the installation.

Usage:
    python run_tests.py
    ./run_tests.py
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """Main test runner function."""
    print("🧪 AT3GUI Test Runner")
    print("=====================")
    print()
    
    # Check if we're in the correct directory
    current_dir = Path.cwd()
    
    if not (current_dir / 'tests').exists():
        print("❌ Error: tests directory not found.")
        print(f"   Make sure you're running this from the AT3GUI root directory.")
        print(f"   Current directory: {current_dir}")
        sys.exit(1)
    
    # Check if we can import pytest
    try:
        import pytest
        print("✅ pytest available")
        use_pytest = True
    except ImportError:
        print("⚠️  pytest not available, running basic tests only")
        print("   To install pytest: pip install pytest")
        use_pytest = False
    
    # Run basic import tests
    print("\n📋 Running basic import tests...")
    try:
        import aretomo3_gui
        print("✅ AT3GUI package import: OK")
        
        from aretomo3_gui.main import AreTomoGUI
        print("✅ Main GUI class import: OK")
        
        from aretomo3_gui.core.logging_config import setup_logging
        print("✅ Logging system import: OK")
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False
    
    # Run comprehensive tests if available
    test_files = [
        'tests/test_comprehensive_imports.py',
        'tests/test_core_functionality.py',
        'tests/test_application_startup.py'
    ]
    
    available_tests = [f for f in test_files if (current_dir / f).exists()]
    
    if not available_tests:
        print("⚠️  No test files found")
        print("✅ Basic import tests passed")
        return True
    
    print(f"\n📋 Found {len(available_tests)} test files")
    
    if use_pytest:
        # Run with pytest
        print("🚀 Running tests with pytest...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                '-v', '--tb=short'
            ] + available_tests, 
            capture_output=False, text=True)
            
            if result.returncode == 0:
                print("✅ All pytest tests passed!")
                return True
            else:
                print("❌ Some pytest tests failed")
                return False
                
        except Exception as e:
            print(f"❌ Error running pytest: {e}")
            return False
    else:
        # Run basic Python tests
        print("🚀 Running basic Python tests...")
        success_count = 0
        
        for test_file in available_tests:
            try:
                print(f"   Testing {test_file}...")
                result = subprocess.run([
                    sys.executable, test_file
                ], capture_output=True, text=True, cwd=current_dir)
                
                if result.returncode == 0:
                    print(f"   ✅ {test_file}: PASSED")
                    success_count += 1
                else:
                    print(f"   ❌ {test_file}: FAILED")
                    if result.stderr:
                        print(f"      Error: {result.stderr[:200]}...")
                        
            except Exception as e:
                print(f"   ❌ {test_file}: ERROR - {e}")
        
        print(f"\n📊 Test Results: {success_count}/{len(available_tests)} passed")
        
        if success_count == len(available_tests):
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Some tests failed")
            return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
