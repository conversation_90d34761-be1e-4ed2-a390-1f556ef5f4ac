#!/usr/bin/env python3
"""
Comprehensive integration test suite for AreTomo3 GUI.
Tests all major components and their interactions.
"""

import pytest
import tempfile
import os
import sys
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Import all major components
from aretomo3_gui.gui.main_window import AreTomoGUI
from aretomo3_gui.gui.widgets.enhanced_spinbox import (
    EnhancedSpinBox, ParameterSpinBoxWidget, create_parameter_widget
)
from aretomo3_gui.utils.mdoc_parser import MDOCParser, extract_tilt_angles_from_mdoc
from aretomo3_gui.core.session_manager import SessionManager
from aretomo3_gui.core.continue_mode_manager import ContinueModeManager
from aretomo3_gui.web.api_server import AreTomo3WebAPI

class TestComprehensiveIntegration:
    """Comprehensive integration tests for AreTomo3 GUI."""

    @pytest.fixture
    def app(self):
        """Create QApplication for testing."""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        app.quit()

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace = {
                'root': Path(temp_dir),
                'input': Path(temp_dir) / 'input',
                'output': Path(temp_dir) / 'output',
                'sessions': Path(temp_dir) / 'sessions',
                'continue_states': Path(temp_dir) / 'continue_states'
            }
            
            # Create directories
            for path in workspace.values():
                if isinstance(path, Path):
                    path.mkdir(parents=True, exist_ok=True)
            
            yield workspace

    @pytest.fixture
    def sample_mdoc_file(self, temp_workspace):
        """Create sample MDOC file for testing."""
        mdoc_content = """
PixelSpacing = 0.15
Voltage = 300
TiltAxis = 85.0
Magnification = 105000

[ZValue = 0]
TiltAngle = -30.0
ExposureDose = 2.5
TargetDefocus = -2.0

[ZValue = 1]
TiltAngle = 0.0
ExposureDose = 2.5
TargetDefocus = -2.0

[ZValue = 2]
TiltAngle = 30.0
ExposureDose = 2.5
TargetDefocus = -2.0
"""
        mdoc_file = temp_workspace['input'] / 'test_series.mdoc'
        with open(mdoc_file, 'w') as f:
            f.write(mdoc_content)
        
        return mdoc_file

    def test_enhanced_spinbox_integration(self, app):
        """Test enhanced spinbox components."""
        # Test basic enhanced spinbox
        spinbox = EnhancedSpinBox()
        spinbox.setRange(0, 100)
        spinbox.setValue(50)
        assert spinbox.value() == 50
        
        # Test parameter widget
        param_widget = ParameterSpinBoxWidget(
            param_name="test_param",
            param_type="float",
            default_value=1.5,
            minimum=0.1,
            maximum=10.0
        )
        assert abs(param_widget.value() - 1.5) < 0.01
        
        # Test factory function
        config = {
            'type': 'int',
            'default': 10,
            'min': 0,
            'max': 100,
            'help': 'Test parameter'
        }
        factory_widget = create_parameter_widget("test_factory", config)
        assert factory_widget.value() == 10

    def test_mdoc_parser_integration(self, sample_mdoc_file):
        """Test MDOC parser functionality."""
        # Test enhanced parser
        parser = MDOCParser()
        result = parser.parse_mdoc_file(str(sample_mdoc_file))
        
        # Check global parameters
        assert abs(result['global_params']['pixel_size'] - 1.5) < 0.01
        assert result['global_params']['voltage'] == 300
        assert result['global_params']['tilt_axis'] == 85.0
        
        # Check tilt series data
        tilt_angles = result['tilt_series']['tilt_angles']
        assert tilt_angles == [-30.0, 0.0, 30.0]
        assert result['tilt_series']['num_images'] == 3
        
        # Test utility function
        angles = extract_tilt_angles_from_mdoc(str(sample_mdoc_file))
        assert angles == [-30.0, 0.0, 30.0]

    def test_session_manager_integration(self, temp_workspace):
        """Test session manager functionality."""
        session_manager = SessionManager(str(temp_workspace['sessions']))
        
        # Create new session
        session = session_manager.create_new_session(
            session_name="Test Session",
            processing_mode="batch",
            input_dir=str(temp_workspace['input']),
            output_dir=str(temp_workspace['output'])
        )
        
        assert session.session_name == "Test Session"
        assert session.processing_mode == "batch"
        
        # Update session
        success = session_manager.update_session(
            processing_status="running",
            progress_percentage=50.0
        )
        assert success
        assert session_manager.current_session.progress_percentage == 50.0
        
        # Get web data
        web_data = session_manager.get_session_for_web()
        assert web_data['session_active'] is True
        assert web_data['progress_percentage'] == 50.0

    def test_continue_mode_manager_integration(self, temp_workspace):
        """Test continue mode manager functionality."""
        continue_manager = ContinueModeManager(str(temp_workspace['continue_states']))
        
        # Test command modification
        original_command = "AreTomo3 -InPrefix /input -PixSize 1.5"
        modified_command = continue_manager.add_continue_parameters(
            original_command,
            str(temp_workspace['output']),
            "test_series"
        )
        
        assert "-Resume 1" in modified_command
        assert "-SaveStack 1" in modified_command
        
        # Test session creation (without actually starting process)
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_process.pid = 12345
            mock_popen.return_value = mock_process
            
            success, session_id = continue_manager.start_processing(
                series_name="test_series",
                command=original_command,
                input_dir=str(temp_workspace['input']),
                output_dir=str(temp_workspace['output'])
            )
            
            assert success
            assert session_id in continue_manager.active_sessions
            
            # Test session status
            status = continue_manager.get_session_status(session_id)
            assert status is not None
            assert status['series_name'] == "test_series"

    def test_main_window_integration(self, app, temp_workspace):
        """Test main window integration."""
        # Mock dependencies to avoid actual GUI creation
        with patch('aretomo3_gui.gui.main_window.AreTomoGUI.__init__', return_value=None):
            # Create mock main window
            main_window = Mock()
            main_window.tab_widget = Mock()
            main_window.tab_widget.count.return_value = 5
            main_window.tab_widget.tabText.side_effect = [
                "Main", "Parameters", "Batch Processing", 
                "Live Processing", "🔬 Napari Viewer"
            ]
            
            # Test tab detection
            viewer_tab_found = False
            for i in range(main_window.tab_widget.count()):
                tab_text = main_window.tab_widget.tabText(i)
                if "Napari Viewer" in tab_text:
                    viewer_tab_found = True
                    break
            
            assert viewer_tab_found

    def test_web_api_integration(self, temp_workspace):
        """Test web API integration."""
        # Mock processor for web API
        mock_processor = Mock()
        mock_processor.is_running = False
        mock_processor.get_stats.return_value = Mock(
            total_files=10,
            processed_files=5,
            failed_files=1,
            avg_processing_time=120.0,
            throughput_per_hour=30.0,
            queue_size=4,
            active_jobs=2
        )
        
        # Create web API instance
        web_api = AreTomo3WebAPI(mock_processor)
        
        # Test session manager integration
        assert hasattr(web_api, 'session_manager')
        assert hasattr(web_api, 'continue_manager')
        
        # Test log entry addition
        web_api.add_log_entry("Test log message", "INFO")
        assert len(web_api.log_entries) == 1
        assert web_api.log_entries[0]['message'] == "Test log message"

    def test_component_interaction_workflow(self, app, temp_workspace, sample_mdoc_file):
        """Test complete workflow with component interactions."""
        # 1. Parse MDOC file
        parser = MDOCParser()
        mdoc_result = parser.parse_mdoc_file(str(sample_mdoc_file))
        
        # 2. Create session with MDOC data
        session_manager = SessionManager(str(temp_workspace['sessions']))
        session = session_manager.create_new_session(
            session_name="Workflow Test",
            processing_mode="batch",
            input_dir=str(temp_workspace['input']),
            output_dir=str(temp_workspace['output'])
        )
        
        # Update session with MDOC parameters
        session_manager.update_session(
            parameters=mdoc_result['global_params'],
            series_data=mdoc_result['tilt_series'],
            total_series=1
        )
        
        # 3. Create enhanced spinbox with MDOC parameters
        pixel_size_widget = ParameterSpinBoxWidget(
            param_name="PixSize",
            param_type="float",
            default_value=mdoc_result['global_params']['pixel_size'],
            minimum=0.1,
            maximum=10.0
        )
        
        assert abs(pixel_size_widget.value() - 1.5) < 0.01
        
        # 4. Set up continue mode
        continue_manager = ContinueModeManager(str(temp_workspace['continue_states']))
        
        # 5. Verify all components work together
        assert session.session_name == "Workflow Test"
        assert len(mdoc_result['tilt_series']['tilt_angles']) == 3
        assert abs(pixel_size_widget.value() - mdoc_result['global_params']['pixel_size']) < 0.01

    def test_error_handling_integration(self, temp_workspace):
        """Test error handling across components."""
        # Test MDOC parser with invalid file
        parser = MDOCParser()
        result = parser.parse_mdoc_file("/nonexistent/file.mdoc")
        assert result['summary']['parsing_success'] is False
        
        # Test session manager with invalid data
        session_manager = SessionManager(str(temp_workspace['sessions']))
        success = session_manager.update_session(invalid_param="invalid")
        # Should handle gracefully without crashing
        
        # Test continue mode manager with invalid session
        continue_manager = ContinueModeManager(str(temp_workspace['continue_states']))
        success = continue_manager.pause_processing("nonexistent_session")
        assert success is False

    def test_performance_integration(self, app, temp_workspace):
        """Test performance of integrated components."""
        import time
        
        # Test session manager performance
        session_manager = SessionManager(str(temp_workspace['sessions']))
        
        start_time = time.time()
        for i in range(10):
            session = session_manager.create_new_session(
                session_name=f"Performance Test {i}",
                processing_mode="batch"
            )
            session_manager.update_session(progress_percentage=i * 10)
        
        elapsed_time = time.time() - start_time
        assert elapsed_time < 5.0  # Should complete in under 5 seconds
        
        # Test MDOC parser performance
        mdoc_content = """
PixelSpacing = 0.15
Voltage = 300
""" + "\n".join([f"""
[ZValue = {i}]
TiltAngle = {i * 3 - 30}
ExposureDose = 2.5
""" for i in range(100)])  # 100 tilt images
        
        large_mdoc_file = temp_workspace['input'] / 'large_series.mdoc'
        with open(large_mdoc_file, 'w') as f:
            f.write(mdoc_content)
        
        parser = MDOCParser()
        start_time = time.time()
        result = parser.parse_mdoc_file(str(large_mdoc_file))
        elapsed_time = time.time() - start_time
        
        assert elapsed_time < 2.0  # Should parse in under 2 seconds
        assert len(result['tilt_series']['tilt_angles']) == 100

    def test_memory_usage_integration(self, app, temp_workspace):
        """Test memory usage of integrated components."""
        import gc
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create multiple components
        components = []
        for i in range(10):
            session_manager = SessionManager(str(temp_workspace['sessions']))
            continue_manager = ContinueModeManager(str(temp_workspace['continue_states']))
            parser = MDOCParser()
            
            components.extend([session_manager, continue_manager, parser])
        
        # Force garbage collection
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for this test)
        assert memory_increase < 100

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
