"""
Comprehensive test suite for AreTomo3 GUI system.
Tests all major components including real-time analysis, session management, 
continue mode, and web interface integration.
"""

import pytest
import tempfile
import shutil
import os
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np

# Import the modules to test
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from aretomo3_gui.core.session_manager import SessionManager, ProcessingSession
from aretomo3_gui.core.continue_mode_manager import ContinueModeManager, ProcessingState
from aretomo3_gui.utils.aretomo3_parser import AreTomo3ResultsParser
from aretomo3_gui.gui.tabs.realtime_analysis_tab import RealTimeAnalysisTab

class TestSessionManager:
    """Test session management functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.session_manager = SessionManager(self.temp_dir)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_create_new_session(self):
        """Test creating a new session."""
        session = self.session_manager.create_new_session(
            "Test Session", "batch", "/input", "/output"
        )
        
        assert session.session_name == "Test Session"
        assert session.processing_mode == "batch"
        assert session.input_directory == "/input"
        assert session.output_directory == "/output"
        assert session.processing_status == "initialized"
        assert self.session_manager.current_session == session
    
    def test_save_and_load_session(self):
        """Test saving and loading sessions."""
        # Create and save session
        session = self.session_manager.create_new_session(
            "Test Session", "live", "/input", "/output"
        )
        session_id = session.session_id
        
        # Update session data
        self.session_manager.update_session(
            processing_status="running",
            progress_percentage=50.0,
            total_series=10
        )
        
        # Load session
        loaded_session = self.session_manager.load_session(session_id)
        
        assert loaded_session is not None
        assert loaded_session.session_name == "Test Session"
        assert loaded_session.processing_status == "running"
        assert loaded_session.progress_percentage == 50.0
        assert loaded_session.total_series == 10
    
    def test_session_for_web(self):
        """Test getting session data for web interface."""
        session = self.session_manager.create_new_session(
            "Web Test", "analysis", "/input", "/output"
        )
        
        web_data = self.session_manager.get_session_for_web()
        
        assert web_data["session_active"] is True
        assert web_data["session_name"] == "Web Test"
        assert web_data["processing_mode"] == "analysis"
        assert "session_id" in web_data
        assert "created_at" in web_data
    
    def test_quality_summary(self):
        """Test quality metrics summary."""
        session = self.session_manager.create_new_session("Quality Test", "batch")
        
        # Add mock quality metrics
        session.quality_metrics = {
            "series1": {"overall_quality": "good"},
            "series2": {"overall_quality": "fair"},
            "series3": {"overall_quality": "poor"}
        }
        
        summary = self.session_manager.get_quality_summary()
        
        assert summary["total"] == 3
        assert summary["good"] == 1
        assert summary["fair"] == 1
        assert summary["poor"] == 1

class TestContinueModeManager:
    """Test continue mode functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.continue_manager = ContinueModeManager(self.temp_dir)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('subprocess.Popen')
    def test_start_processing(self, mock_popen):
        """Test starting continue mode processing."""
        # Mock process
        mock_process = Mock()
        mock_process.pid = 12345
        mock_popen.return_value = mock_process
        
        success, session_id = self.continue_manager.start_processing(
            "test_series", "aretomo3 -input test.mrc", "/input", "/output"
        )
        
        assert success is True
        assert session_id in self.continue_manager.active_sessions
        
        session = self.continue_manager.active_sessions[session_id]
        assert session.series_name == "test_series"
        assert session.state == ProcessingState.RUNNING
        assert session.process_id == 12345
    
    def test_session_status(self):
        """Test getting session status."""
        # Create mock session
        from aretomo3_gui.core.continue_mode_manager import ContinueSession
        session = ContinueSession(
            session_id="test_session",
            input_directory="/input",
            output_directory="/output",
            series_name="test_series",
            command="test_command",
            state=ProcessingState.RUNNING,
            process_id=12345,
            start_time=time.time(),
            pause_time=None,
            resume_time=None,
            completed_frames=10,
            total_frames=100,
            current_step="processing",
            error_message=None
        )
        
        self.continue_manager.active_sessions["test_session"] = session
        
        status = self.continue_manager.get_session_status("test_session")
        
        assert status["session_id"] == "test_session"
        assert status["series_name"] == "test_series"
        assert status["state"] == "running"
        assert status["process_id"] == 12345
        assert status["completed_frames"] == 10
        assert status["total_frames"] == 100
        assert status["can_pause"] is True
        assert status["can_resume"] is False

class TestAreTomo3Parser:
    """Test AreTomo3 results parser."""
    
    def setup_method(self):
        """Set up test environment with mock data."""
        self.temp_dir = tempfile.mkdtemp()
        self.create_mock_aretomo3_data()
        self.parser = AreTomo3ResultsParser(self.temp_dir)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_mock_aretomo3_data(self):
        """Create mock AreTomo3 output files."""
        # Create motion correction file
        motion_data = """0   0  -10.02   1.91     0.00     0.00
1   0  -10.02   1.91    -2.23     3.00
2   0  -10.02   1.91    -4.47     5.95
3   0  -10.02   1.91    -6.77     8.89
4   0  -10.02   1.91    -9.14    11.84"""
        
        with open(os.path.join(self.temp_dir, "test_series_MC_GL.csv"), 'w') as f:
            f.write(motion_data)
        
        # Create CTF file
        ctf_data = """# Columns: #1 micrograph number; #2 - defocus 1 [A]; #3 - defocus 2; #4 - azimuth of astigmatism; #5 - additional phase shift [radian]; #6 - cross correlation; #7 - spacing (in Angstroms) up to which CTF rings were fit successfully; #8 - dfHand
   1   18596.46  17802.71     52.28     0.0000    0.0000  977.9200   -1
   2   31533.69  30187.75     52.16     0.0000    0.0900   13.2151   -1
   3   19341.91  18516.35     47.28     0.0000    0.0149   29.6339   -1
   4   14116.71  13514.17     55.84     0.0000    0.0088    9.4031   -1"""
        
        with open(os.path.join(self.temp_dir, "test_series_CTF.txt"), 'w') as f:
            f.write(ctf_data)
        
        # Create alignment file
        alignment_data = """0  35  -64.01   1.91    0.99  -96.65   -13.12    -1.36
1  34  -61.00   1.91    1.08  -96.65   -13.99    10.05
2  31  -58.01   1.91    1.16  -96.65   -10.66    24.18
3  30  -55.01   1.91    1.24  -96.65    -8.81    60.07"""
        
        with open(os.path.join(self.temp_dir, "test_series_AT_GL.csv"), 'w') as f:
            f.write(alignment_data)
    
    def test_parse_motion_data(self):
        """Test parsing motion correction data."""
        parsed_data = self.parser.parse_all_results()
        
        assert "motion_data" in parsed_data
        assert "test_series" in parsed_data["motion_data"]
        
        motion_data = parsed_data["motion_data"]["test_series"]
        assert len(motion_data["frames"]) == 5
        assert len(motion_data["x_shifts"]) == 5
        assert len(motion_data["y_shifts"]) == 5
        assert len(motion_data["total_motion"]) == 5
        assert motion_data["format"] == "aretomo3_csv"
    
    def test_parse_ctf_data(self):
        """Test parsing CTF data."""
        parsed_data = self.parser.parse_all_results()
        
        assert "ctf_data" in parsed_data
        assert "test_series" in parsed_data["ctf_data"]
        
        ctf_data = parsed_data["ctf_data"]["test_series"]
        assert len(ctf_data["micrograph_numbers"]) == 4
        assert len(ctf_data["defocus1"]) == 4
        assert len(ctf_data["defocus2"]) == 4
        assert ctf_data["format"] == "aretomo3_ctf"
        assert "mean_defocus_value" in ctf_data
        assert "mean_cc" in ctf_data
    
    def test_parse_alignment_data(self):
        """Test parsing alignment data."""
        parsed_data = self.parser.parse_all_results()
        
        assert "alignment_data" in parsed_data
        assert "test_series" in parsed_data["alignment_data"]
        
        alignment_data = parsed_data["alignment_data"]["test_series"]
        assert len(alignment_data["tilt_angles"]) == 4
        assert len(alignment_data["alignment_scores"]) == 4
        assert alignment_data["format"] == "aretomo3_alignment"
        assert "mean_score" in alignment_data
        assert "tilt_range" in alignment_data

class TestRealTimeAnalysisIntegration:
    """Test real-time analysis tab integration."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock Qt application for testing
        with patch('PyQt6.QtWidgets.QWidget'):
            self.analysis_tab = RealTimeAnalysisTab()
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_mdoc_parsing(self):
        """Test MDOC file parsing for tilt angles."""
        # Create mock MDOC file
        mdoc_content = """[ZValue = 0]
TiltAngle = -60.0
ExposureTime = 1.0

[ZValue = 1]
TiltAngle = -58.0
ExposureTime = 1.0

[ZValue = 2]
TiltAngle = -56.0
ExposureTime = 1.0"""
        
        mdoc_file = os.path.join(self.temp_dir, "test_series.mdoc")
        with open(mdoc_file, 'w') as f:
            f.write(mdoc_content)
        
        tilt_angles = self.analysis_tab.parse_mdoc_tilt_angles(self.temp_dir, "test_series")
        
        assert len(tilt_angles) == 3
        assert tilt_angles == [-60.0, -58.0, -56.0]
    
    @patch('matplotlib.pyplot.show')
    def test_plot_generation(self, mock_show):
        """Test plot generation with sample data."""
        # Add mock series data
        self.analysis_tab.series_data = {
            "test_series": {
                "name": "test_series",
                "data": {
                    "motion_data": {
                        "tilt_angles": [-60, -58, -56, -54],
                        "total_motion": [1.2, 0.8, 1.1, 0.9],
                        "x_shifts": [0.5, -0.3, 0.6, -0.2],
                        "y_shifts": [1.1, 0.7, 0.9, 0.8]
                    }
                }
            }
        }
        
        # Test motion plot update
        selected_series = ["test_series"]
        
        # This should not raise an exception
        try:
            self.analysis_tab.update_motion_plots(selected_series)
            plot_success = True
        except Exception as e:
            plot_success = False
            print(f"Plot generation failed: {e}")
        
        assert plot_success is True

class TestSystemIntegration:
    """Test complete system integration."""
    
    def setup_method(self):
        """Set up integrated test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.session_manager = SessionManager(os.path.join(self.temp_dir, "sessions"))
        self.continue_manager = ContinueModeManager(os.path.join(self.temp_dir, "continue"))
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow."""
        # 1. Create session
        session = self.session_manager.create_new_session(
            "Integration Test", "batch", "/input", "/output"
        )
        
        # 2. Update session with processing data
        self.session_manager.update_session(
            processing_status="running",
            total_series=5,
            completed_series=["series1", "series2"],
            progress_percentage=40.0
        )
        
        # 3. Test session data for web interface
        web_data = self.session_manager.get_session_for_web()
        assert web_data["session_active"] is True
        assert web_data["progress_percentage"] == 40.0
        assert web_data["completed_series"] == 2
        
        # 4. Test continue mode integration
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_process.pid = 12345
            mock_popen.return_value = mock_process
            
            success, continue_session_id = self.continue_manager.start_processing(
                "series3", "aretomo3 -input series3.mrc", "/input", "/output"
            )
            
            assert success is True
            
            # 5. Test continue session status
            status = self.continue_manager.get_session_status(continue_session_id)
            assert status["state"] == "running"
            assert status["can_pause"] is True
        
        # 6. Complete the workflow
        self.session_manager.update_session(
            processing_status="completed",
            progress_percentage=100.0,
            completed_series=["series1", "series2", "series3", "series4", "series5"]
        )
        
        final_web_data = self.session_manager.get_session_for_web()
        assert final_web_data["progress_percentage"] == 100.0
        assert final_web_data["completed_series"] == 5

def test_error_handling():
    """Test error handling across the system."""
    # Test session manager with invalid directory
    with pytest.raises(Exception):
        session_manager = SessionManager("/invalid/path/that/cannot/be/created")
    
    # Test continue manager with invalid session
    temp_dir = tempfile.mkdtemp()
    try:
        continue_manager = ContinueModeManager(temp_dir)
        status = continue_manager.get_session_status("nonexistent_session")
        assert status is None
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
