#!/usr/bin/env python3
"""
Test script to verify the batch processing fix for current_batch_index attribute error.
"""

import sys
import os
import pytest
from unittest.mock import Mock, patch

# Add the source directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))


class TestBatchProcessingFix:
    """Test cases for batch processing attribute fixes."""

    def test_batch_processing_attributes_initialization(self):
        """Test that batch processing attributes are properly initialized."""

        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            from PyQt6.QtWidgets import QApplication

            # Create application instance
            app = QApplication.instance() or QApplication([])

            # Create main window instance
            main_window = AreTomoGUI()

            # Test attribute initialization
            assert hasattr(
                main_window,
                'current_batch_index'
            ), "current_batch_index not initialized"
            assert hasattr(main_window, 'batch_queue'), "batch_queue not initialized"
            assert hasattr(
                main_window,
                'batch_workers'
            ), "batch_workers not initialized"
            assert main_window.current_batch_index == 0, f"current_batch_index should be 0, got {main_window.current_batch_index}"
            assert main_window.batch_queue == [], f"batch_queue should be empty list, got {main_window.batch_queue}"
            assert main_window.batch_workers == {}, f"batch_workers should be empty dict, got {main_window.batch_workers}"

        except ImportError:
            pytest.skip("GUI components not available")

    def test_start_next_batch_jobs_empty_queue(self):
        """Test _start_next_batch_item with empty queue."""

        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            from PyQt6.QtWidgets import QApplication

            app = QApplication.instance() or QApplication([])
            main_window = AreTomoGUI()

            # Should handle empty queue gracefully
            main_window._start_next_batch_item()

            # Verify attributes are still intact
            assert hasattr(main_window, 'current_batch_index')
            assert main_window.current_batch_index == 0

        except ImportError:
            pytest.skip("GUI components not available")

    def test_start_next_batch_item_empty_queue(self):
        """Test _start_next_batch_item with empty queue."""

        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            from PyQt6.QtWidgets import QApplication

            app = QApplication.instance() or QApplication([])
            main_window = AreTomoGUI()

            # Should handle empty queue gracefully
            main_window._start_next_batch_item()

            # Verify attributes are still intact
            assert hasattr(main_window, 'current_batch_index')
            assert main_window.current_batch_index == 0

        except ImportError:
            pytest.skip("GUI components not available")

    def test_finish_batch_processing(self):
        """Test _finish_batch_processing method."""

        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            from PyQt6.QtWidgets import QApplication

            app = QApplication.instance() or QApplication([])
            main_window = AreTomoGUI()

            # Set some values to test reset
            main_window.current_batch_index = 5
            main_window.batch_queue = ["job1", "job2"]

            # Call finish batch processing
            main_window._finish_batch_processing()

            # Check that attributes are reset but still exist
            assert hasattr(
                main_window,
                'current_batch_index'
            ), "current_batch_index should not be deleted"
            assert hasattr(
                main_window,
                'batch_queue'
            ), "batch_queue should not be deleted"
            assert main_window.current_batch_index == 0, "current_batch_index should be reset to 0"
            assert main_window.batch_queue == [], "batch_queue should be reset to empty list"

        except ImportError:
            pytest.skip("GUI components not available")

    def test_stop_batch_processing(self):
        """Test stop_batch_processing method."""

        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            from PyQt6.QtWidgets import QApplication

            app = QApplication.instance() or QApplication([])
            main_window = AreTomoGUI()

            # Set some values to test reset
            main_window.current_batch_index = 3
            main_window.batch_queue = ["job1", "job2", "job3"]

            # Call stop batch processing
            main_window.stop_batch_processing()

            # Check that attributes are reset but still exist
            assert hasattr(
                main_window,
                'current_batch_index'
            ), "current_batch_index should not be deleted"
            assert hasattr(
                main_window,
                'batch_queue'
            ), "batch_queue should not be deleted"
            assert main_window.current_batch_index == 0, "current_batch_index should be reset to 0"
            assert main_window.batch_queue == [], "batch_queue should be reset to empty list"

        except ImportError:
            pytest.skip("GUI components not available")

    def test_batch_processing_with_mock_gui(self):
        """Test batch processing with mocked GUI components."""

        # Create a mock main window
        mock_window = Mock()
        mock_window.current_batch_index = 0
        mock_window.batch_queue = []
        mock_window.batch_workers = {}

        # Test that mock has required attributes
        assert hasattr(mock_window, 'current_batch_index')
        assert hasattr(mock_window, 'batch_queue')
        assert hasattr(mock_window, 'batch_workers')

        # Test attribute manipulation
        mock_window.current_batch_index = 2
        assert mock_window.current_batch_index == 2

        mock_window.batch_queue = ["job1", "job2"]
        assert len(mock_window.batch_queue) == 2


def test_batch_processing_verification_standalone():
    """Standalone test function for command line usage."""

    print("🧪 Testing Batch Processing Implementation")
    print("=" * 50)

    try:
        # Test core imports
        import aretomo3_gui
        print("   ✅ Core aretomo3_gui module imported")

        # Test if GUI can be imported (may fail in headless environments)
        try:
            from aretomo3_gui.gui.main_window import AreTomoGUI
            print("   ✅ Main window GUI imported")
            gui_available = True
        except ImportError:
            print("   ⚠️  GUI not available (headless environment)")
            gui_available = False

        if gui_available:
            from PyQt6.QtWidgets import QApplication

            # Create application
            app = QApplication.instance() or QApplication([])

            # Test main window creation
            main_window = AreTomoGUI()

            # Test attributes
            assert hasattr(main_window, 'current_batch_index')
            assert hasattr(main_window, 'batch_queue')
            assert hasattr(main_window, 'batch_workers')
            print("   ✅ All required attributes exist")

            # Test method calls
            main_window._start_next_batch_item()
            main_window._finish_batch_processing()
            main_window.stop_batch_processing()
            print("   ✅ All batch processing methods work")

        print("\n✅ All batch processing verification tests passed!")
        # Test passed successfully

    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        # Use assertion for test failure
        assert False, f"Batch processing verification failed: {e}"
if __name__ == "__main__":
    print("AT3Gui Batch Processing Fix Verification")
    print("=" * 50)

    try:
        test_batch_processing_verification_standalone()
        print("\n✅ Verification completed successfully!")
        import sys
        sys.exit(0)
    except Exception:
        print("\n❌ Verification failed!")
        import sys
        sys.exit(1)


# Marks for test categorization
pytestmark = [
    pytest.mark.unit,
    pytest.mark.gui,
]
