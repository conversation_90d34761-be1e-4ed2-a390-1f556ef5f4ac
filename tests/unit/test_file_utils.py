"""
Unit tests for file utilities.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

from aretomo3_gui.utils import file_utils
from aretomo3_gui.utils import parse_mdoc  # Use available utilities


class TestFileUtils:
    """Test cases for file utility functions."""

    def test_get_file_info_nonexistent(self):
        """Test get_file_info with nonexistent file."""
        info = file_utils.get_file_info("/nonexistent/file.txt")
        assert info is None

    def test_get_file_info_regular_file(self):
        """Test get_file_info with regular file."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(b"test content")
            tmp_path = tmp.name

        try:
            info = file_utils.get_file_info(tmp_path)
            assert isinstance(info, dict)
            assert 'filename' in info
            assert 'file_size' in info
            assert 'file_type' in info
            assert info['filename'] == os.path.basename(tmp_path)
            assert info['file_size'] > 0
        finally:
            os.unlink(tmp_path)

    @pytest.mark.parametrize("extension,expected_type", [
        (".mrc", "mrc"),
        (".eer", "eer"),
        (".tif", "tiff"),
        (".tiff", "tiff"),
        (".dm4", "dm4"),
        (".txt", "unknown"),
        ("", "unknown"),
    ])
    def test_get_file_type(self, extension, expected_type):
        """Test file type detection."""
        with tempfile.NamedTemporaryFile(suffix=extension, delete=False) as tmp:
            tmp_path = tmp.name

        try:
            file_type = file_utils.get_file_type(tmp_path)
            assert file_type == expected_type
        finally:
            os.unlink(tmp_path)

    def test_is_supported_format(self):
        """Test supported format detection."""
        assert file_utils.is_supported_format("test.eer") is True
        assert file_utils.is_supported_format("test.mrc") is True
        assert file_utils.is_supported_format("test.tif") is True
        assert file_utils.is_supported_format("test.tiff") is True
        assert file_utils.is_supported_format("test.dm4") is True
        assert file_utils.is_supported_format("test.txt") is False
        assert file_utils.is_supported_format("test") is False

    def test_get_directory_contents(self):
        """Test directory contents listing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create some test files
            (Path(tmpdir) / "test1.eer").touch()
            (Path(tmpdir) / "test2.mrc").touch()
            (Path(tmpdir) / "test3.txt").touch()
            (Path(tmpdir) / "subdir").mkdir()

            contents = file_utils.get_directory_contents(tmpdir)
            assert isinstance(contents, list)

            # Should contain the files we created
            filenames = [item['name'] for item in contents if item['type'] == 'file']
            assert "test1.eer" in filenames
            assert "test2.mrc" in filenames
            assert "test3.txt" in filenames

            # Should contain the subdirectory
            dirnames = [item['name'] for item in contents if item['type'] == 'directory']
            assert "subdir" in dirnames

    def test_filter_supported_files(self):
        """Test filtering of supported files."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Create test files
            (Path(tmpdir) / "test1.eer").touch()
            (Path(tmpdir) / "test2.mrc").touch()
            (Path(tmpdir) / "test3.txt").touch()
            (Path(tmpdir) / "test4.tif").touch()

            supported_files = file_utils.filter_supported_files(tmpdir)
            assert isinstance(supported_files, list)

            # Should only contain supported formats
            basenames = [os.path.basename(f) for f in supported_files]
            assert "test1.eer" in basenames
            assert "test2.mrc" in basenames
            assert "test4.tif" in basenames
            assert "test3.txt" not in basenames


class TestPathUtils:
    """Test cases for path utility functions."""

    def test_normalize_path(self):
        """Test path normalization."""
        # Test with different path formats
        assert file_utils.normalize_path("./test") == os.path.abspath("test")
        assert file_utils.normalize_path("~/test") == os.path.expanduser("~/test")
        assert file_utils.normalize_path("/test/path") == "/test/path"

    def test_get_relative_path(self):
        """Test relative path calculation."""
        base = "/home/<USER>/project"
        target = "/home/<USER>/project/data/file.eer"
        expected = "data/file.eer"

        result = file_utils.get_relative_path(target, base)
        assert result == expected

    def test_ensure_directory_exists(self):
        """Test directory creation."""
        with tempfile.TemporaryDirectory() as tmpdir:
            test_dir = os.path.join(tmpdir, "new", "nested", "directory")

            # Should not exist initially
            assert not os.path.exists(test_dir)

            # Create it
            file_utils.ensure_directory_exists(test_dir)

            # Should exist now
            assert os.path.exists(test_dir)
            assert os.path.isdir(test_dir)


class TestFileValidation:
    """Test cases for file validation functions."""

    def test_validate_file_permissions(self):
        """Test file permission validation."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp_path = tmp.name

        try:
            # File should be readable by default
            assert file_utils.validate_file_permissions(tmp_path) is True

            # Test with nonexistent file
            assert file_utils.validate_file_permissions("/nonexistent") is False

        finally:
            os.unlink(tmp_path)

    def test_check_disk_space(self):
        """Test disk space checking."""
        # Test with current directory (should have some space)
        space = file_utils.check_disk_space(".")
        assert isinstance(space, dict)
        assert 'free' in space
        assert 'total' in space
        assert space['free'] >= 0
        assert space['total'] > 0

    def test_estimate_processing_space(self):
        """Test processing space estimation."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(b"x" * 1000)  # 1KB file
            tmp_path = tmp.name

        try:
            estimated = file_utils.estimate_processing_space(tmp_path)
            assert isinstance(estimated, int)
            assert estimated > 1000  # Should be larger than original
        finally:
            os.unlink(tmp_path)


# Test fixtures
@pytest.fixture
def sample_directory(tmp_path):
    """Create a sample directory with various file types."""
    # Create files
    (tmp_path / "data1.eer").write_text("eer data")
    (tmp_path / "data2.mrc").write_text("mrc data")
    (tmp_path / "data3.tif").write_text("tiff data")
    (tmp_path / "readme.txt").write_text("readme")

    # Create subdirectory
    subdir = tmp_path / "subdir"
    subdir.mkdir()
    (subdir / "nested.eer").write_text("nested eer")

    return tmp_path


@pytest.fixture
def large_file(tmp_path):
    """Create a large file for testing."""
    large_file = tmp_path / "large.eer"
    with open(large_file, 'wb') as f:
        f.write(b"x" * (10 * 1024 * 1024))  # 10MB
    return large_file


class TestFileUtilsIntegration:
    """Integration tests for file utilities."""

    def test_directory_analysis(self, sample_directory):
        """Test comprehensive directory analysis."""
        analysis = file_utils.analyze_directory(sample_directory)

        assert isinstance(analysis, dict)
        assert 'total_files' in analysis
        assert 'supported_files' in analysis
        assert 'total_size' in analysis
        assert 'file_types' in analysis

        # Check counts
        assert analysis['total_files'] >= 4
        assert analysis['supported_files'] >= 3  # eer, mrc, tif

        # Check file types
        assert 'eer' in analysis['file_types']
        assert 'mrc' in analysis['file_types']
        assert 'tiff' in analysis['file_types']

    def test_large_file_handling(self, large_file):
        """Test handling of large files."""
        info = file_utils.get_file_info(str(large_file))

        assert info is not None
        assert info['file_size'] >= 10 * 1024 * 1024  # At least 10MB
        assert info['file_type'] == 'eer'

        # Test space estimation
        estimated = file_utils.estimate_processing_space(str(large_file))
        assert estimated > info['file_size']  # Should be larger

    def test_batch_file_operations(self, sample_directory):
        """Test batch operations on multiple files."""
        # Get all supported files
        supported = file_utils.filter_supported_files(str(sample_directory))
        assert len(supported) >= 3

        # Validate all files
        valid_files = []
        for file_path in supported:
            if file_utils.validate_file_permissions(file_path):
                valid_files.append(file_path)

        assert len(valid_files) == len(supported)  # All should be valid

        # Get info for all files
        file_infos = []
        for file_path in valid_files:
            info = file_utils.get_file_info(file_path)
            if info:
                file_infos.append(info)

        assert len(file_infos) == len(valid_files)


# Marks for test categorization
pytestmark = [
    pytest.mark.unit,
    pytest.mark.utils,
]
