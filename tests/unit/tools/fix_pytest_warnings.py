#!/usr/bin/env python3
"""
Fix pytest warnings by removing return statements from test functions.
Pytest expects test functions to return None, not True/False values.
"""

import os
import re
from pathlib import Path

def fix_test_function_returns(file_path):
    """Fix return statements in test functions to use assertions instead."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to find test functions with return True
        patterns_to_fix = [
            # Return True at end of function
            (r'(\s+)return True\s*$', r'\1# Test passed'),
            # Return False with pytest.fail
            (r'(\s+)return False\s*$', r'\1pytest.fail("Test failed")'),
            # Return 0 (success)
            (r'(\s+)return 0\s*$', r'\1# Test passed'),
            # Return 1 (failure)  
            (r'(\s+)return 1\s*$', r'\1pytest.fail("Test failed")'),
            # Conditional returns
            (r'return True  # Dependencies are available', r'assert True  # Dependencies are available'),
            (r'return success', r'assert success'),
        ]
        
        # Apply fixes
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Special case: fix main function returns in standalone scripts
        if '__name__ == "__main__"' in content:
            # Fix return statements in main() functions
            content = re.sub(r'(\s+)return (\d+)\s*$', r'\1sys.exit(\2)', content, flags=re.MULTILINE)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed: {file_path}")
            return True
        else:
            print(f"ℹ️  No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def find_test_files(base_dir):
    """Find all test files in the project."""
    test_files = []
    test_dir = Path(base_dir) / 'tests'
    
    if test_dir.exists():
        # Find all Python files in tests directory
        for test_file in test_dir.rglob('*.py'):
            if test_file.name.startswith('test_') or test_file.name.endswith('_test.py'):
                test_files.append(test_file)
    
    return test_files

def main():
    """Main function to fix all test files."""
    print("🔧 Fixing Pytest Warnings in Test Files")
    print("=" * 50)
    
    # Get project directory
    project_dir = Path(__file__).parent
    
    # Find all test files
    test_files = find_test_files(project_dir)
    
    if not test_files:
        print("No test files found!")
        return 1
    
    print(f"Found {len(test_files)} test files to check...")
    
    fixed_count = 0
    total_count = len(test_files)
    
    for test_file in test_files:
        if fix_test_function_returns(test_file):
            fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total files checked: {total_count}")
    print(f"   Files fixed: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print(f"\n✅ Fixed pytest warnings in {fixed_count} files!")
        print("Now run 'pytest' to verify the warnings are gone.")
    else:
        print("\n✅ No fixes needed - all test functions already return None!")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)