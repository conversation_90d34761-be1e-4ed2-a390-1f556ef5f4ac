#!/usr/bin/env python3
"""
Test suite for enhanced MDOC parser.
"""

import pytest
import tempfile
import os
from pathlib import Path

from src.aretomo3_gui.utils.mdoc_parser import (
    MDOCParser, parse_mdoc, extract_tilt_angles_from_mdoc, get_mdoc_summary
)

class TestEnhancedMDOCParser:
    """Test enhanced MDOC parser functionality."""

    @pytest.fixture
    def sample_mdoc_content(self):
        """Create sample MDOC file content for testing."""
        return """
# Sample MDOC file for testing
PixelSpacing = 0.15
Voltage = 300
SphericalAberration = 2.7
TiltAxis = 85.5
Magnification = 105000
ImageFile = test_series.mrc
DataMode = 1
ImageSize = 4096 4096
Binning = 1

[ZValue = 0]
TiltAngle = -30.0
StagePosition = 10.5 -5.2
StageZ = 15.3
ExposureDose = 2.5
TargetDefocus = -2.0
ExposureTime = 1.0
DateTime = 2024-01-15 14:30:00
SubFramePath = frames/frame_000.eer
NumSubFrames = 40

[ZValue = 1]
TiltAngle = -20.0
StagePosition = 10.6 -5.1
StageZ = 15.4
ExposureDose = 2.5
TargetDefocus = -2.1
ExposureTime = 1.0
DateTime = 2024-01-15 14:31:00
SubFramePath = frames/frame_001.eer
NumSubFrames = 40

[ZValue = 2]
TiltAngle = 0.0
StagePosition = 10.7 -5.0
StageZ = 15.5
ExposureDose = 2.5
TargetDefocus = -2.0
ExposureTime = 1.0
DateTime = 2024-01-15 14:32:00
SubFramePath = frames/frame_002.eer
NumSubFrames = 40

[ZValue = 3]
TiltAngle = 20.0
StagePosition = 10.8 -4.9
StageZ = 15.6
ExposureDose = 2.5
TargetDefocus = -1.9
ExposureTime = 1.0
DateTime = 2024-01-15 14:33:00
SubFramePath = frames/frame_003.eer
NumSubFrames = 40

[ZValue = 4]
TiltAngle = 30.0
StagePosition = 10.9 -4.8
StageZ = 15.7
ExposureDose = 2.5
TargetDefocus = -1.8
ExposureTime = 1.0
DateTime = 2024-01-15 14:34:00
SubFramePath = frames/frame_004.eer
NumSubFrames = 40
"""

    @pytest.fixture
    def temp_mdoc_file(self, sample_mdoc_content):
        """Create temporary MDOC file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.mdoc', delete=False) as f:
            f.write(sample_mdoc_content)
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)

    def test_mdoc_parser_creation(self):
        """Test MDOC parser creation."""
        parser = MDOCParser()
        assert parser is not None
        assert hasattr(parser, 'global_params')
        assert hasattr(parser, 'sections')
        assert hasattr(parser, 'tilt_series_data')

    def test_parse_global_parameters(self, temp_mdoc_file):
        """Test parsing of global parameters."""
        parser = MDOCParser()
        result = parser.parse_mdoc_file(temp_mdoc_file)
        
        global_params = result['global_params']
        
        # Check key parameters
        assert abs(global_params['pixel_size'] - 1.5) < 0.01  # 0.15 nm * 10 = 1.5 Å
        assert global_params['voltage'] == 300
        assert global_params['cs'] == 2.7
        assert global_params['tilt_axis'] == 85.5
        assert global_params['magnification'] == 105000
        assert global_params['image_file'] == 'test_series.mrc'
        assert global_params['data_mode'] == 1
        assert global_params['image_size'] == (4096, 4096)
        assert global_params['binning'] == 1

    def test_parse_sections(self, temp_mdoc_file):
        """Test parsing of individual sections."""
        parser = MDOCParser()
        result = parser.parse_mdoc_file(temp_mdoc_file)
        
        sections = result['sections']
        
        # Should have 5 sections (ZValue 0-4)
        assert len(sections) == 5
        
        # Check first section
        first_section = sections[0]
        assert first_section['z_value'] == 0
        assert first_section['tilt_angle'] == -30.0
        assert first_section['stage_position'] == (10.5, -5.2)
        assert first_section['stage_z'] == 15.3
        assert first_section['exposure_dose'] == 2.5
        assert first_section['defocus'] == -2.0
        assert first_section['exposure_time'] == 1.0
        assert 'DateTime' in first_section['datetime']
        assert first_section['sub_frame_path'] == 'frames/frame_000.eer'
        assert first_section['num_sub_frames'] == 40

    def test_extract_tilt_series_data(self, temp_mdoc_file):
        """Test extraction of tilt series data."""
        parser = MDOCParser()
        result = parser.parse_mdoc_file(temp_mdoc_file)
        
        tilt_series = result['tilt_series']
        
        # Check tilt angles
        expected_angles = [-30.0, -20.0, 0.0, 20.0, 30.0]
        assert tilt_series['tilt_angles'] == expected_angles
        
        # Check other data
        assert tilt_series['num_images'] == 5
        assert tilt_series['tilt_range'] == (-30.0, 30.0)
        assert tilt_series['total_dose'] == 12.5  # 5 * 2.5
        
        # Check exposure doses
        assert len(tilt_series['exposure_doses']) == 5
        assert all(dose == 2.5 for dose in tilt_series['exposure_doses'])
        
        # Check defocus values
        expected_defocus = [-2.0, -2.1, -2.0, -1.9, -1.8]
        assert tilt_series['defocus_values'] == expected_defocus

    def test_generate_summary(self, temp_mdoc_file):
        """Test summary generation."""
        parser = MDOCParser()
        result = parser.parse_mdoc_file(temp_mdoc_file)
        
        summary = result['summary']
        
        assert summary['num_sections'] == 5
        assert summary['has_tilt_series'] is True
        assert summary['global_params_count'] > 0
        assert summary['parsing_success'] is True
        assert summary['tilt_range'] == (-30.0, 30.0)
        assert summary['num_tilts'] == 5
        assert summary['total_dose'] == 12.5
        assert summary['average_dose_per_tilt'] == 2.5

    def test_legacy_parse_mdoc_function(self, temp_mdoc_file):
        """Test legacy parse_mdoc function for backward compatibility."""
        result = parse_mdoc(temp_mdoc_file)
        
        # Check legacy format
        assert abs(result['pixel_size'] - 1.5) < 0.01
        assert result['voltage'] == 300
        assert result['cs'] == 2.7
        assert result['total_dose'] == 12.5
        assert result['tilt_axis'] == 85.5
        assert result['tilt_angles'] == [-30.0, -20.0, 0.0, 20.0, 30.0]
        assert result['num_images'] == 5
        assert result['tilt_range'] == (-30.0, 30.0)

    def test_extract_tilt_angles_function(self, temp_mdoc_file):
        """Test extract_tilt_angles_from_mdoc function."""
        angles = extract_tilt_angles_from_mdoc(temp_mdoc_file)
        
        expected_angles = [-30.0, -20.0, 0.0, 20.0, 30.0]
        assert angles == expected_angles

    def test_get_mdoc_summary_function(self, temp_mdoc_file):
        """Test get_mdoc_summary function."""
        summary = get_mdoc_summary(temp_mdoc_file)
        
        assert summary['num_sections'] == 5
        assert summary['has_tilt_series'] is True
        assert summary['parsing_success'] is True
        assert summary['num_tilts'] == 5

    def test_error_handling_nonexistent_file(self):
        """Test error handling for nonexistent file."""
        parser = MDOCParser()
        result = parser.parse_mdoc_file('/nonexistent/file.mdoc')
        
        assert result['summary']['parsing_success'] is False
        assert 'error_message' in result['summary']
        assert len(result['sections']) == 0
        assert len(result['global_params']) == 0

    def test_error_handling_invalid_content(self):
        """Test error handling for invalid MDOC content."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.mdoc', delete=False) as f:
            f.write("This is not a valid MDOC file content")
            temp_path = f.name
        
        try:
            parser = MDOCParser()
            result = parser.parse_mdoc_file(temp_path)
            
            # Should still succeed but with empty data
            assert result['summary']['parsing_success'] is True
            assert len(result['sections']) == 0
            assert result['tilt_series']['num_images'] == 0
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_parser_reset(self):
        """Test parser reset functionality."""
        parser = MDOCParser()
        
        # Add some dummy data
        parser.global_params = {'test': 'value'}
        parser.sections = [{'test': 'section'}]
        parser.tilt_series_data = {'test': 'data'}
        
        # Reset
        parser.reset()
        
        # Check that everything is cleared
        assert len(parser.global_params) == 0
        assert len(parser.sections) == 0
        assert len(parser.tilt_series_data) == 0

    def test_complex_mdoc_parsing(self):
        """Test parsing of more complex MDOC content."""
        complex_content = """
PixelSpacing = 0.2
Voltage = 200
TiltAxis = 90.0
ImageShift = 1.5 -2.3
BeamShift = 0.1 0.2

[ZValue = 0]
TiltAngle = -45.0
StagePosition = 5.0 10.0
ImageShift = 1.6 -2.2
BeamShift = 0.11 0.21
ExposureDose = 3.0
AccumulatedDose = 3.0
FrameDoseRate = 5.0
CameraIndex = 1
MagIndex = 20

[ZValue = 1]
TiltAngle = 45.0
StagePosition = 5.1 10.1
ImageShift = 1.7 -2.1
BeamShift = 0.12 0.22
ExposureDose = 3.0
AccumulatedDose = 6.0
FrameDoseRate = 5.0
CameraIndex = 1
MagIndex = 20
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.mdoc', delete=False) as f:
            f.write(complex_content)
            temp_path = f.name
        
        try:
            parser = MDOCParser()
            result = parser.parse_mdoc_file(temp_path)
            
            # Check global parameters
            global_params = result['global_params']
            assert abs(global_params['pixel_size'] - 2.0) < 0.01  # 0.2 nm * 10 = 2.0 Å
            assert global_params['voltage'] == 200
            assert global_params['tilt_axis'] == 90.0
            
            # Check sections
            sections = result['sections']
            assert len(sections) == 2
            
            # Check first section details
            first_section = sections[0]
            assert first_section['tilt_angle'] == -45.0
            assert first_section['stage_position'] == (5.0, 10.0)
            assert first_section['image_shift'] == (1.6, -2.2)
            assert first_section['beam_shift'] == (0.11, 0.21)
            assert first_section['exposure_dose'] == 3.0
            assert first_section['accumulated_dose'] == 3.0
            assert first_section['camera_index'] == 1
            assert first_section['mag_index'] == 20
            
            # Check tilt series data
            tilt_series = result['tilt_series']
            assert tilt_series['tilt_angles'] == [-45.0, 45.0]
            assert tilt_series['tilt_range'] == (-45.0, 45.0)
            assert tilt_series['total_dose'] == 6.0
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

if __name__ == "__main__":
    pytest.main([__file__])
